<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = '';
$customer = null;

// Get customer data
if ($id > 0) {
    $result = db_query("SELECT * FROM customers WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $customer = db_fetch_assoc($result);
    } else {
        $error = 'Cliente não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}

// Get customer quotes
$quotes = [];
if ($id > 0) {
    $result = db_query("
        SELECT q.*, COUNT(qi.id) as item_count
        FROM quotes q
        LEFT JOIN quote_items qi ON q.id = qi.quote_id
        WHERE q.customer_id = $id
        GROUP BY q.id
        ORDER BY q.quote_date DESC
    ");

    if ($result && db_num_rows($result) > 0) {
        $quotes = db_fetch_all($result);
    }
}

// Get customer invoices
$invoices = [];
if ($id > 0) {
    $result = db_query("
        SELECT i.*, COUNT(ii.id) as item_count
        FROM invoices i
        LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
        WHERE i.customer_id = $id
        GROUP BY i.id
        ORDER BY i.invoice_date DESC
    ");

    if ($result && db_num_rows($result) > 0) {
        $invoices = db_fetch_all($result);
    }
}

// Get customer service calls
$service_calls = [];
if ($id > 0) {
    try {
        // Check if service_calls table exists
        $table_exists = false;
        $check_table = db_query("SHOW TABLES LIKE 'service_calls'");
        if ($check_table && db_num_rows($check_table) > 0) {
            $table_exists = true;
        }

        if ($table_exists) {
            $result = db_query("
                SELECT * FROM service_calls
                WHERE customer_id = $id
                ORDER BY scheduled_date DESC
            ");

            if ($result && db_num_rows($result) > 0) {
                $service_calls = db_fetch_all($result);
            }
        } else {
            // Criar a tabela service_calls
            $create_table_sql = "CREATE TABLE IF NOT EXISTS `service_calls` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `customer_id` int(11) NOT NULL,
                `title` varchar(100) NOT NULL,
                `description` text DEFAULT NULL,
                `scheduled_date` datetime NOT NULL,
                `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
                `service_history` text DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `customer_id` (`customer_id`),
                CONSTRAINT `service_calls_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

            if (db_query($create_table_sql)) {
                error_log("Tabela service_calls criada com sucesso na página customer_view.php");
            } else {
                error_log("Erro ao criar tabela service_calls na página customer_view.php: " . db_error());
            }
        }
    } catch (Exception $e) {
        error_log("Erro ao buscar chamados de atendimento: " . $e->getMessage());
    }
}

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// Função para traduzir o status
function translate_status($status) {
    $translations = [
        'pending' => 'Pendente',
        'in_progress' => 'Em Andamento',
        'completed' => 'Concluído',
        'cancelled' => 'Cancelado'
    ];

    return isset($translations[$status]) ? $translations[$status] : $status;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Cliente - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Estilos para a página de visualização */
        .customer-details {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .detail-label {
            font-weight: bold;
            width: 150px;
            flex-shrink: 0;
        }

        .detail-value {
            flex: 1;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn i {
            font-size: 0.9em;
        }

        .card {
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .card-header {
            padding: 12px 15px;
            background-color: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-body {
            padding: 15px;
        }

        .card-title {
            margin: 0;
            font-size: 1.3rem;
        }

        .customer-icon {
            color: #4a6cf7;
            margin-right: 5px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th, .data-table td {
            padding: 8px 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .data-table th {
            background-color: #f8f9fa;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-in_progress {
            background-color: #b8daff;
            color: #004085;
        }

        .status-completed {
            background-color: #c3e6cb;
            color: #155724;
        }

        .status-cancelled {
            background-color: #f5c6cb;
            color: #721c24;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-expired {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .status-partial {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-unpaid {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }

        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }

        .tab-button {
            padding: 10px 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }

        .tab-button.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <p><a href="customers.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Clientes</a></p>
            <?php elseif ($customer): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title"><i class="fas fa-user customer-icon"></i> <?php echo htmlspecialchars($customer['name']); ?></h1>
                        <div class="action-buttons">
                            <a href="customers.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar</a>
                            <a href="customer_edit.php?id=<?php echo $id; ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Editar</a>
                            <a href="service_call_create.php?customer_id=<?php echo $id; ?>" class="btn btn-info"><i class="fas fa-headset"></i> Novo Chamado</a>
                            <a href="quote_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-file-invoice"></i> Novo Orçamento</a>
                            <a href="invoice_create.php?customer_id=<?php echo $id; ?>" class="btn"><i class="fas fa-file-invoice-dollar"></i> Nova Fatura</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="customer-details">
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-user"></i> Nome:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['name']); ?></div>
                            </div>

                            <?php if (!empty($customer['email'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-envelope"></i> Email:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['email']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['phone'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-phone"></i> Telefone:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['phone']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['address'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-map-marker-alt"></i> Endereço:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['address']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['city'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-city"></i> Cidade:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['city']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['state'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-map"></i> Estado:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['state']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['zip'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-mailbox"></i> CEP:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($customer['zip']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($customer['notes'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-sticky-note"></i> Observações:</div>
                                <div class="detail-value"><?php echo nl2br(htmlspecialchars($customer['notes'])); ?></div>
                            </div>
                            <?php endif; ?>

                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-calendar-alt"></i> Data de Cadastro:</div>
                                <div class="detail-value"><?php echo date('d/m/Y H:i', strtotime($customer['created_at'])); ?></div>
                            </div>

                            <?php if ($customer['updated_at'] != $customer['created_at']): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-edit"></i> Última Atualização:</div>
                                <div class="detail-value"><?php echo date('d/m/Y H:i', strtotime($customer['updated_at'])); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="tab-container">
                            <div class="tab-buttons">
                                <div class="tab-button active" data-tab="quotes"><i class="fas fa-file-invoice"></i> Orçamentos (<?php echo count($quotes); ?>)</div>
                                <div class="tab-button" data-tab="invoices"><i class="fas fa-file-invoice-dollar"></i> Faturas (<?php echo count($invoices); ?>)</div>
                                <div class="tab-button" data-tab="service-calls"><i class="fas fa-headset"></i> Chamados (<?php echo count($service_calls); ?>)</div>
                            </div>

                            <div class="tab-content active" id="quotes-tab">
                                <?php if (empty($quotes)): ?>
                                    <p>Nenhum orçamento encontrado para este cliente.</p>
                                    <a href="quote_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Orçamento</a>
                                <?php else: ?>
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Número</th>
                                                <th>Data</th>
                                                <th>Total</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($quotes as $quote): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($quote['quote_number']); ?></td>
                                                    <td><?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></td>
                                                    <td>R$ <?php echo number_format($quote['total'], 2, ',', '.'); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_class = 'status-' . $quote['status'];
                                                        $status_text = '';
                                                        switch ($quote['status']) {
                                                            case 'pending':
                                                                $status_text = 'Pendente';
                                                                break;
                                                            case 'approved':
                                                                $status_text = 'Aprovado';
                                                                break;
                                                            case 'rejected':
                                                                $status_text = 'Rejeitado';
                                                                break;
                                                            case 'expired':
                                                                $status_text = 'Expirado';
                                                                break;
                                                            default:
                                                                $status_text = ucfirst($quote['status']);
                                                        }
                                                        ?>
                                                        <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="quote_view.php?id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-info" title="Visualizar"><i class="fas fa-eye"></i></a>
                                                            <a href="quote_edit.php?id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-secondary" title="Editar"><i class="fas fa-edit"></i></a>
                                                            <?php if ($quote['status'] === 'pending' || $quote['status'] === 'approved'): ?>
                                                                <a href="invoice_create.php?quote_id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-success" title="Gerar Fatura"><i class="fas fa-file-invoice-dollar"></i></a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <a href="quote_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Orçamento</a>
                                <?php endif; ?>
                            </div>

                            <div class="tab-content" id="invoices-tab">
                                <?php if (empty($invoices)): ?>
                                    <p>Nenhuma fatura encontrada para este cliente.</p>
                                    <a href="invoice_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Fatura</a>
                                <?php else: ?>
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>Número</th>
                                                <th>Data</th>
                                                <th>Vencimento</th>
                                                <th>Total</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($invoices as $invoice): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                                    <td><?php echo date('d/m/Y', strtotime($invoice['invoice_date'])); ?></td>
                                                    <td><?php echo !empty($invoice['due_date']) ? date('d/m/Y', strtotime($invoice['due_date'])) : '-'; ?></td>
                                                    <td>R$ <?php echo number_format($invoice['total'], 2, ',', '.'); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_class = 'status-' . $invoice['status'];
                                                        $status_text = '';
                                                        switch ($invoice['status']) {
                                                            case 'pending':
                                                                $status_text = 'Pendente';
                                                                break;
                                                            case 'paid':
                                                                $status_text = 'Pago';
                                                                break;
                                                            case 'partial':
                                                                $status_text = 'Parcial';
                                                                break;
                                                            case 'cancelled':
                                                                $status_text = 'Cancelado';
                                                                break;
                                                            case 'overdue':
                                                                $status_text = 'Vencido';
                                                                break;
                                                            default:
                                                                $status_text = ucfirst($invoice['status']);
                                                        }
                                                        ?>
                                                        <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="generate_invoice_pdf.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-info" title="Visualizar" target="_blank"><i class="fas fa-eye"></i></a>
                                                            <a href="invoice_edit.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-secondary" title="Editar"><i class="fas fa-edit"></i></a>
                                                            <a href="invoice_print.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-primary" title="Imprimir" target="_blank"><i class="fas fa-print"></i></a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <a href="invoice_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Fatura</a>
                                <?php endif; ?>
                            </div>

                            <div class="tab-content" id="service-calls-tab">
                                <?php if (empty($service_calls)): ?>
                                    <p>Nenhum chamado de atendimento encontrado para este cliente.</p>
                                    <a href="service_call_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Chamado</a>
                                <?php else: ?>
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Título</th>
                                                <th>Data Agendada</th>
                                                <th>Status</th>
                                                <th>Ações</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($service_calls as $call): ?>
                                                <tr>
                                                    <td><?php echo $call['id']; ?></td>
                                                    <td><?php echo htmlspecialchars($call['title']); ?></td>
                                                    <td><?php echo format_date($call['scheduled_date']); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $call['status']; ?>">
                                                            <?php echo translate_status($call['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="service_call_view.php?id=<?php echo $call['id']; ?>" class="btn btn-sm btn-info" title="Visualizar"><i class="fas fa-eye"></i></a>
                                                            <a href="service_call_edit.php?id=<?php echo $call['id']; ?>" class="btn btn-sm btn-secondary" title="Editar"><i class="fas fa-edit"></i></a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                    <a href="service_call_create.php?customer_id=<?php echo $id; ?>" class="btn btn-success"><i class="fas fa-plus"></i> Criar Chamado</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to current button and content
                    this.classList.add('active');
                    document.getElementById(tabId + '-tab').classList.add('active');
                });
            });

            // Delete confirmation
            const deleteButtons = document.querySelectorAll('.delete-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('Tem certeza que deseja excluir este item?')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
