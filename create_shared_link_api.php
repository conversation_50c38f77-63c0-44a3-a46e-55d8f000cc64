<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Acesso negado. Faça login para continuar.']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Function to generate a random token
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Function to create a new shared link
function create_shared_link($type, $item_id, $expires_days = 30) {
    // First, check if the shared_links table exists
    $table_exists = false;
    $tables_result = db_query("SHOW TABLES LIKE 'shared_links'");
    if ($tables_result) {
        $table_exists = db_num_rows($tables_result) > 0;
    }

    // Create the table if it doesn't exist
    if (!$table_exists) {
        $create_table_sql = "CREATE TABLE IF NOT EXISTS shared_links (
            id INT(11) NOT NULL AUTO_INCREMENT,
            type ENUM('invoice', 'quote') NOT NULL,
            item_id INT(11) NOT NULL,
            token VARCHAR(64) NOT NULL,
            created_at DATETIME NOT NULL,
            expires_at DATETIME NULL,
            email_sent_to VARCHAR(255) NULL,
            access_count INT(11) DEFAULT 0,
            last_accessed DATETIME NULL,
            PRIMARY KEY (id),
            UNIQUE KEY (token),
            INDEX (type, item_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if (!db_query($create_table_sql)) {
            return ['success' => false, 'message' => 'Error creating shared_links table: ' . db_error()];
        }
    }

    // Check if item exists
    $table = ($type === 'invoice') ? 'invoices' : 'quotes';
    $query = "SELECT id FROM $table WHERE id = " . intval($item_id);
    $result = db_query($query);

    if (!$result || db_num_rows($result) === 0) {
        return ['success' => false, 'message' => ucfirst($type) . ' not found.'];
    }

    // Generate token
    $token = generate_token();

    // Set expiration date
    $expires_at = date('Y-m-d H:i:s', strtotime("+$expires_days days"));

    // Check if link already exists
    $query = "SELECT id, token FROM shared_links WHERE type = '" . db_escape($type) . "' AND item_id = " . intval($item_id);
    $result = db_query($query);

    if ($result && db_num_rows($result) > 0) {
        // Update existing link
        $row = db_fetch_assoc($result);
        $link_id = $row['id'];
        $token = $row['token']; // Keep the same token

        $query = "UPDATE shared_links SET expires_at = '" . db_escape($expires_at) . "' WHERE id = " . intval($link_id);

        if (db_query($query)) {
            return [
                'success' => true,
                'message' => 'Link updated successfully.',
                'token' => $token,
                'expires_at' => $expires_at
            ];
        } else {
            return ['success' => false, 'message' => 'Error updating link: ' . db_error()];
        }
    } else {
        // Create new link
        $query = "INSERT INTO shared_links (type, item_id, token, created_at, expires_at) VALUES ('" . 
                 db_escape($type) . "', " . intval($item_id) . ", '" . db_escape($token) . "', NOW(), '" . db_escape($expires_at) . "')";

        if (db_query($query)) {
            return [
                'success' => true,
                'message' => 'Link created successfully.',
                'token' => $token,
                'expires_at' => $expires_at
            ];
        } else {
            return ['success' => false, 'message' => 'Error creating link: ' . db_error()];
        }
    }
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Only POST requests are allowed.']);
    exit;
}

// Get POST data
$type = isset($_POST['type']) ? $_POST['type'] : '';
$item_id = isset($_POST['item_id']) ? intval($_POST['item_id']) : 0;
$expires_days = isset($_POST['expires_days']) ? intval($_POST['expires_days']) : 30;

// Validate input
if (empty($type) || !in_array($type, ['invoice', 'quote'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid type. Must be "invoice" or "quote".']);
    exit;
}

if ($item_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid item ID.']);
    exit;
}

// Create the shared link
$result = create_shared_link($type, $item_id, $expires_days);

// Generate the full URL if successful
if ($result['success']) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $base_path = rtrim($script_dir, '/') . '/';
    $link_url = "$protocol://$host{$base_path}view_shared.php?token=" . $result['token'];
    
    $result['link_url'] = $link_url;
}

// Return JSON response
echo json_encode($result);

// Close database connection
db_close();
?>
