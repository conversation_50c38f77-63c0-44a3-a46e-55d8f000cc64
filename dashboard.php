<?php
// Include session check
require_once 'check_session.php';

// Include database connection
require_once 'db_connect.php';

// Get dashboard statistics
$stats = [
    'customers' => 0,
    'inventory' => 0,
    'quotes' => 0,
    'invoices' => 0,
    'pending_quotes' => 0,
    'unpaid_invoices' => 0,
    'total_sales' => 0,
    'monthly_sales' => 0
];

// Count customers
$result = db_query("SELECT COUNT(*) as count FROM customers");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['customers'] = $row['count'];
}

// Count inventory items
$result = db_query("SELECT COUNT(*) as count FROM inventory");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['inventory'] = $row['count'];
}

// Count quotes
$result = db_query("SELECT COUNT(*) as count FROM quotes");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['quotes'] = $row['count'];
}

// Count invoices
$result = db_query("SELECT COUNT(*) as count FROM invoices");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['invoices'] = $row['count'];
}

// Count pending quotes
$result = db_query("SELECT COUNT(*) as count FROM quotes WHERE status = 'pending'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['pending_quotes'] = $row['count'];
}

// Count unpaid invoices
$result = db_query("SELECT COUNT(*) as count FROM invoices WHERE status = 'unpaid'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['unpaid_invoices'] = $row['count'];
}

// Calculate total sales (sum of all invoices)
$result = db_query("SELECT SUM(total) as total FROM invoices WHERE status != 'cancelled'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['total_sales'] = $row['total'] ?: 0;
}

// Calculate monthly sales (current month)
$current_month = date('Y-m');
$result = db_query("SELECT SUM(total) as total FROM invoices WHERE status != 'cancelled' AND invoice_date LIKE '$current_month%'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['monthly_sales'] = $row['total'] ?: 0;
}

// Get monthly sales data for chart (last 12 months)
$sales_data = [];
$sales_labels = [];

for ($i = 11; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $month_name = date('M', strtotime("-$i months"));
    $sales_labels[] = $month_name;

    $result = db_query("SELECT SUM(total) as total FROM invoices WHERE status != 'cancelled' AND invoice_date LIKE '$month%'");
    if ($result && $row = db_fetch_assoc($result)) {
        $sales_data[] = $row['total'] ?: 0;
    } else {
        $sales_data[] = 0;
    }
}

// Get quotes vs invoices data for chart (last 6 months)
$quotes_data = [];
$invoices_data = [];

for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));

    $result = db_query("SELECT COUNT(*) as count FROM quotes WHERE quote_date LIKE '$month%'");
    if ($result && $row = db_fetch_assoc($result)) {
        $quotes_data[] = $row['count'];
    } else {
        $quotes_data[] = 0;
    }

    $result = db_query("SELECT COUNT(*) as count FROM invoices WHERE invoice_date LIKE '$month%'");
    if ($result && $row = db_fetch_assoc($result)) {
        $invoices_data[] = $row['count'];
    } else {
        $invoices_data[] = 0;
    }
}

// Get top 5 products
$top_products = [];
$result = db_query("
    SELECT i.name, COUNT(ii.inventory_id) as count
    FROM invoice_items ii
    JOIN inventory i ON ii.inventory_id = i.id
    GROUP BY ii.inventory_id
    ORDER BY count DESC
    LIMIT 5
");

if ($result && db_num_rows($result) > 0) {
    $top_products = db_fetch_all($result);
}

$top_products_labels = [];
$top_products_data = [];

foreach ($top_products as $product) {
    $top_products_labels[] = $product['name'];
    $top_products_data[] = $product['count'];
}

// Get recent invoices
$recent_invoices = [];
$result = db_query("
    SELECT i.*, c.name as customer_name
    FROM invoices i
    JOIN customers c ON i.customer_id = c.id
    ORDER BY i.created_at DESC
    LIMIT 5
");

if ($result && db_num_rows($result) > 0) {
    $recent_invoices = db_fetch_all($result);
}

// Get recent quotes
$recent_quotes = [];
$result = db_query("
    SELECT q.*, c.name as customer_name
    FROM quotes q
    JOIN customers c ON q.customer_id = c.id
    ORDER BY q.created_at DESC
    LIMIT 5
");

if ($result && db_num_rows($result) > 0) {
    $recent_quotes = db_fetch_all($result);
}

// Convert data to JSON for JavaScript
$sales_data_json = json_encode($sales_data);
$quotes_data_json = json_encode($quotes_data);
$invoices_data_json = json_encode($invoices_data);
$top_products_labels_json = json_encode($top_products_labels);
$top_products_data_json = json_encode($top_products_data);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Tonys AC Repair</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css"><script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="dashboard.php" class="sidebar-logo">Tonys AC Repair</a>
            </div>

            <ul class="sidebar-nav">
                <li class="sidebar-nav-item">
                    <a href="dashboard.php" class="sidebar-nav-link active">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="customers.php" class="sidebar-nav-link">
                        <i class="fas fa-users"></i> Clientes
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="inventory.php" class="sidebar-nav-link">
                        <i class="fas fa-boxes"></i> Estoque
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="quotes.php" class="sidebar-nav-link">
                        <i class="fas fa-file-invoice"></i> Orçamentos
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="invoices_list.php" class="sidebar-nav-link">
                        <i class="fas fa-file-invoice-dollar"></i> Faturas
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <i class="fas fa-chart-bar"></i> Relatórios
                    </a>
                </li>
                <li class="sidebar-nav-item">
                    <a href="#" class="sidebar-nav-link">
                        <i class="fas fa-cog"></i> Configurações
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <a href="#"><i class="fas fa-sign-out-alt"></i> Sair</a>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="navbar">
                    <button class="menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>

                    <div class="navbar-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Pesquisar...">
                    </div>

                    <div class="navbar-user">
                        <img src="https://via.placeholder.com/35" alt="User">
                        <span>Administrador</span>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="dashboard-content">
                <h1 class="page-title">Dashboard</h1>

                <!-- Filter Options -->
                <div class="filter-options" style="margin-bottom: 20px;">
                    <button onclick="filterDashboardData('today')" class="btn">Hoje</button>
                    <button onclick="filterDashboardData('week')" class="btn">Esta Semana</button>
                    <button onclick="filterDashboardData('month')" class="btn">Este Mês</button>
                    <button onclick="filterDashboardData('year')" class="btn">Este Ano</button>
                </div>

                <!-- Stats Cards -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-card-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3><?php echo $stats['customers']; ?></h3>
                            <p>Clientes</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon inventory">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3><?php echo $stats['inventory']; ?></h3>
                            <p>Produtos</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon quotes">
                            <i class="fas fa-file-invoice"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3><?php echo $stats['quotes']; ?></h3>
                            <p>Orçamentos</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon invoices">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="stat-card-content">
                            <h3><?php echo $stats['invoices']; ?></h3>
                            <p>Faturas</p>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="dashboard-charts">
                    <div class="chart-card">
                        <div class="chart-card-header">
                            <h2 class="chart-card-title">Vendas Mensais</h2>
                            <div class="chart-card-actions">
                                <button><i class="fas fa-download"></i></button>
                                <button><i class="fas fa-sync-alt"></i></button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="salesChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-card-header">
                            <h2 class="chart-card-title">Produtos Mais Vendidos</h2>
                            <div class="chart-card-actions">
                                <button><i class="fas fa-download"></i></button>
                                <button><i class="fas fa-sync-alt"></i></button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="topProductsChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="chart-card" style="margin-bottom: 1.5rem;">
                    <div class="chart-card-header">
                        <h2 class="chart-card-title">Orçamentos vs Faturas</h2>
                        <div class="chart-card-actions">
                            <button><i class="fas fa-download"></i></button>
                            <button><i class="fas fa-sync-alt"></i></button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="quotesInvoicesChart"></canvas>
                    </div>
                </div>

                <!-- Tables -->
                <div class="dashboard-tables">
                    <div class="table-card">
                        <div class="table-card-header">
                            <h2 class="table-card-title">Faturas Recentes</h2>
                            <div class="table-card-actions">
                                <a href="invoices_list.php">Ver Todas</a>
                            </div>
                        </div>
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Número</th>
                                    <th>Cliente</th>
                                    <th>Data</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recent_invoices)): ?>
                                    <tr>
                                        <td colspan="5">Nenhuma fatura encontrada.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recent_invoices as $invoice): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                            <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($invoice['invoice_date'])); ?></td>
                                            <td>R$ <?php echo number_format($invoice['total'], 2, ',', '.'); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';

                                                switch ($invoice['status']) {
                                                    case 'paid':
                                                        $status_class = 'status-paid';
                                                        $status_text = 'Pago';
                                                        break;
                                                    case 'unpaid':
                                                        $status_class = 'status-unpaid';
                                                        $status_text = 'Não Pago';
                                                        break;
                                                    case 'partial':
                                                        $status_class = 'status-pending';
                                                        $status_text = 'Parcial';
                                                        break;
                                                    case 'cancelled':
                                                        $status_class = 'status-unpaid';
                                                        $status_text = 'Cancelado';
                                                        break;
                                                    default:
                                                        $status_text = $invoice['status'];
                                                }
                                                ?>
                                                <span class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="table-card">
                        <div class="table-card-header">
                            <h2 class="table-card-title">Orçamentos Recentes</h2>
                            <div class="table-card-actions">
                                <a href="quotes.php">Ver Todos</a>
                            </div>
                        </div>
                        <table class="dashboard-table">
                            <thead>
                                <tr>
                                    <th>Número</th>
                                    <th>Cliente</th>
                                    <th>Data</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recent_quotes)): ?>
                                    <tr>
                                        <td colspan="5">Nenhum orçamento encontrado.</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($recent_quotes as $quote): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($quote['quote_number']); ?></td>
                                            <td><?php echo htmlspecialchars($quote['customer_name']); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></td>
                                            <td>R$ <?php echo number_format($quote['total'], 2, ',', '.'); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = '';

                                                switch ($quote['status']) {
                                                    case 'pending':
                                                        $status_class = 'status-pending';
                                                        $status_text = 'Pendente';
                                                        break;
                                                    case 'approved':
                                                        $status_class = 'status-approved';
                                                        $status_text = 'Aprovado';
                                                        break;
                                                    case 'rejected':
                                                        $status_class = 'status-unpaid';
                                                        $status_text = 'Rejeitado';
                                                        break;
                                                    case 'expired':
                                                        $status_class = 'status-unpaid';
                                                        $status_text = 'Expirado';
                                                        break;
                                                    default:
                                                        $status_text = $quote['status'];
                                                }
                                                ?>
                                                <span class="status <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions" style="display: flex; gap: 1rem; margin-bottom: 2rem;">
                    <a href="customer_create.php" class="btn">
                        <i class="fas fa-user-plus"></i> Novo Cliente
                    </a>
                    <a href="inventory_create.php" class="btn">
                        <i class="fas fa-box-open"></i> Novo Produto
                    </a>
                    <a href="quote_create.php" class="btn">
                        <i class="fas fa-file-invoice"></i> Novo Orçamento
                    </a>
                    <a href="invoice_create.php" class="btn">
                        <i class="fas fa-file-invoice-dollar"></i> Nova Fatura
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Pass PHP data to JavaScript
        const salesData = <?php echo $sales_data_json; ?>;
        const quotesData = <?php echo $quotes_data_json; ?>;
        const invoicesData = <?php echo $invoices_data_json; ?>;
        const topProductsLabels = <?php echo !empty($top_products_labels_json) ? $top_products_labels_json : '["Sem dados"]'; ?>;
        const topProductsData = <?php echo !empty($top_products_data_json) ? $top_products_data_json : '[100]'; ?>;
    </script>
    <script src="js/main.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
