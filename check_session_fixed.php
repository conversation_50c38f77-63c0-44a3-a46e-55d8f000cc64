<?php
// IMPORTANTE: Não deve haver nenhuma saída antes deste ponto
// Isso inclui espaços em branco, HTML, ou chamadas a echo/print

// Configurar diretório de sessões personalizado
$session_dir = __DIR__ . '/sessions';
if (!file_exists($session_dir)) {
    @mkdir($session_dir, 0777, true);
}

// Configurar opções de sessão ANTES de iniciar a sessão
ini_set('session.save_path', $session_dir);
ini_set('session.gc_probability', 1);
ini_set('session.gc_maxlifetime', 86400); // 24 horas
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Definir como 1 se estiver usando HTTPS
ini_set('session.use_trans_sid', 0);
ini_set('session.cache_limiter', 'nocache');

// Start session if not already started
try {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
} catch (Exception $e) {
    // Ignorar erros de sessão
    error_log("Erro ao iniciar sessão: " . $e->getMessage());
}

// Include database connection if not already included
if (!isset($conn) && file_exists('db_connect.php')) {
    require_once 'db_connect.php';
}

// Check for default admin user
if (file_exists('includes/check_admin.php')) {
    require_once 'includes/check_admin.php';
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Redirect to login page
    header('Location: login_modern.php');
    exit;
}
?>
