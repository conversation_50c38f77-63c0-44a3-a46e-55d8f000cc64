<?php
// Clean start - no output before this point
ob_start();

// Desabilitar exibição de erros para não quebrar o JSON
ini_set('display_errors', 0);
error_reporting(0);

// API para criar novos clientes
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Authentication required'
    ]);
    exit;
}

// Verificar se o arquivo de conexão existe
if (!file_exists('db_connect.php')) {
    ob_clean();
    echo json_encode(['error' => 'Arquivo db_connect.php não encontrado']);
    exit;
}

// Incluir conexão com banco de dados sem verificação de sessão
define('BYPASS_SECURITY', true);
try {
    require_once 'db_connect.php';
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['error' => 'Erro ao conectar com banco: ' . $e->getMessage()]);
    exit;
}

try {
    // Verificar se é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        ob_clean();
        echo json_encode(['error' => 'Método não permitido']);
        exit;
    }
    
    // Obter dados do formulário
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $city = trim($_POST['city'] ?? '');
    $state = trim($_POST['state'] ?? '');
    $zip = trim($_POST['zip'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    // Validar nome obrigatório
    if (empty($name)) {
        ob_clean();
        echo json_encode(['error' => 'Nome é obrigatório']);
        exit;
    }
    
    // Escapar dados
    $name_escaped = db_escape($name);
    $email_escaped = db_escape($email);
    $phone_escaped = db_escape($phone);
    $address_escaped = db_escape($address);
    $city_escaped = db_escape($city);
    $state_escaped = db_escape($state);
    $zip_escaped = db_escape($zip);
    $notes_escaped = db_escape($notes);
    
    // Inserir cliente
    $query = "INSERT INTO customers (name, email, phone, address, city, state, zip, notes, created_at, updated_at)
              VALUES ('$name_escaped', '$email_escaped', '$phone_escaped', '$address_escaped',
                      '$city_escaped', '$state_escaped', '$zip_escaped', '$notes_escaped', NOW(), NOW())";
    
    if (db_query($query)) {
        $customer_id = db_insert_id();

        ob_clean();
        echo json_encode([
            'success' => true,
            'customer' => [
                'id' => (int)$customer_id,
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'address' => $address
            ]
        ]);
    } else {
        ob_clean();
        echo json_encode(['error' => 'Erro ao criar cliente: ' . db_error()]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    ob_clean();
    echo json_encode(['error' => 'Erro interno: ' . $e->getMessage()]);
} finally {
    if (function_exists('db_close')) {
        db_close();
    }
}

// End output buffering and flush
ob_end_flush();
?>
