<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Function to generate a random token
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Function to create a new shared link
function create_shared_link($conn, $type, $item_id, $expires_days = 30) {
    // First, check if the shared_links table exists
    $table_exists = false;
    $tables_result = $conn->query("SHOW TABLES LIKE 'shared_links'");
    if ($tables_result) {
        $table_exists = $tables_result->num_rows > 0;
    }

    // Create the table if it doesn't exist
    if (!$table_exists) {
        $create_table_sql = "CREATE TABLE IF NOT EXISTS shared_links (
            id INT(11) NOT NULL AUTO_INCREMENT,
            type ENUM('invoice', 'quote') NOT NULL,
            item_id INT(11) NOT NULL,
            token VARCHAR(64) NOT NULL,
            created_at DATETIME NOT NULL,
            expires_at DATETIME NULL,
            email_sent_to VARCHAR(255) NULL,
            access_count INT(11) DEFAULT 0,
            last_accessed DATETIME NULL,
            PRIMARY KEY (id),
            UNIQUE KEY (token),
            INDEX (type, item_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        if (!$conn->query($create_table_sql)) {
            return ['success' => false, 'message' => 'Error creating shared_links table: ' . $conn->error];
        }
    }

    // Check if item exists
    $table = ($type === 'invoice') ? 'invoices' : 'quotes';
    $query = "SELECT id FROM $table WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $item_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => ucfirst($type) . ' not found.'];
    }

    // Generate token
    $token = generate_token();

    // Set expiration date
    $expires_at = date('Y-m-d H:i:s', strtotime("+$expires_days days"));

    // Check if link already exists
    $query = "SELECT id, token FROM shared_links WHERE type = ? AND item_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('si', $type, $item_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing link
        $row = $result->fetch_assoc();
        $link_id = $row['id'];
        $token = $row['token']; // Keep the same token

        $query = "UPDATE shared_links SET expires_at = ? WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('si', $expires_at, $link_id);

        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Link updated successfully.',
                'token' => $token,
                'expires_at' => $expires_at
            ];
        } else {
            return ['success' => false, 'message' => 'Error updating link: ' . $conn->error];
        }
    } else {
        // Create new link
        $query = "INSERT INTO shared_links (type, item_id, token, created_at, expires_at) VALUES (?, ?, ?, NOW(), ?)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('siss', $type, $item_id, $token, $expires_at);

        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => 'Link created successfully.',
                'token' => $token,
                'expires_at' => $expires_at
            ];
        } else {
            return ['success' => false, 'message' => 'Error creating link: ' . $conn->error];
        }
    }
}

// Function to delete a shared link
function delete_shared_link($conn, $link_id) {
    // Check if the shared_links table exists
    $table_exists = false;
    $tables_result = $conn->query("SHOW TABLES LIKE 'shared_links'");
    if ($tables_result) {
        $table_exists = $tables_result->num_rows > 0;
    }

    if (!$table_exists) {
        return ['success' => false, 'message' => 'Shared links table does not exist.'];
    }

    $query = "DELETE FROM shared_links WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $link_id);

    if ($stmt->execute()) {
        return ['success' => true, 'message' => 'Link deleted successfully.'];
    } else {
        return ['success' => false, 'message' => 'Error deleting link: ' . $conn->error];
    }
}

// Function to send link via email
function send_link_email($conn, $link_id, $email) {
    // Check if the shared_links table exists
    $table_exists = false;
    $tables_result = $conn->query("SHOW TABLES LIKE 'shared_links'");
    if ($tables_result) {
        $table_exists = $tables_result->num_rows > 0;
    }

    if (!$table_exists) {
        return ['success' => false, 'message' => 'Shared links table does not exist.'];
    }

    // Get link details
    $query = "SELECT sl.*,
              CASE
                WHEN sl.type = 'invoice' THEN i.invoice_number
                WHEN sl.type = 'quote' THEN q.quote_number
              END as item_number,
              CASE
                WHEN sl.type = 'invoice' THEN c_i.name
                WHEN sl.type = 'quote' THEN c_q.name
              END as customer_name
              FROM shared_links sl
              LEFT JOIN invoices i ON sl.type = 'invoice' AND sl.item_id = i.id
              LEFT JOIN quotes q ON sl.type = 'quote' AND sl.item_id = q.id
              LEFT JOIN customers c_i ON i.customer_id = c_i.id
              LEFT JOIN customers c_q ON q.customer_id = c_q.id
              WHERE sl.id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $link_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return ['success' => false, 'message' => 'Link not found.'];
    }

    $link = $result->fetch_assoc();
    $token = $link['token'];
    $type = $link['type'];
    $item_number = $link['item_number'];
    $customer_name = $link['customer_name'];

    // Create the link URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $base_path = rtrim($script_dir, '/') . '/';
    $link_url = "$protocol://$host{$base_path}view_shared.php?token=$token";

    // Email subject
    $subject = ucfirst($type) . " #$item_number for $customer_name";

    // Email message
    $message = "
    <html>
    <head>
        <title>$subject</title>
    </head>
    <body>
        <p>Dear Customer,</p>
        <p>You can view your " . strtolower($type) . " #$item_number by clicking the link below:</p>
        <p><a href='$link_url'>View " . ucfirst($type) . "</a></p>
        <p>This link will expire on " . date('m/d/Y', strtotime($link['expires_at'])) . ".</p>
        <p>Thank you for your business!</p>
    </body>
    </html>
    ";

    // Email headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: Tonys AC Repair <<EMAIL>>" . "\r\n";

    // Send email
    if (mail($email, $subject, $message, $headers)) {
        // Update the email_sent_to field
        $query = "UPDATE shared_links SET email_sent_to = ? WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('si', $email, $link_id);
        $stmt->execute();

        return ['success' => true, 'message' => 'Email sent successfully to ' . $email];
    } else {
        return ['success' => false, 'message' => 'Error sending email.'];
    }
}

// Initialize variables
$message = '';
$message_type = '';
$shared_links = [];

// Check if we're coming from a direct link with type and item_id
$direct_type = isset($_GET['type']) ? $_GET['type'] : '';
$direct_item_id = isset($_GET['item_id']) ? intval($_GET['item_id']) : 0;

// If direct link parameters are provided, create a shared link automatically
if (!empty($direct_type) && $direct_item_id > 0) {
    $expires_days = 30; // Default expiration
    $result = create_shared_link($conn, $direct_type, $direct_item_id, $expires_days);

    if ($result['success']) {
        $message = $result['message'];
        $message_type = 'success';
    } else {
        $message = $result['message'];
        $message_type = 'danger';
    }
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Create new link
    if (isset($_POST['create_link'])) {
        $type = $_POST['type'];
        $item_id = intval($_POST['item_id']);
        $expires_days = intval($_POST['expires_days']);

        $result = create_shared_link($conn, $type, $item_id, $expires_days);

        if ($result['success']) {
            $message = $result['message'];
            $message_type = 'success';
        } else {
            $message = $result['message'];
            $message_type = 'danger';
        }
    }

    // Delete link
    if (isset($_POST['delete_link'])) {
        $link_id = intval($_POST['link_id']);

        $result = delete_shared_link($conn, $link_id);

        if ($result['success']) {
            $message = $result['message'];
            $message_type = 'success';
        } else {
            $message = $result['message'];
            $message_type = 'danger';
        }
    }

    // Send link via email
    if (isset($_POST['send_email'])) {
        $link_id = intval($_POST['link_id']);
        $email = $_POST['email'];

        $result = send_link_email($conn, $link_id, $email);

        if ($result['success']) {
            $message = $result['message'];
            $message_type = 'success';
        } else {
            $message = $result['message'];
            $message_type = 'danger';
        }
    }
}

// Check if shared_links table exists
$table_exists = false;
$tables_result = $conn->query("SHOW TABLES LIKE 'shared_links'");
if ($tables_result) {
    $table_exists = $tables_result->num_rows > 0;
}

// Get all shared links
if ($table_exists || $conn->query("SHOW TABLES LIKE 'shared_links'")->num_rows > 0) {
    $query = "SELECT sl.*,
              CASE
                WHEN sl.type = 'invoice' THEN i.invoice_number
                WHEN sl.type = 'quote' THEN q.quote_number
              END as item_number,
              CASE
                WHEN sl.type = 'invoice' THEN c_i.name
                WHEN sl.type = 'quote' THEN c_q.name
              END as customer_name
              FROM shared_links sl
              LEFT JOIN invoices i ON sl.type = 'invoice' AND sl.item_id = i.id
              LEFT JOIN quotes q ON sl.type = 'quote' AND sl.item_id = q.id
              LEFT JOIN customers c_i ON i.customer_id = c_i.id
              LEFT JOIN customers c_q ON q.customer_id = c_q.id
              ORDER BY sl.created_at DESC";
    $result = $conn->query($query);

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $shared_links[] = $row;
        }
    }
}

// Get all invoices for dropdown
$invoices = [];
$query = "SELECT i.id, i.invoice_number, c.name as customer_name
          FROM invoices i
          JOIN customers c ON i.customer_id = c.id
          ORDER BY i.created_at DESC";
$result = $conn->query($query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $invoices[] = $row;
    }
}

// Get all quotes for dropdown
$quotes = [];
$query = "SELECT q.id, q.quote_number, c.name as customer_name
          FROM quotes q
          JOIN customers c ON q.customer_id = c.id
          ORDER BY q.created_at DESC";
$result = $conn->query($query);

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $quotes[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared Links - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .container-fluid {
            width: 100%;
            padding: 20px;
            background-color: #fff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-danger {
            background-color: #e74c3c;
            color: white;
        }

        .btn-success {
            background-color: #2ecc71;
            color: white;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .links-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .links-table th, .links-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .links-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .links-table tr:hover {
            background-color: #f5f5f5;
        }

        .action-buttons {
            position: relative;
            display: inline-block;
        }

        .action-menu-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            cursor: pointer;
        }

        .action-menu-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 4px;
        }

        .action-menu-content a, .action-menu-content button {
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
            width: 100%;
            background: none;
            border: none;
            cursor: pointer;
        }

        .action-menu-content a:hover, .action-menu-content button:hover {
            background-color: #f1f1f1;
        }

        .action-menu-content form {
            margin: 0;
        }

        .show-menu {
            display: block;
        }



        .email-form {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .email-form .form-group {
            display: flex;
            gap: 10px;
        }

        .email-form .form-control {
            flex: 1;
        }

        .badge {
            display: inline-block;
            padding: 3px 7px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-invoice {
            background-color: #3498db;
            color: white;
        }

        .badge-quote {
            background-color: #f39c12;
            color: white;
        }

        .badge-active {
            background-color: #2ecc71;
            color: white;
        }

        .badge-expired {
            background-color: #e74c3c;
            color: white;
        }

        @media screen and (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .links-table {
                font-size: 0.9em;
            }

            .links-table th, .links-table td {
                padding: 8px 5px;
            }

            .action-menu-btn {
                padding: 5px 8px;
                font-size: 0.9em;
            }

            .action-menu-content {
                min-width: 140px;
            }

            .action-menu-content a, .action-menu-content button {
                padding: 10px 12px;
                font-size: 0.9em;
            }

            .admin-content {
                margin-left: 0;
                padding-top: 60px;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>

    <div class="admin-content">
        <div class="container-fluid">
            <h1>Shared Links</h1>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <div class="section">
                <div class="section-title">Create New Shared Link</div>
                <form method="post" action="">
                    <div class="form-group">
                        <label for="type" class="form-label">Type</label>
                        <select id="type" name="type" class="form-control" required>
                            <option value="invoice" <?php echo ($direct_type === 'invoice') ? 'selected' : ''; ?>>Invoice</option>
                            <option value="quote" <?php echo ($direct_type === 'quote') ? 'selected' : ''; ?>>Quote</option>
                        </select>
                    </div>

                    <div class="form-group" id="invoice-select">
                        <label for="invoice_id" class="form-label">Invoice</label>
                        <select id="invoice_id" name="item_id" class="form-control">
                            <option value="">Select an invoice</option>
                            <?php foreach ($invoices as $invoice): ?>
                                <option value="<?php echo $invoice['id']; ?>" <?php echo ($direct_type === 'invoice' && $direct_item_id === (int)$invoice['id']) ? 'selected' : ''; ?>>
                                    #<?php echo $invoice['invoice_number']; ?> - <?php echo $invoice['customer_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group" id="quote-select" style="display: none;">
                        <label for="quote_id" class="form-label">Quote</label>
                        <select id="quote_id" name="item_id" class="form-control">
                            <option value="">Select a quote</option>
                            <?php foreach ($quotes as $quote): ?>
                                <option value="<?php echo $quote['id']; ?>" <?php echo ($direct_type === 'quote' && $direct_item_id === (int)$quote['id']) ? 'selected' : ''; ?>>
                                    #<?php echo $quote['quote_number']; ?> - <?php echo $quote['customer_name']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="expires_days" class="form-label">Expires After (days)</label>
                        <select id="expires_days" name="expires_days" class="form-control">
                            <option value="7">7 days</option>
                            <option value="14">14 days</option>
                            <option value="30" selected>30 days</option>
                            <option value="60">60 days</option>
                            <option value="90">90 days</option>
                        </select>
                    </div>

                    <button type="submit" name="create_link" class="btn btn-primary">Create Link</button>
                </form>
            </div>

            <div class="section">
                <div class="section-title">Existing Shared Links</div>
                <?php if (empty($shared_links)): ?>
                    <p>No shared links found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="links-table">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Item</th>
                                    <th>Customer</th>
                                    <th>Created</th>
                                    <th>Expires</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($shared_links as $link): ?>
                                    <tr>
                                        <td>
                                            <span class="badge badge-<?php echo $link['type']; ?>">
                                                <?php echo ucfirst($link['type']); ?>
                                            </span>
                                        </td>
                                        <td>#<?php echo $link['item_number']; ?></td>
                                        <td><?php echo $link['customer_name']; ?></td>
                                        <td><?php echo date('m/d/Y', strtotime($link['created_at'])); ?></td>
                                        <td><?php echo date('m/d/Y', strtotime($link['expires_at'])); ?></td>
                                        <td>
                                            <?php if (strtotime($link['expires_at']) > time()): ?>
                                                <span class="badge badge-active">Active</span>
                                            <?php else: ?>
                                                <span class="badge badge-expired">Expired</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <?php
                                                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                                                $host = $_SERVER['HTTP_HOST'];
                                                $script_dir = dirname($_SERVER['SCRIPT_NAME']);
                                                $base_path = rtrim($script_dir, '/') . '/';
                                                $link_url = "$protocol://$host{$base_path}view_shared.php?token=" . $link['token'];
                                                ?>
                                                <button class="action-menu-btn" onclick="toggleActionMenu('action-menu-<?php echo $link['id']; ?>')">
                                                    <i class="fas fa-ellipsis-v"></i> Actions
                                                </button>
                                                <div id="action-menu-<?php echo $link['id']; ?>" class="action-menu-content">
                                                    <button onclick="copyToClipboard('<?php echo $link_url; ?>')">
                                                        <i class="fas fa-copy"></i> Copy Link
                                                    </button>

                                                    <button onclick="toggleEmailForm('email-form-<?php echo $link['id']; ?>')">
                                                        <i class="fas fa-envelope"></i> Email
                                                    </button>

                                                    <form method="post" action="" onsubmit="return confirm('Are you sure you want to delete this link?');">
                                                        <input type="hidden" name="link_id" value="<?php echo $link['id']; ?>">
                                                        <button type="submit" name="delete_link">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>

                                            <div id="email-form-<?php echo $link['id']; ?>" class="email-form">
                                                <form method="post" action="">
                                                    <input type="hidden" name="link_id" value="<?php echo $link['id']; ?>">
                                                    <div class="form-group">
                                                        <input type="email" name="email" class="form-control" placeholder="Enter email address" required>
                                                        <button type="submit" name="send_email" class="btn btn-primary">Send</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Function to toggle between invoice and quote select
        function toggleTypeSelect(type) {
            if (type === 'invoice') {
                document.getElementById('invoice-select').style.display = 'block';
                document.getElementById('quote-select').style.display = 'none';
                document.getElementById('invoice_id').setAttribute('name', 'item_id');
                document.getElementById('quote_id').removeAttribute('name');
            } else {
                document.getElementById('invoice-select').style.display = 'none';
                document.getElementById('quote-select').style.display = 'block';
                document.getElementById('quote_id').setAttribute('name', 'item_id');
                document.getElementById('invoice_id').removeAttribute('name');
            }
        }

        // Toggle on change
        document.getElementById('type').addEventListener('change', function() {
            toggleTypeSelect(this.value);
        });

        // Initialize with the correct selection
        document.addEventListener('DOMContentLoaded', function() {
            toggleTypeSelect(document.getElementById('type').value);
        });

        // Copy link to clipboard
        function copyToClipboard(text) {
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            alert('Link copied to clipboard!');
        }

        // Toggle email form
        function toggleEmailForm(formId) {
            const form = document.getElementById(formId);
            if (form.style.display === 'block') {
                form.style.display = 'none';
            } else {
                // Hide all email forms
                const emailForms = document.querySelectorAll('.email-form');
                emailForms.forEach(form => {
                    form.style.display = 'none';
                });

                // Hide all action menus
                const actionMenus = document.querySelectorAll('.action-menu-content');
                actionMenus.forEach(menu => {
                    menu.classList.remove('show-menu');
                });

                // Show the selected form
                form.style.display = 'block';
            }
        }

        // Toggle action menu
        function toggleActionMenu(menuId) {
            const menu = document.getElementById(menuId);

            // Hide all action menus first
            const actionMenus = document.querySelectorAll('.action-menu-content');
            actionMenus.forEach(m => {
                if (m.id !== menuId) {
                    m.classList.remove('show-menu');
                }
            });

            // Hide all email forms
            const emailForms = document.querySelectorAll('.email-form');
            emailForms.forEach(form => {
                form.style.display = 'none';
            });

            // Toggle the selected menu
            menu.classList.toggle('show-menu');
        }

        // Close the action menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.matches('.action-menu-btn') && !event.target.closest('.action-menu-content')) {
                const actionMenus = document.querySelectorAll('.action-menu-content');
                actionMenus.forEach(menu => {
                    menu.classList.remove('show-menu');
                });
            }
        });
    </script>
</body>
</html>
