<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check if it exists
if (file_exists('check_session.php')) {
    require_once 'check_session.php';
}

// Include functions if they exist
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Initialize variables
$id = '';
$username = '';
$error_message = '';
$success_message = '';

// Check if user ID is provided
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $id = $_GET['id'];

    // Get user details
    $query = "SELECT id, username FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $username = $user['username'];
    } else {
        $error_message = 'User not found.';
    }

    $stmt->close();
} else {
    $error_message = 'User ID not provided.';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    $id = $_POST['id'];

    // Delete user from database
    $query = "DELETE FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);

    if ($stmt->execute()) {
        // Redirect to settings page with success message
        header("Location: settings.php?deleted=1");
        exit;
    } else {
        $error_message = 'Error deleting user: ' . $conn->error;
    }

    $stmt->close();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete User - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .delete-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .delete-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .delete-header {
            background-color: #f8d7da;
            padding: 15px 20px;
            border-bottom: 1px solid #f5c6cb;
        }

        .delete-header h2 {
            margin: 0;
            font-size: 1.25rem;
            color: #721c24;
        }

        .delete-body {
            padding: 20px;
        }

        .delete-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .delete-warning i {
            margin-right: 10px;
            font-size: 1.25rem;
        }

        .delete-actions {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .delete-container {
                padding: 10px;
            }

            .delete-actions {
                flex-direction: column;
            }

            .btn-danger, .btn-secondary {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="delete-container">
                <h1>Delete User</h1>

                <div class="delete-card">
                    <div class="delete-header">
                        <h2><i class="fas fa-exclamation-triangle"></i> Confirm Deletion</h2>
                    </div>
                    <div class="delete-body">
                        <?php if (!empty($error_message)): ?>
                            <div class="alert alert-danger"><?php echo $error_message; ?></div>
                        <?php else: ?>
                            <div class="delete-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> You are about to delete the user "<?php echo htmlspecialchars($username); ?>". This action cannot be undone.
                            </div>

                            <form method="POST" action="user_delete.php?id=<?php echo $id; ?>">
                                <input type="hidden" name="id" value="<?php echo $id; ?>">

                                <div class="delete-actions">
                                    <button type="submit" name="confirm_delete" class="btn-danger"><i class="fas fa-trash-alt"></i> Delete User</button>
                                    <a href="settings.php" class="btn-secondary"><i class="fas fa-times"></i> Cancel</a>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
