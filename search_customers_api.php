<?php
// Desabilitar exibição de erros para não quebrar o JSON
ini_set('display_errors', 0);
error_reporting(0);

// API para buscar clientes
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Verificar se o arquivo de conexão existe
if (!file_exists('db_connect.php')) {
    echo json_encode(['error' => 'Arquivo db_connect.php não encontrado']);
    exit;
}

// Incluir conexão com banco de dados
try {
    require_once 'db_connect.php';
} catch (Exception $e) {
    echo json_encode(['error' => 'Erro ao conectar com banco: ' . $e->getMessage()]);
    exit;
}

// Verificar se as funções do banco existem
if (!function_exists('db_query')) {
    echo json_encode(['error' => 'Função db_query não encontrada']);
    exit;
}

if (!function_exists('db_escape')) {
    echo json_encode(['error' => 'Função db_escape não encontrada']);
    exit;
}

try {
    // Verificar se há termo de busca
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';

    if (empty($search)) {
        echo json_encode([]);
        exit;
    }

    // Escapar termo de busca
    $search_escaped = db_escape($search);

    // Query para buscar clientes
    $query = "SELECT id, name, phone, email, address
              FROM customers
              WHERE name LIKE '%$search_escaped%'
              ORDER BY name
              LIMIT 10";

    $result = db_query($query);

    if (!$result) {
        echo json_encode(['error' => 'Erro na consulta: ' . (function_exists('db_error') ? db_error() : 'Erro desconhecido')]);
        exit;
    }

    $customers = [];

    if (function_exists('db_num_rows') && db_num_rows($result) > 0) {
        while ($row = db_fetch_assoc($result)) {
            $customers[] = [
                'id' => (int)$row['id'],
                'name' => $row['name'] ?? '',
                'phone' => $row['phone'] ?? '',
                'email' => $row['email'] ?? '',
                'address' => $row['address'] ?? ''
            ];
        }
    }

    echo json_encode($customers);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro interno: ' . $e->getMessage()]);
} finally {
    if (function_exists('db_close')) {
        db_close();
    }
}
?>
