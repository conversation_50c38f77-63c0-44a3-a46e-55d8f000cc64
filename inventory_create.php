<?php
// Include database connection
require_once 'db_connect.php';
require_once 'check_session.php';
require_once 'includes/functions.php';

// Get URL parameters first
$prefilled_name = $_GET['name'] ?? '';
$return_to = $_GET['return_to'] ?? '';

// Process form submission FIRST (before any output)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = trim($_POST['name'] ?? '');
    $sku = trim($_POST['sku'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = trim($_POST['price'] ?? '');
    $quantity = trim($_POST['quantity'] ?? '');
    $category = trim($_POST['category'] ?? '');
    $return_to = trim($_POST['return_to'] ?? '');

    // Debug: Log the return_to value
    error_log("Return to value: " . $return_to);

    // Debug: Log form data
    error_log("Form data - Name: $name, Price: $price, Return to: $return_to");

    // Validate form data
    if (empty($name)) {
        $error_message = 'Product name is required';
        error_log("Validation failed: Product name is required");
    } elseif (empty($price)) {
        $error_message = 'Price is required';
        error_log("Validation failed: Price is required");
    } else {
        error_log("Validation passed, proceeding to insert");
        // Convert price to decimal
        $price = str_replace(',', '.', $price);

        // Set default quantity if empty
        if (empty($quantity)) {
            $quantity = 0;
        }

        // Insert product into database
        $query = "INSERT INTO inventory (name, sku, description, price, quantity, category, created_at, updated_at)
                  VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";

        $stmt = $conn->prepare($query);
        $stmt->bind_param('sssdis', $name, $sku, $description, $price, $quantity, $category);

        if ($stmt->execute()) {
            $product_id = $conn->insert_id;
            // Debug: Log successful execution
            error_log("Product created successfully. ID: $product_id, Return to: " . $return_to);

            // Check if should redirect back to invoice creation
            if ($return_to === 'invoice_create') {
                error_log("Redirecting to invoice_create.php with product ID: $product_id");
                // Try PHP redirect first
                if (!headers_sent()) {
                    header("Location: invoice_create.php?product_created=true&new_product_id=$product_id");
                    exit;
                } else {
                    // If headers already sent, use JavaScript redirect
                    echo "<script>window.location.href = 'invoice_create.php?product_created=true&new_product_id=$product_id';</script>";
                    exit;
                }
            } else {
                $success_message = 'Product created successfully';

                // Clear form data after successful submission
                $name = '';
                $sku = '';
                $description = '';
                $price = '';
                $quantity = '';
                $category = '';
            }
        } else {
            $error_message = 'Error creating product: ' . $stmt->error;
            error_log("Database error: " . $stmt->error);
            error_log("Query: " . $query);
            error_log("Values: name=$name, sku=$sku, description=$description, price=$price, quantity=$quantity, category=$category");
        }

        $stmt->close();
    }
} else {
    // Initialize variables for GET request
    $name = '';
    $sku = '';
    $description = '';
    $price = '';
    $quantity = '';
    $category = '';
    $error_message = '';
    $success_message = '';

    // Pre-fill name if provided
    if (!empty($prefilled_name)) {
        $name = $prefilled_name;
    }
}


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Product - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">Create New Product</h1>
                <?php if ($return_to === 'invoice_create'): ?>
                    <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
                        <i class="fas fa-info-circle"></i>
                        After creating this product, you'll return to the invoice creation page.
                    </p>
                <?php else: ?>
                    <a href="inventory.php" class="btn"><i class="fas fa-arrow-left"></i> Back to Inventory</a>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>
                
                <form method="POST" action="inventory_create.php" class="needs-validation">
                    <input type="hidden" name="return_to" value="<?php echo htmlspecialchars($return_to); ?>">
                    <div class="form-group">
                        <label for="name">Product Name <span class="required">*</span></label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="sku">SKU</label>
                        <input type="text" id="sku" name="sku" value="<?php echo htmlspecialchars($sku); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="price">Price <span class="required">*</span></label>
                            <input type="number" id="price" name="price" step="0.01" min="0" value="<?php echo htmlspecialchars($price); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="quantity">Quantity</label>
                            <input type="number" id="quantity" name="quantity" min="0" value="<?php echo htmlspecialchars($quantity); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="category">Category</label>
                        <input type="text" id="category" name="category" value="<?php echo htmlspecialchars($category); ?>">
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn-primary"><i class="fas fa-save"></i> Save Product</button>
                        <?php if ($return_to === 'invoice_create'): ?>
                            <a href="invoice_create.php" class="btn-secondary"><i class="fas fa-arrow-left"></i> Back to Invoice</a>
                        <?php else: ?>
                            <a href="inventory.php" class="btn-secondary"><i class="fas fa-times"></i> Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. All rights reserved.</p>
        </div>
    </footer>
    
    <script src="js/main.js"></script>

    <?php if (!empty($success_message) && $return_to === 'invoice_create'): ?>
    <script>
        // Force redirect to invoice creation if PHP redirect failed
        setTimeout(function() {
            window.location.href = 'invoice_create.php?product_created=true';
        }, 1000);
    </script>
    <?php endif; ?>
</body>
</html>
<?php
// Close database connection
$conn->close();
?>
