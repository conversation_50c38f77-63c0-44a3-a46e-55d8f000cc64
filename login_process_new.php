<?php
ob_start();

// Configurar exibição de erros
ini_set('display_errors', 0); // Não exibir erros na tela
ini_set('log_errors', 1); // Habilitar log de erros
error_reporting(E_ALL); // Reportar todos os erros

// Criar diretório de logs se não existir
$log_dir = __DIR__ . '/logs';
if (!file_exists($log_dir)) {
    @mkdir($log_dir, 0777, true);
}
ini_set('error_log', $log_dir . '/php_errors.log'); // Definir arquivo de log

// Configurar diretório de sessões personalizado
$session_dir = __DIR__ . '/sessions';
if (!file_exists($session_dir)) {
    @mkdir($session_dir, 0777, true);
}

// Configurar opções de sessão
ini_set('session.save_path', $session_dir);
ini_set('session.gc_probability', 1);

// Tentar iniciar a sessão com tratamento de erros
try {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
} catch (Exception $e) {
    // Ignorar erros de sessão
    error_log("Erro ao iniciar sessão: " . $e->getMessage());
}

// Initialize variables
$email = isset($_POST['email']) ? $_POST['email'] : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';
$remember = isset($_POST['remember']) ? true : false;

// Validação simples
if (empty($email) || empty($password)) {
    $_SESSION['error'] = 'Please fill in all fields.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit;
}

// Incluir conexão com o banco de dados
require_once 'db_connect.php';

// Verificar se a tabela users existe
$table_exists = false;
$query = "SHOW TABLES LIKE 'users'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $table_exists = true;
}

// Verificar se a tabela user_roles existe
$roles_table_exists = false;
$query = "SHOW TABLES LIKE 'user_roles'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $roles_table_exists = true;
}

// Para compatibilidade, manter credenciais fixas como fallback
if ($email === '<EMAIL>' && $password === 'admin123') {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'administrator';

    if ($remember) {
        setcookie('user_email', $email, time() + (86400 * 30), "/");
    }

    // Redirecionar para o dashboard
    header('Location: dashboard_simple.php');
    ob_end_flush();
    exit;
}
// Verificar credenciais no banco de dados se a tabela existir
elseif ($table_exists) {
    // Primeiro, tentar login com email
    $query = "SELECT u.id, u.username, u.email, u.password, r.name as role_name 
              FROM users u 
              LEFT JOIN user_roles r ON u.role_id = r.id 
              WHERE u.email = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();

    // Se não encontrar com email, tentar com username
    if ($result->num_rows === 0) {
        $stmt->close();
        $query = "SELECT u.id, u.username, u.email, u.password, r.name as role_name 
                  FROM users u 
                  LEFT JOIN user_roles r ON u.role_id = r.id 
                  WHERE u.username = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('s', $email); // Usar o campo email como possível username
        $stmt->execute();
        $result = $stmt->get_result();
    }

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Verificar se a senha está correta
        if (password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            
            // Set user role
            if (!empty($user['role_name'])) {
                $_SESSION['user_role'] = $user['role_name'];
            } else {
                $_SESSION['user_role'] = 'user'; // Default role
            }

            // Get user permissions if roles table exists
            if ($roles_table_exists) {
                $permissions = [];
                $query = "SELECT p.name 
                          FROM permissions p 
                          JOIN role_permissions rp ON p.id = rp.permission_id 
                          JOIN user_roles r ON rp.role_id = r.id 
                          WHERE r.name = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param('s', $_SESSION['user_role']);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result && $result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        $permissions[] = $row['name'];
                    }
                }
                
                $_SESSION['user_permissions'] = $permissions;
            }

            if ($remember) {
                setcookie('user_email', $user['email'], time() + (86400 * 30), "/");
            }

            // Redirecionar para o dashboard
            header('Location: dashboard_simple.php');
            ob_end_flush();
            exit;
        }
    }

    // Se chegou aqui, as credenciais estão incorretas
    $_SESSION['error'] = 'Incorrect email/username or password.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit();
} else {
    $_SESSION['error'] = 'Incorrect email or password.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit();
}

// Fechar conexão com o banco de dados se existir
if (isset($conn)) {
    if (function_exists('db_close')) {
        db_close();
    } else {
        $conn->close();
    }
}
?>
