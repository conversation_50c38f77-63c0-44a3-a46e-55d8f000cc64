<?php
// Include simple session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Check if this is a shared link view
$is_shared = isset($_GET['shared']) && $_GET['shared'] == 1;

// Helper functions
function format_date($date) {
    return date('m/d/Y', strtotime($date));
}

function format_money($value) {
    return number_format($value, 2, '.', ',');
}

// Initialize variables
$id = 0;
$error = '';
$invoice = null;
$items = [];
$customer = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get invoice data
    $result = db_query("SELECT * FROM invoices WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $invoice = db_fetch_assoc($result);

        // Get customer data
        $customer_id = $invoice['customer_id'];
        $result = db_query("SELECT * FROM customers WHERE id = $customer_id");

        if ($result && db_num_rows($result) > 0) {
            $customer = db_fetch_assoc($result);
        }

        // Get invoice items
        $result = db_query("
            SELECT ii.*, i.name as item_name, i.sku as item_sku
            FROM invoice_items ii
            JOIN inventory i ON ii.inventory_id = i.id
            WHERE ii.invoice_id = $id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }

        // Get payment history
        $payments = [];
        $total_paid = 0;

        // Check if invoice_payments table exists
        $check_table = db_query("SHOW TABLES LIKE 'invoice_payments'");
        if (db_num_rows($check_table) > 0) {
            $payments_result = db_query("SELECT * FROM invoice_payments WHERE invoice_id = $id ORDER BY payment_date DESC");

            if ($payments_result && db_num_rows($payments_result) > 0) {
                while ($payment = db_fetch_assoc($payments_result)) {
                    $payments[] = $payment;
                    $total_paid += $payment['amount'];
                }
            }
        }

        $remaining = $invoice['total'] - $total_paid;
    } else {
        $error = 'Invoice not found.';
    }
} else {
    $error = 'Invoice ID not provided.';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #<?php echo $invoice ? htmlspecialchars($invoice['invoice_number']) : ''; ?> - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f9f9f9;
        }

        .print-container {
            max-width: 1000px;
            margin: 20px auto;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 25px;
            border-radius: 5px;
        }

        .print-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
        }

        .company-info {
            flex: 1;
        }

        .company-info h1 { color: #2c3e50; margin: 0 0 10px 0; font-size: 28px; }
        .company-info p { margin: 3px 0; color: #555; font-size: 0.9em; }

        .invoice-info { text-align: right; padding: 15px 20px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #3498db; }
        .invoice-title { font-size: 24px; font-weight: bold; margin-bottom: 15px; color: #3498db; text-transform: uppercase; letter-spacing: 1px; }
        .invoice-number { font-size: 18px; margin-bottom: 10px; font-weight: 600; }

        .customer-info { margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }

        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #3498db; border-bottom: 1px solid #eee; padding-bottom: 5px; text-transform: uppercase; letter-spacing: 0.5px; }

        .customer-details { display: flex; flex-wrap: wrap; }
        .customer-details div { flex: 1; min-width: 250px; }
        .customer-details p { margin: 4px 0; font-size: 0.9em; }
        .customer-details strong { color: #2c3e50; }

        .table-responsive { overflow-x: auto; -webkit-overflow-scrolling: touch; margin-bottom: 15px; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); font-size: 0.9em; }
        .items-table th { background-color: #3498db; color: white; padding: 8px 10px; text-align: left; font-weight: 600; }

        .items-table td { padding: 6px 10px; border-bottom: 1px solid #eee; }
        .items-table tr:nth-child(even) { background-color: #f9f9f9; }
        .items-table tr:hover { background-color: #f5f5f5; }

        .totals { width: 300px; margin-left: auto; background-color: #f8f9fa; padding: 10px 15px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); font-size: 0.9em; }
        .totals-row { display: flex; justify-content: space-between; padding: 4px 0; border-bottom: 1px solid #eee; }
        .grand-total { font-weight: bold; font-size: 16px; border-top: 2px solid #3498db; padding-top: 8px; margin-top: 8px; color: #2c3e50; }

        .notes { margin-top: 20px; border-top: 1px solid #eee; padding-top: 10px; background-color: #f8f9fa; padding: 12px; border-radius: 5px; font-size: 0.9em; }
        .payment-history { margin-top: 30px; }

        /* Print Button Styles */
        .print-button {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            background: #007bff !important;
            color: white;
            border: none;
            border-radius: 0;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            z-index: 1000;
            transition: background-color 0.3s ease;
            margin: 0;
        }

        .print-button:hover {
            background: #0056b3 !important;
        }

        /* Ajuste do body para elementos fixos */
        body {
            padding-top: 48px !important; /* Espaço para o botão de impressão */
        }

        body.shared-view {
            padding-top: 80px !important; /* Espaço adicional para o banner quando compartilhado */
        }

        /* Regras específicas para impressão - garante padding consistente */
        @media print {
            body {
                padding: 0.2in !important; /* Padding padrão para impressão */
                padding-top: 0.2in !important; /* Remove padding extra do topo na impressão */
                margin: 0 !important; /* Remove margin que pode estar interferindo */
                background-color: #fff !important;
            }

            body.shared-view {
                padding: 0.2in !important; /* Mesmo padding para visualização compartilhada */
                padding-top: 0.2in !important;
                margin: 0 !important;
            }

            /* Garante que elementos fixos não apareçam na impressão */
            .print-button,
            .no-print,
            header,
            .navbar {
                display: none !important;
            }

            .print-container {
                box-shadow: none !important;
                padding: 0 !important;
                max-width: 100% !important;
            }

            .items-table,
            .totals {
                box-shadow: none !important;
            }
        }



        /* Responsive styles for mobile devices */
        @media screen and (max-width: 768px) {
            body { padding: 10px; }
            .print-container { padding: 15px; }

            .print-header {
                flex-direction: column;
            }

            .invoice-info {
                text-align: left;
                margin-top: 15px;
                padding: 10px;
            }

            .customer-details {
                flex-direction: column;
            }

            .customer-details div {
                min-width: 100%;
                margin-bottom: 10px;
            }

            .items-table {
                font-size: 0.8em;
            }

            .items-table th, .items-table td {
                padding: 5px;
            }

            /* Make table responsive */
            .items-table {
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            /* Adjust totals section */
            .totals {
                width: 100%;
                margin-left: 0;
                box-sizing: border-box;
            }

            /* Adjust payment history and totals layout */
            div[style*="display: flex; justify-content: space-between"] {
                flex-direction: column;
            }

            .payment-history, div[style*="width: 48%"] {
                width: 100% !important;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body<?php echo $is_shared ? ' class="shared-view"' : ''; ?>>
    <!-- Print Button (always show) -->
    <button class="print-button no-print" onclick="window.print()">
        Print
    </button>

    <!-- Shared View Header -->
    <?php if ($is_shared): ?>
    <div style="background: #f8f9fa; padding: 8px 15px; text-align: center; border-bottom: 1px solid #dee2e6; margin: 0; width: 100%; position: fixed; top: 48px; left: 0; right: 0; z-index: 999;" class="no-print">
        <p style="margin: 0; color: #6c757d; font-size: 12px; font-weight: normal;">
            <i class="fas fa-share-alt" style="color: #6c757d; margin-right: 6px; font-size: 11px;"></i>
            You are viewing a shared invoice link - This invoice was shared with you by Tony's AC Repair
        </p>
    </div>
    <?php endif; ?>

    <?php if (!$is_shared): ?>
        <?php include 'includes/header_responsive.php'; ?>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="error"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if ($invoice && $customer): ?>
    <div class="print-container">
        <div class="print-header">
            <div class="company-info">
                <h1>Tony's AC Repair LLC</h1>
                <p>Orlando, Florida</p>
                <p>Phone: ************</p>
                <p>Email: <EMAIL></p>
                <p>Website: TONYSACREPAIR.COM</p>
            </div>

            <div class="invoice-info">
                <div class="invoice-title">INVOICE</div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <div class="invoice-number">#<?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                    <div style="font-weight: bold; color:
                    <?php
                    switch ($invoice['status']) {
                        case 'paid':
                            echo '#27ae60'; // green
                            $status = 'Paid';
                            break;
                        case 'unpaid':
                            echo '#e74c3c'; // red
                            $status = 'Unpaid';
                            break;
                        case 'partial':
                            echo '#f39c12'; // orange
                            $status = 'Partial';
                            break;
                        case 'cancelled':
                            echo '#95a5a6'; // gray
                            $status = 'Cancelled';
                            break;
                        default:
                            echo '#3498db'; // blue
                            $status = $invoice['status'];
                    }
                    ?>">
                        Status: <?php echo $status; ?>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <p>Date: <?php echo date('m/d/Y', strtotime($invoice['invoice_date'])); ?></p>
                    <p>Due Date: <?php echo !empty($invoice['due_date']) ? date('m/d/Y', strtotime($invoice['due_date'])) : 'Not specified'; ?></p>
                </div>
            </div>
        </div>

        <div class="customer-info">
            <div class="section-title" style="text-align: center;">👤 CUSTOMER INFORMATION</div>
            <div class="customer-details">
                <div>
                    <p><strong>Name:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                </div>
                <div>
                    <p><strong>Address:</strong> <?php echo htmlspecialchars($customer['address']); ?></p>
                    <p><strong>City:</strong> <?php echo htmlspecialchars($customer['city']); ?></p>
                    <p><strong>State/ZIP:</strong> <?php echo htmlspecialchars($customer['state']); ?> <?php echo htmlspecialchars($customer['zip']); ?></p>
                </div>
            </div>
        </div>

        <?php if (!empty($invoice['service_address'])): ?>
        <div class="customer-info" style="background-color: #e8f5e8; border-left: 4px solid #28a745;">
            <div class="section-title" style="text-align: center; color: #28a745;">📍 SERVICE ADDRESS</div>
            <div style="text-align: center; padding: 10px;">
                <p style="margin: 0; font-size: 14px; line-height: 1.5; white-space: pre-line;"><strong><?php echo htmlspecialchars($invoice['service_address']); ?></strong></p>
                <p style="margin: 5px 0 0 0; font-size: 12px; color: #6c757d; font-style: italic;">Location where the service was performed</p>
            </div>
        </div>
        <?php endif; ?>

        <div class="section-title">Invoice Items</div>

        <?php if (empty($items)): ?>
            <p>No items found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>SKU</th>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Subtotal</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $item): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                <td><?php echo htmlspecialchars($item['item_sku']); ?></td>
                                <td><?php echo htmlspecialchars($item['description']); ?></td>
                                <td><?php echo $item['quantity']; ?></td>
                                <td>$ <?php echo number_format($item['price'], 2, '.', ','); ?></td>
                                <td>$ <?php echo number_format($item['subtotal'], 2, '.', ','); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <div style="display: flex; justify-content: space-between; margin-top: 20px; margin-bottom: 20px;">
            <?php if (!empty($payments)): ?>
            <div class="payment-history" style="width: 48%;">
                <div class="section-title" style="text-align: left; font-size: 14px;">PAYMENT HISTORY</div>
                <div class="table-responsive">
                    <table class="items-table" style="margin-top: 8px; font-size: 0.85em;">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Method</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                                <tr>
                                    <td><?php echo format_date($payment['payment_date']); ?></td>
                                    <td>$ <?php echo format_money($payment['amount']); ?></td>
                                    <td>
                                        <?php
                                            $method = $payment['payment_method'];
                                            $method_labels = [
                                                'zelle' => 'Zelle',
                                                'card' => 'Card',
                                                'cash' => 'Cash',
                                                'check' => 'Check'
                                            ];
                                            echo isset($method_labels[$method]) ? $method_labels[$method] : ucfirst($method);
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>

            <div class="totals" style="width: <?php echo !empty($payments) ? '48%' : '100%'; ?>;">
                <div class="section-title" style="text-align: left; font-size: 14px;">INVOICE SUMMARY</div>
                <div class="totals-row" style="margin-top: 8px;">
                    <span>Subtotal:</span>
                    <span>$ <?php echo number_format($invoice['subtotal'], 2, '.', ','); ?></span>
                </div>

                <?php if ($invoice['tax_rate'] > 0): ?>
                <div class="totals-row">
                    <span>Tax (<?php echo number_format($invoice['tax_rate'], 2, '.', ','); ?>%):</span>
                    <span>$ <?php echo number_format($invoice['tax_amount'], 2, '.', ','); ?></span>
                </div>
                <?php endif; ?>

                <?php if ($invoice['discount_amount'] > 0): ?>
                <div class="totals-row">
                    <span>Discount:</span>
                    <span>$ <?php echo number_format($invoice['discount_amount'], 2, '.', ','); ?></span>
                </div>
                <?php endif; ?>

                <div class="totals-row grand-total">
                    <span>Total:</span>
                    <span>$ <?php echo number_format($invoice['total'], 2, '.', ','); ?></span>
                </div>

                <?php if (!empty($payments)): ?>
                <div class="totals-row" style="margin-top: 15px; border-top: 1px dashed #ddd; padding-top: 10px;">
                    <span>Total Paid:</span>
                    <span style="color: #27ae60;">$ <?php echo format_money($total_paid); ?></span>
                </div>

                <div class="totals-row" style="font-weight: bold;">
                    <span>Balance Due:</span>
                    <span style="color: <?php echo $remaining <= 0 ? '#27ae60' : '#e74c3c'; ?>;">$ <?php echo format_money($remaining); ?></span>
                </div>
                <?php endif; ?>
        </div>
        </div>


        <?php if (!empty($invoice['notes'])): ?>
            <div class="notes">
                <div class="section-title">Notes</div>
                <p><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
            </div>
        <?php endif; ?>

        <div class="no-print" style="margin-top: 15px; text-align: center;">
            <button onclick="window.print()" style="padding: 6px 12px; background-color: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 8px; font-size: 0.9em;">Print</button>
            <?php if (!$is_shared): ?>
            <button onclick="window.location.href='invoices_list.php'" style="padding: 6px 12px; background-color: #2ecc71; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 8px; font-size: 0.9em;">Back to Invoices</button>
            <?php endif; ?>
            <button onclick="window.close()" style="padding: 6px 12px; background-color: #95a5a6; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.9em;">Close</button>
        </div>
    </div> <!-- End of print-container -->
    <?php endif; ?>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically open the print dialog
            // window.print();
        };
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
