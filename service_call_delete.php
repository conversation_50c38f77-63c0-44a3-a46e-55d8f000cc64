<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = $success = '';
$service_call = null;

// Get service call data
if ($id > 0) {
    $result = db_query("
        SELECT sc.*, c.name as customer_name
        FROM service_calls sc
        JOIN customers c ON sc.customer_id = c.id
        WHERE sc.id = $id
    ");

    if ($result && db_num_rows($result) > 0) {
        $service_call = db_fetch_assoc($result);
    } else {
        $error = 'Chamado não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete']) && $service_call) {
    try {
        // Delete service call
        $query = "DELETE FROM service_calls WHERE id = $id";

        // Registrar a query para debug
        error_log("Query: " . $query);

        if (db_query($query)) {
            $success = 'Chamado de atendimento excluído com sucesso!';
            // Redirect to service calls list after 2 seconds
            header("refresh:2;url=service_calls.php");
        } else {
            $error = 'Erro ao excluir chamado: ' . db_error();
            error_log("Erro ao executar query: " . db_error());
        }
    } catch (Exception $e) {
        $error = 'Erro ao processar a exclusão: ' . $e->getMessage();
        error_log("Exceção: " . $e->getMessage());
    }
}

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excluir Chamado de Atendimento - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .delete-confirmation {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8d7da;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .delete-confirmation h2 {
            color: #721c24;
        }

        .delete-confirmation p {
            margin-bottom: 20px;
        }

        .delete-info {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: left;
        }

        .delete-info p {
            margin: 5px 0;
        }

        .delete-actions {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tony's AC Repair</div>
                <ul class="nav-links">
                    <li><a href="dashboard_simple.php">Painel</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                    <li><a href="service_calls.php" class="active">Chamados</a></li>
                </ul>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-trash-alt"></i> Excluir Chamado de Atendimento</h1>
                <a href="service_calls.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar</a>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php if ($error === 'Chamado não encontrado.' || $error === 'ID inválido.'): ?>
                    <p><a href="service_calls.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Chamados</a></p>
                <?php endif; ?>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if ($service_call && empty($success)): ?>
                <div class="delete-confirmation">
                    <h2><i class="fas fa-exclamation-triangle"></i> Confirmação de Exclusão</h2>
                    <p>Você está prestes a excluir o seguinte chamado de atendimento:</p>

                    <div class="delete-info">
                        <p><strong>ID:</strong> <?php echo $service_call['id']; ?></p>
                        <p><strong>Cliente:</strong> <?php echo htmlspecialchars($service_call['customer_name']); ?></p>
                        <p><strong>Título:</strong> <?php echo htmlspecialchars($service_call['title']); ?></p>
                        <p><strong>Data Agendada:</strong> <?php echo format_date($service_call['scheduled_date']); ?></p>
                    </div>

                    <p><strong>Esta ação não pode ser desfeita. Deseja continuar?</strong></p>

                    <div class="delete-actions">
                        <form method="POST" action="service_call_delete.php?id=<?php echo $id; ?>">
                            <input type="hidden" name="confirm_delete" value="1">
                            <button type="submit" class="btn btn-danger"><i class="fas fa-trash-alt"></i> Sim, Excluir</button>
                        </form>
                        <a href="service_calls.php" class="btn btn-secondary"><i class="fas fa-times"></i> Cancelar</a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Menu toggle para dispositivos móveis
            $('.menu-toggle').click(function() {
                $('.nav-links').toggleClass('active');
            });
        });
    </script>
</body>
</html>
