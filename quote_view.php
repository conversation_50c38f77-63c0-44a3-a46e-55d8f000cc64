<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = 0;
$error = '';
$quote = null;
$items = [];
$customer = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get quote data
    $result = db_query("SELECT * FROM quotes WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);

        // Get customer data
        $customer_id = $quote['customer_id'];
        $result = db_query("SELECT * FROM customers WHERE id = $customer_id");

        if ($result && db_num_rows($result) > 0) {
            $customer = db_fetch_assoc($result);
        }

        // Get quote items
        $result = db_query("
            SELECT qi.*, i.name as item_name, i.sku as item_sku
            FROM quote_items qi
            JOIN inventory i ON qi.inventory_id = i.id
            WHERE qi.quote_id = $id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }
    } else {
        $error = 'Orçamento não encontrado.';
    }
} else {
    $error = 'ID do orçamento não fornecido.';
}

// Update quote status if requested
if (isset($_GET['action']) && $quote) {
    $action = $_GET['action'];
    $status = '';

    switch ($action) {
        case 'approve':
            $status = 'approved';
            break;
        case 'reject':
            $status = 'rejected';
            break;
        case 'expire':
            $status = 'expired';
            break;
        case 'pending':
            $status = 'pending';
            break;
    }

    if (!empty($status)) {
        if (db_query("UPDATE quotes SET status = '$status' WHERE id = $id")) {
            $quote['status'] = $status;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Orçamento - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($quote && $customer): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">Orçamento <?php
                            $quote_number = $quote['quote_number'];
                            // Primeiro, procurar por qualquer padrão #YYMMDD-XXX no texto
                            if (preg_match('/#\d{6}-\d{3}/', $quote_number, $matches)) {
                                echo htmlspecialchars($matches[0]);
                            } else if (strpos($quote_number, "\n") !== false) {
                                $parts = explode("\n", $quote_number);
                                $last_part = trim(end($parts));
                                echo htmlspecialchars($last_part);
                            } else {
                                echo htmlspecialchars($quote_number);
                            }
                        ?></h1>
                        <div class="action-buttons">
                            <a href="quotes.php" class="btn">Voltar para Lista</a>
                            <a href="quote_edit.php?id=<?php echo $id; ?>" class="btn btn-secondary">Editar</a>
                            <a href="quote_print.php?id=<?php echo $id; ?>" class="btn" target="_blank">Imprimir</a>
                            <?php if ($quote['status'] === 'pending' || $quote['status'] === 'approved'): ?>
                                <a href="invoice_create.php?quote_id=<?php echo $id; ?>" class="btn">Gerar Fatura</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="quote-status">
                            <strong>Status:</strong>
                            <?php
                            $status = '';
                            switch ($quote['status']) {
                                case 'pending':
                                    $status = '<span class="status-pending">Pendente</span>';
                                    break;
                                case 'approved':
                                    $status = '<span class="status-approved">Aprovado</span>';
                                    break;
                                case 'rejected':
                                    $status = '<span class="status-rejected">Rejeitado</span>';
                                    break;
                                case 'expired':
                                    $status = '<span class="status-expired">Expirado</span>';
                                    break;
                                default:
                                    $status = $quote['status'];
                            }
                            echo $status;
                            ?>

                            <div class="status-actions">
                                <a href="quote_view.php?id=<?php echo $id; ?>&action=approve" class="btn">Aprovar</a>
                                <a href="quote_view.php?id=<?php echo $id; ?>&action=reject" class="btn btn-danger">Rejeitar</a>
                                <a href="quote_view.php?id=<?php echo $id; ?>&action=expire" class="btn btn-secondary">Expirar</a>
                                <a href="quote_view.php?id=<?php echo $id; ?>&action=pending" class="btn">Marcar como Pendente</a>
                            </div>
                        </div>

                        <div class="quote-details">
                            <div class="row">
                                <div class="col">
                                    <h3>Informações do Cliente</h3>
                                    <p><strong>Nome:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                                    <p><strong>Email:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                                    <p><strong>Telefone:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                                    <p><strong>Endereço:</strong> <?php echo htmlspecialchars($customer['address']); ?></p>
                                    <p><strong>Cidade:</strong> <?php echo htmlspecialchars($customer['city']); ?></p>
                                    <p><strong>Estado:</strong> <?php echo htmlspecialchars($customer['state']); ?></p>
                                    <p><strong>CEP:</strong> <?php echo htmlspecialchars($customer['zip']); ?></p>
                                </div>

                                <div class="col">
                                    <h3>Informações do Orçamento</h3>
                                    <p><strong>Número:</strong><br><?php
                                        $quote_number = $quote['quote_number'];
                                        // Primeiro, procurar por qualquer padrão #YYMMDD-XXX no texto
                                        if (preg_match('/#\d{6}-\d{3}/', $quote_number, $matches)) {
                                            echo htmlspecialchars($matches[0]);
                                        } else if (strpos($quote_number, "\n") !== false) {
                                            $parts = explode("\n", $quote_number);
                                            $last_part = trim(end($parts));
                                            echo htmlspecialchars($last_part);
                                        } else {
                                            echo htmlspecialchars($quote_number);
                                        }
                                    ?></p>
                                    <p><strong>Data:</strong> <?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></p>
                                    <p><strong>Válido Até:</strong> <?php echo !empty($quote['valid_until']) ? date('d/m/Y', strtotime($quote['valid_until'])) : 'Não especificado'; ?></p>
                                    <p><strong>Data de Criação:</strong> <?php echo date('d/m/Y H:i', strtotime($quote['created_at'])); ?></p>
                                    <p><strong>Última Atualização:</strong> <?php echo date('d/m/Y H:i', strtotime($quote['updated_at'])); ?></p>
                                </div>
                            </div>

                            <h3>Itens do Orçamento</h3>

                            <?php if (empty($items)): ?>
                                <p>Nenhum item encontrado.</p>
                            <?php else: ?>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Produto</th>
                                            <th>SKU</th>
                                            <th>Descrição</th>
                                            <th>Quantidade</th>
                                            <th>Preço Unitário</th>
                                            <th>Subtotal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($items as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                                <td><?php echo htmlspecialchars($item['item_sku']); ?></td>
                                                <td><?php echo htmlspecialchars($item['description']); ?></td>
                                                <td><?php echo $item['quantity']; ?></td>
                                                <td>R$ <?php echo number_format($item['price'], 2, ',', '.'); ?></td>
                                                <td>R$ <?php echo number_format($item['subtotal'], 2, ',', '.'); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            <?php endif; ?>

                            <div class="totals-section">
                                <div class="totals-row">
                                    <span class="totals-label">Subtotal:</span>
                                    <span class="totals-value">R$ <?php echo number_format($quote['subtotal'], 2, ',', '.'); ?></span>
                                </div>

                                <?php if ($quote['tax_rate'] > 0): ?>
                                    <div class="totals-row">
                                        <span class="totals-label">Taxa (<?php echo number_format($quote['tax_rate'], 2, ',', '.'); ?>%):</span>
                                        <span class="totals-value">R$ <?php echo number_format($quote['tax_amount'], 2, ',', '.'); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($quote['discount_amount'] > 0): ?>
                                    <div class="totals-row">
                                        <span class="totals-label">Desconto:</span>
                                        <span class="totals-value">R$ <?php echo number_format($quote['discount_amount'], 2, ',', '.'); ?></span>
                                    </div>
                                <?php endif; ?>

                                <div class="totals-row grand-total">
                                    <span class="totals-label">Total:</span>
                                    <span class="totals-value">R$ <?php echo number_format($quote['total'], 2, ',', '.'); ?></span>
                                </div>
                            </div>

                            <?php if (!empty($quote['notes'])): ?>
                                <div class="notes-section">
                                    <h3>Observações</h3>
                                    <p><?php echo nl2br(htmlspecialchars($quote['notes'])); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
