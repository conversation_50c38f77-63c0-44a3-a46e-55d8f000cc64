<?php
// Use simple session fix for cPanel hosting
require_once 'simple_session_fix.php';

// Bypass security check to avoid header issues
define('BYPASS_SECURITY', true);

// Include database connection
require_once 'db_connect.php';

// Simple authentication check
$authenticated = false;
if (isset($_SESSION['user_id']) || isset($_COOKIE['user_logged_in']) ||
    (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], $_SERVER['HTTP_HOST']) !== false)) {
    $authenticated = true;
}

if (!$authenticated) {
    // Instead of redirect, show error message
    die('<h1>Access Denied</h1><p>You must be logged in to access this page.</p><a href="login.php">Login</a>');
}

// Initialize variables
$id = 0;
$error = $success = '';
$item = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    // Get inventory data
    $result = db_query("SELECT * FROM inventory WHERE id = $id");
    
    if ($result && db_num_rows($result) > 0) {
        $item = db_fetch_assoc($result);
    } else {
        $error = 'Produto não encontrado.';
    }
} else {
    $error = 'ID do produto não fornecido.';
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    $id = intval($_POST['id']);
    
    // Check if item is used in quotes or invoices
    $result = db_query("SELECT COUNT(*) as count FROM quote_items WHERE inventory_id = $id");
    $row = db_fetch_assoc($result);
    $quote_items_count = $row['count'];
    
    $result = db_query("SELECT COUNT(*) as count FROM invoice_items WHERE inventory_id = $id");
    $row = db_fetch_assoc($result);
    $invoice_items_count = $row['count'];
    
    if ($quote_items_count > 0 || $invoice_items_count > 0) {
        $error = 'Não é possível excluir este produto pois ele está sendo usado em orçamentos ou faturas.';
    } else {
        // Delete inventory item
        if (db_query("DELETE FROM inventory WHERE id = $id")) {
            $success = 'Produto excluído com sucesso!';
            $item = null; // Clear item data
        } else {
            $error = 'Erro ao excluir produto: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excluir Produto - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tonys AC Repair</div>
                <ul class="nav-links">
                    <li><a href="index.php">Início</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                    <p><a href="inventory.php" class="btn">Voltar para Lista de Produtos</a></p>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">Excluir Produto</h1>
                        <a href="inventory.php" class="btn">Voltar para Lista</a>
                    </div>
                    <div class="card-body">
                        <?php if ($item): ?>
                            <div class="alert alert-danger">
                                <p><strong>Atenção!</strong> Você está prestes a excluir o produto abaixo. Esta ação não pode ser desfeita.</p>
                            </div>
                            
                            <div class="item-details">
                                <p><strong>Nome:</strong> <?php echo htmlspecialchars($item['name']); ?></p>
                                <p><strong>SKU:</strong> <?php echo htmlspecialchars($item['sku']); ?></p>
                                <p><strong>Preço:</strong> R$ <?php echo number_format($item['price'], 2, ',', '.'); ?></p>
                                <p><strong>Quantidade em Estoque:</strong> <?php echo $item['quantity']; ?></p>
                                <p><strong>Categoria:</strong> <?php echo htmlspecialchars($item['category']); ?></p>
                                <p><strong>Descrição:</strong> <?php echo htmlspecialchars($item['description']); ?></p>
                            </div>
                            
                            <form method="POST" action="inventory_delete.php">
                                <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <button type="submit" name="confirm_delete" class="btn btn-danger">Confirmar Exclusão</button>
                                <a href="inventory.php" class="btn">Cancelar</a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
