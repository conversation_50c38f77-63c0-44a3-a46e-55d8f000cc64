<?php
// Include database connection
require_once 'db_connect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if ID parameter is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Product ID is required']);
    exit;
}

$product_id = intval($_GET['id']);

try {
    // Query to get product by ID
    $query = "SELECT id, name, sku, description, price, quantity, category 
              FROM inventory 
              WHERE id = ? 
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    if (!$stmt) {
        throw new Exception('Database prepare error: ' . $conn->error);
    }
    
    $stmt->bind_param('i', $product_id);
    
    if (!$stmt->execute()) {
        throw new Exception('Database execute error: ' . $stmt->error);
    }
    
    $result = $stmt->get_result();
    $product = $result->fetch_assoc();
    
    if ($product) {
        // Convert price to float for consistency
        $product['price'] = floatval($product['price']);
        $product['quantity'] = intval($product['quantity']);
        
        echo json_encode($product);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Product not found']);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    error_log('Error in get_product.php: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

$conn->close();
?>
