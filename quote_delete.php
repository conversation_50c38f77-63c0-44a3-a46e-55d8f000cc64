<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$confirm = isset($_GET['confirm']) ? $_GET['confirm'] : '';
$error = '';
$success = '';

// Get quote data
if ($id > 0) {
    $result = db_query("SELECT * FROM quotes WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);
    } else {
        $error = 'Orçamento não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}

// Handle deletion
if ($id > 0 && $confirm === 'yes') {
    // Start transaction
    db_begin_transaction();
    $transaction_success = true;

    // Delete quote items first
    if (!db_query("DELETE FROM quote_items WHERE quote_id = $id")) {
        $transaction_success = false;
        $error = 'Erro ao excluir itens do orçamento: ' . db_error();
    }

    // Delete quote
    if ($transaction_success) {
        if (!db_query("DELETE FROM quotes WHERE id = $id")) {
            $transaction_success = false;
            $error = 'Erro ao excluir orçamento: ' . db_error();
        }
    }

    // Commit or rollback transaction
    if ($transaction_success) {
        db_commit();
        $success = 'Orçamento excluído com sucesso!';
    } else {
        db_rollback();
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excluir Orçamento - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .card-body {
            padding: 20px;
        }

        .card-title {
            margin: 0;
            font-size: 1.5rem;
            color: #333;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 8px 15px;
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 1rem;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .confirmation-box {
            text-align: center;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .confirmation-box p {
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .confirmation-box .quote-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #eee;
        }

        .confirmation-box .quote-info p {
            margin: 5px 0;
            font-size: 1rem;
        }

        .confirmation-box .buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <p><a href="quotes.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Orçamentos</a></p>
            <?php elseif (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <p><a href="quotes.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Orçamentos</a></p>
            <?php elseif (isset($quote) && $confirm !== 'yes'): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title"><i class="fas fa-trash"></i> Excluir Orçamento</h1>
                    </div>
                    <div class="card-body">
                        <div class="confirmation-box">
                            <p><i class="fas fa-exclamation-triangle" style="color: #dc3545; font-size: 2rem;"></i></p>
                            <p>Tem certeza que deseja excluir este orçamento?</p>
                            <p>Esta ação não pode ser desfeita.</p>

                            <div class="quote-info">
                                <p><strong>Número:</strong> <?php echo htmlspecialchars($quote['quote_number']); ?></p>
                                <p><strong>Data:</strong> <?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></p>
                                <p><strong>Total:</strong> $<?php echo number_format($quote['total'], 2); ?></p>
                            </div>

                            <div class="buttons">
                                <a href="quotes.php" class="btn btn-secondary"><i class="fas fa-times"></i> Cancelar</a>
                                <a href="quote_delete.php?id=<?php echo $id; ?>&confirm=yes" class="btn btn-danger"><i class="fas fa-trash"></i> Sim, Excluir</a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>
</body>
</html>
<?php
// Close database connection
db_close();
?>
