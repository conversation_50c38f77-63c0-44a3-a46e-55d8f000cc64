<?php
// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// Start output buffering
ob_start();

// Set content type to JSON
header('Content-Type: application/json');

// Check if there's a search term
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

if (empty($search)) {
    ob_clean();
    echo json_encode([]);
    exit;
}

// Database connection parameters
$db_host = 'localhost';
$db_name = 'tonyoli_NOVO';
$db_user = 'tonyoli_NOVO';
$db_pass = 'Novos';

try {
    // Create mysqli connection
    $mysqli = new mysqli($db_host, $db_user, $db_pass, $db_name);
    
    // Check connection
    if ($mysqli->connect_error) {
        ob_clean();
        echo json_encode(['error' => 'Database connection failed']);
        exit;
    }
    
    // Set charset
    $mysqli->set_charset("utf8mb4");
    
    // Escape search term
    $search_escaped = $mysqli->real_escape_string($search);
    
    // Query to search items with word position priority
    $query = "SELECT id, name, price, sku, description, quantity,
              CASE
                  WHEN name REGEXP '^$search_escaped' THEN 1
                  WHEN name REGEXP '^[^ ]+ $search_escaped' THEN 2
                  WHEN name REGEXP '^[^ ]+ [^ ]+ $search_escaped' THEN 3
                  WHEN name REGEXP '^[^ ]+ [^ ]+ [^ ]+ $search_escaped' THEN 4
                  WHEN name REGEXP '^[^ ]+ [^ ]+ [^ ]+ [^ ]+ $search_escaped' THEN 5
                  WHEN name LIKE '%$search_escaped%' THEN 6
                  WHEN sku LIKE '$search_escaped%' THEN 7
                  WHEN description LIKE '%$search_escaped%' THEN 8
                  ELSE 9
              END as priority
              FROM inventory
              WHERE name LIKE '%$search_escaped%'
              OR sku LIKE '%$search_escaped%'
              OR description LIKE '%$search_escaped%'
              ORDER BY priority ASC, name ASC
              LIMIT 10";
    
    $result = $mysqli->query($query);
    
    if (!$result) {
        ob_clean();
        echo json_encode(['error' => 'Query error: ' . $mysqli->error]);
        exit;
    }
    
    $items = [];
    
    while ($row = $result->fetch_assoc()) {
        $items[] = [
            'id' => (int)$row['id'],
            'name' => $row['name'] ?? '',
            'price' => floatval($row['price']),
            'sku' => $row['sku'] ?? '',
            'description' => $row['description'] ?? '',
            'quantity' => (int)($row['quantity'] ?? 0)
        ];
    }
    
    ob_clean();
    echo json_encode($items);
    
} catch (Exception $e) {
    ob_clean();
    echo json_encode(['error' => 'Internal error occurred']);
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}

ob_end_flush();
?>
