# Arquitetura do Sistema de Compartilhamento

## 📊 Fluxo de Funcionamento

```
┌─────────────────────────────────────────────────────────────────┐
│                    FATURA CRIADA COM SUCESSO                   │
│                     (Modal de Sucesso)                         │
└─────────────────────┬───────────────────────────────────────────┘
                      │
                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                 USUÁRIO ESCOLHE AÇÃO                           │
├─────────────────────┬───────────────────┬───────────────────────┤
│  Compartilhar       │   Enviar Email    │   Outras Opções       │
│  Fatura             │                   │   (Ver/Imprimir)      │
└─────────────────────┼───────────────────┼───────────────────────┘
                      │                   │
                      ▼                   ▼
┌─────────────────────────────────────────┐ ┌─────────────────────┐
│        DETECÇÃO DE DISPOSITIVO          │ │   MODAL DE EMAIL    │
├─────────────────────┬───────────────────┤ │                     │
│      Mobile         │     Desktop       │ │  • Email cliente    │
│   (Web Share API)   │  (Modal Custom)   │ │  • Assunto          │
└─────────────────────┼───────────────────┘ │  • Mensagem         │
                      │                     └─────────────────────┘
                      ▼                               │
┌─────────────────────────────────────────┐           │
│           OPÇÕES DE COMPARTILHAMENTO    │           │
├─────────────┬─────────────┬─────────────┤           │
│  WhatsApp   │    SMS      │ Copiar Link │           │
└─────────────┼─────────────┼─────────────┘           │
              │             │                         │
              ▼             ▼                         ▼
┌─────────────────────────────────────────┐ ┌─────────────────────┐
│      generate_share_link.php            │ │  send_invoice_pdf.php│
│                                         │ │                     │
│  • Cria token único                     │ │  • Valida email     │
│  • Verifica se link já existe           │ │  • Gera email HTML  │
│  • Define expiração (30 dias)           │ │  • Envia via mail() │
│  • Salva no banco (shared_links)        │ │  • Inclui link      │
│  • Retorna URL segura                   │ │  • Log do envio     │
└─────────────────────────────────────────┘ └─────────────────────┘
              │                                       │
              ▼                                       ▼
┌─────────────────────────────────────────┐ ┌─────────────────────┐
│           RESULTADO FINAL               │ │    EMAIL ENVIADO    │
│                                         │ │                     │
│  WhatsApp: wa.me/?text=mensagem+link    │ │  Para: cliente@...  │
│  SMS: sms:?body=mensagem+link           │ │  Assunto: Fatura #  │
│  Clipboard: Link copiado                │ │  Conteúdo: HTML     │
└─────────────────────────────────────────┘ └─────────────────────┘
```

## 🔧 Arquivos e Suas Responsabilidades

### 📄 **generate_share_link.php**
**Função:** Gerar links seguros para compartilhamento
**Usado por:** WhatsApp, SMS, Copiar Link
**Características:**
- ✅ Cria tokens únicos de 64 caracteres
- ✅ Verifica se link já existe (reutiliza)
- ✅ Define expiração automática (30 dias)
- ✅ Salva no banco de dados (shared_links)
- ✅ Retorna URL segura para view_shared.php

### 📧 **send_invoice_pdf.php**
**Função:** Enviar fatura por email
**Usado por:** Modal "Enviar por Email"
**Características:**
- ✅ Valida endereço de email
- ✅ Busca dados da fatura no banco
- ✅ Gera email HTML profissional
- ✅ Inclui link para visualização
- ✅ Envia via função mail() do PHP
- ✅ Registra log do envio

### 🔍 **get_invoice_customer_email.php**
**Função:** Buscar email do cliente
**Usado por:** Preenchimento automático do modal de email
**Características:**
- ✅ Busca email do cliente da fatura
- ✅ Retorna dados em JSON
- ✅ Usado para UX melhorada

## 🎯 Por Que Ambos São Necessários?

### **Cenário 1: Compartilhamento Rápido (WhatsApp/SMS)**
```
generate_share_link.php → Link seguro → App nativo
```
- **Vantagem:** Rápido, direto, usa apps nativos
- **Segurança:** Token único, expiração automática
- **UX:** Um clique e pronto

### **Cenário 2: Email Profissional**
```
send_invoice_pdf.php → Email HTML → Caixa de entrada
```
- **Vantagem:** Email profissional formatado
- **Personalização:** Assunto e mensagem customizáveis
- **Registro:** Log completo do envio

## 🔄 Fluxos de Uso Reais

### **Mobile (Usuário no celular):**
1. Cria fatura → Modal aparece
2. Clica "Compartilhar" → Web Share API nativa
3. Escolhe WhatsApp → generate_share_link.php → WhatsApp abre
4. Envia para cliente → Cliente recebe link seguro

### **Desktop (Usuário no computador):**
1. Cria fatura → Modal aparece
2. Clica "Compartilhar" → Modal personalizado
3. Escolhe "Enviar Email" → send_invoice_pdf.php
4. Email enviado → Cliente recebe email profissional

## ✅ Conclusão

**AMBOS os arquivos são essenciais:**
- `generate_share_link.php` = Compartilhamento moderno e seguro
- `send_invoice_pdf.php` = Email profissional tradicional

Eles trabalham juntos para oferecer a melhor experiência possível em diferentes cenários de uso!
