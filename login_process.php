<?php
ob_start();

// Configurar exibição de erros
ini_set('display_errors', 0); // Não exibir erros na tela
ini_set('log_errors', 1); // Habilitar log de erros
error_reporting(E_ALL); // Reportar todos os erros

// Criar diretório de logs se não existir
$log_dir = __DIR__ . '/logs';
if (!file_exists($log_dir)) {
    @mkdir($log_dir, 0777, true);
}
ini_set('error_log', $log_dir . '/php_errors.log'); // Definir arquivo de log

// Include simple session fix
require_once 'simple_session_fix.php';

// Initialize variables
$email = isset($_POST['email']) ? $_POST['email'] : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';
$remember = isset($_POST['remember']) ? true : false;

// Validação simples
if (empty($email) || empty($password)) {
    $_SESSION['error'] = 'Please fill in all fields.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit;
}

// Incluir conexão com o banco de dados
require_once 'db_connect.php';

// Verificar se a tabela users existe
$table_exists = false;
$query = "SHOW TABLES LIKE 'users'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $table_exists = true;
}

// Para compatibilidade, manter credenciais fixas como fallback
if ($email === '<EMAIL>' && $password === 'admin') {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
    $_SESSION['user_role'] = 'admin';

    if ($remember) {
        setcookie('user_email', $email, time() + (86400 * 30), "/");
    }

    // Redirecionar para o dashboard
    header('Location: dashboard_simple.php');
    ob_end_flush();
    exit;
}
// Verificar credenciais no banco de dados se a tabela existir
elseif ($table_exists) {
    // Primeiro, tentar login com email
    $query = "SELECT u.id, u.username, u.email, u.password, u.role_id, ur.name as role_name
              FROM users u
              LEFT JOIN user_roles ur ON u.role_id = ur.id
              WHERE u.email = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();

    // Se não encontrar com email, tentar com username
    if ($result->num_rows === 0) {
        $stmt->close();
        $query = "SELECT u.id, u.username, u.email, u.password, u.role_id, ur.name as role_name
                  FROM users u
                  LEFT JOIN user_roles ur ON u.role_id = ur.id
                  WHERE u.username = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('s', $email); // Usar o campo email como possível username
        $stmt->execute();
        $result = $stmt->get_result();
    }

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Verificar se a senha está correta
        if (password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];

            // Definir role do usuário
            if (!empty($user['role_name'])) {
                $_SESSION['user_role'] = $user['role_name'];
            } else {
                $_SESSION['user_role'] = 'user'; // Definir role padrão se não tiver
            }

            // Carregar permissões do usuário
            $permissions = [];

            // Verificar se as tabelas de permissões existem
            $permissions_table_exists = false;
            $role_permissions_table_exists = false;

            $check_query = "SHOW TABLES LIKE 'permissions'";
            $check_result = $conn->query($check_query);
            if ($check_result && $check_result->num_rows > 0) {
                $permissions_table_exists = true;
            }

            $check_query = "SHOW TABLES LIKE 'role_permissions'";
            $check_result = $conn->query($check_query);
            if ($check_result && $check_result->num_rows > 0) {
                $role_permissions_table_exists = true;
            }

            // Não precisamos mais carregar todas as permissões na sessão
            // As permissões serão verificadas dinamicamente com base no papel do usuário
            // Isso evita que usuários com papéis diferentes tenham as mesmas permissões

            // Apenas para compatibilidade com código existente, carregamos algumas permissões básicas
            // com base no papel do usuário
            $permissions = [];

            // Definir permissões com base no papel do usuário
            if (strtolower($user['role_name'] ?? '') === 'administrator') {
                // Administrador tem todas as permissões, mas não precisamos carregá-las todas
                // A função user_has_permission() já lida com isso
                $permissions = ['manage_users', 'manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports', 'system_settings'];
            } else if (strtolower($user['role_name'] ?? '') === 'manager') {
                // Gerente tem permissões específicas
                $permissions = ['manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports'];
            } else if (strtolower($user['role_name'] ?? '') === 'user') {
                // Usuário regular tem permissões limitadas
                $permissions = ['manage_customers', 'manage_quotes', 'manage_invoices'];
            }

            $_SESSION['user_permissions'] = $permissions;

            if ($remember) {
                setcookie('user_email', $user['email'], time() + (86400 * 30), "/");
            }

            // Redirecionar para o dashboard
            header('Location: dashboard_simple.php');
            ob_end_flush();
            exit;
        }
    }

    // Se chegou aqui, as credenciais estão incorretas
    $_SESSION['error'] = 'Incorrect email/username or password.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit();
} else {
    $_SESSION['error'] = 'Incorrect email or password.';
    header('Location: login_modern.php');
    ob_end_flush();
    exit();
}

// Fechar conexão com o banco de dados se existir
if (isset($conn)) {
    if (function_exists('db_close')) {
        db_close();
    } else {
        $conn->close();
    }
}
?>