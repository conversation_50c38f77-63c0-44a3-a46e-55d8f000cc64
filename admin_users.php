<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Verificar se estamos usando o modo bypass
$bypass_auth = isset($_GET['bypass']) && $_GET['bypass'] === 'true';

if (!$bypass_auth) {
    // Include session check
    require_once 'check_session.php';

    // Include permissions helper
    require_once 'includes/permissions.php';

    // Verificar se o usuário é administrador
    if (!user_has_role('administrator')) {
        // Log the access attempt
        error_log("Access attempt to admin_users.php by non-admin user: {$_SESSION['username']} with role {$_SESSION['user_role']}");
        header('Location: dashboard_simple.php');
        exit;
    }
} else {
    // Modo bypass - exibir aviso
    echo '<div style="background-color: #fff3cd; color: #856404; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #ffeeba;">
            <strong>Atenção!</strong> Você está acessando esta página no modo bypass, ignorando as verificações de permissão.
          </div>';
}

// Initialize variables
$users = [];
$message = '';
$message_type = '';

// No modo bypass, sempre permitir acesso
if ($bypass_auth) {
    $admin_exists = true;
    $is_admin = true;
    $allow_access = true;
} else {
    // Check if admin user exists
    $admin_exists = false;

    // Primeiro, verificar se a coluna 'name' existe na tabela user_roles
    $check_column = $conn->query("SHOW COLUMNS FROM user_roles LIKE 'name'");
    if ($check_column && $check_column->num_rows > 0) {
        // Usar a consulta original se a coluna existir
        $query = "SELECT 1 FROM users u
                  JOIN user_roles ur ON u.role_id = ur.id
                  WHERE ur.name = 'administrator'
                  LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    } else {
        // Usar uma consulta alternativa se a coluna não existir
        // Verificar se há algum usuário com role_id = 1 (assumindo que 1 é o ID do administrador)
        $query = "SELECT 1 FROM users WHERE role_id = 1 LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    }

    // Check if user is admin or if no admin exists yet
    $is_admin = user_has_role('administrator');
    $allow_access = $is_admin || !$admin_exists;

    // If access is not allowed, redirect to dashboard
    if (!$allow_access) {
        header('Location: dashboard_simple_fixed.php');
        exit;
    }
}

// Handle user deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $user_id = (int)$_GET['delete'];

    // Don't allow deleting the admin user
    $query = "SELECT u.username FROM users u
              JOIN user_roles ur ON u.role_id = ur.id
              WHERE u.id = ? AND ur.name = 'administrator'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $message = "Administrator users cannot be deleted.";
        $message_type = 'error';
    } else {
        // Delete the user
        $query = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('i', $user_id);

        if ($stmt->execute() && $stmt->affected_rows > 0) {
            $message = "User deleted successfully.";
            $message_type = 'success';
        } else {
            $message = "Error deleting user: " . $conn->error;
            $message_type = 'error';
        }
    }
}

// Get list of users with their roles
$query = "SELECT u.id, u.username, u.email, u.created_at, ur.name as role_name
          FROM users u
          LEFT JOIN user_roles ur ON u.role_id = ur.id
          ORDER BY u.username";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Administration</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-actions {
            display: flex;
            gap: 10px;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th, .admin-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .admin-table tr:hover {
            background-color: #f8f9fa;
        }

        .user-actions {
            display: flex;
            gap: 10px;
        }

        .action-edit, .action-delete {
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .action-edit {
            background-color: #e9ecef;
            color: #495057;
        }

        .action-delete {
            background-color: #f8d7da;
            color: #721c24;
        }

        .action-edit:hover {
            background-color: #dee2e6;
        }

        .action-delete:hover {
            background-color: #f5c6cb;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            font-size: 18px;
            margin-bottom: 20px;
        }

        .admin-notice {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .role-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .role-administrator {
            background-color: #cce5ff;
            color: #004085;
        }

        .role-manager {
            background-color: #d4edda;
            color: #155724;
        }

        .role-user {
            background-color: #e9ecef;
            color: #495057;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">User Administration</h1>
                    <div class="admin-actions">
                        <a href="admin_user_add.php" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Add New User
                        </a>
                        <a href="admin_roles.php" class="btn btn-secondary">
                            <i class="fas fa-user-tag"></i> Manage Roles
                        </a>
                    </div>
                </div>

                <?php if (!$admin_exists): ?>
                    <div class="admin-notice">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Notice:</strong> No administrator account found. Please create an admin user to secure your system.
                    </div>
                <?php endif; ?>

                <?php if (!empty($message)): ?>
                    <div class="message <?php echo $message_type === 'success' ? 'message-success' : 'message-error'; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Manage Users</h2>
                    </div>
                    <div class="admin-card-body">
                        <?php if (empty($users)): ?>
                            <div class="empty-state">
                                <i class="fas fa-users"></i>
                                <p>No users found in the system.</p>
                                <a href="admin_user_add.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> Add First User
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo $user['id']; ?></td>
                                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td>
                                                    <?php
                                                    $role = $user['role_name'] ?? 'user';
                                                    $role_class = 'role-' . strtolower($role);
                                                    ?>
                                                    <span class="role-badge <?php echo $role_class; ?>">
                                                        <?php echo htmlspecialchars($role); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('m/d/Y', strtotime($user['created_at'])); ?></td>
                                                <td>
                                                    <div class="user-actions">
                                                        <a href="admin_user_edit.php?id=<?php echo $user['id']; ?>" class="action-edit">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a>
                                                        <?php if (strtolower($user['role_name']) !== 'administrator'): ?>
                                                            <a href="admin_users.php?delete=<?php echo $user['id']; ?>" class="action-delete" onclick="return confirm('Are you sure you want to delete this user?');">
                                                                <i class="fas fa-trash-alt"></i> Delete
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
