<?php
/**
 * Strict permissions system that completely ignores the existing permissions system
 * and implements a simple, direct role-based permission check.
 */

/**
 * Check if the current user has a specific permission
 *
 * @param string $permission The permission name to check
 * @return bool True if the user has the permission, false otherwise
 */
function strict_has_permission($permission) {
    // Get the user's role from session
    $role = isset($_SESSION['user_role']) ? strtolower($_SESSION['user_role']) : '';
    
    // Define permissions for each role
    $admin_permissions = [
        'manage_users',
        'manage_customers',
        'manage_inventory',
        'manage_quotes',
        'manage_invoices',
        'view_reports',
        'system_settings'
    ];
    
    $manager_permissions = [
        'manage_customers',
        'manage_inventory',
        'manage_quotes',
        'manage_invoices',
        'view_reports'
    ];
    
    $user_permissions = [
        'manage_customers',
        'manage_quotes',
        'manage_invoices'
    ];
    
    // Check permissions based on role
    if ($role === 'administrator' || $role === 'admin') {
        return in_array($permission, $admin_permissions);
    } else if ($role === 'manager') {
        return in_array($permission, $manager_permissions);
    } else if ($role === 'user') {
        return in_array($permission, $user_permissions);
    }
    
    // Default: no permission
    return false;
}

/**
 * Check if the current user has a specific role
 *
 * @param string|array $roles The role(s) to check
 * @return bool True if the user has any of the roles, false otherwise
 */
function strict_has_role($roles) {
    // Get the user's role from session
    $role = isset($_SESSION['user_role']) ? strtolower($_SESSION['user_role']) : '';
    
    // Check if the user has the specified role(s)
    if (is_array($roles)) {
        $roles_lower = array_map('strtolower', $roles);
        return in_array($role, $roles_lower);
    } else {
        return $role === strtolower($roles);
    }
}
?>
