<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Verificar se a tabela service_calls existe e criá-la se necessário
$table_exists = false;
$check_table = db_query("SHOW TABLES LIKE 'service_calls'");
if ($check_table && db_num_rows($check_table) > 0) {
    $table_exists = true;
} else {
    // Criar a tabela service_calls
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `service_calls` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `customer_id` int(11) NOT NULL,
        `title` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `scheduled_date` datetime NOT NULL,
        `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
        `service_history` text DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `customer_id` (`customer_id`),
        CONSTRAINT `service_calls_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if (db_query($create_table_sql)) {
        $table_exists = true;
        error_log("Tabela service_calls criada com sucesso");
    } else {
        error_log("Erro ao criar tabela service_calls: " . db_error());
        $error = 'Erro ao criar tabela de chamados. Por favor, contate o administrador do sistema.';
    }
}

// Get all service calls with customer information
$service_calls = [];
$result = db_query("
    SELECT sc.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
    FROM service_calls sc
    JOIN customers c ON sc.customer_id = c.id
    ORDER BY sc.scheduled_date DESC
");

if ($result && db_num_rows($result) > 0) {
    $service_calls = db_fetch_all($result);
}

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// Função para traduzir o status
function translate_status($status) {
    $translations = [
        'pending' => 'Pendente',
        'in_progress' => 'Em Andamento',
        'completed' => 'Concluído',
        'cancelled' => 'Cancelado'
    ];

    return isset($translations[$status]) ? $translations[$status] : $status;
}

// Função para obter a classe CSS do status
function get_status_class($status) {
    $classes = [
        'pending' => 'status-pending',
        'in_progress' => 'status-in-progress',
        'completed' => 'status-completed',
        'cancelled' => 'status-cancelled'
    ];

    return isset($classes[$status]) ? $classes[$status] : '';
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chamados de Atendimento - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            text-align: center;
            min-width: 100px;
        }

        .status-pending {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-in-progress {
            background-color: #b8daff;
            color: #004085;
        }

        .status-completed {
            background-color: #c3e6cb;
            color: #155724;
        }

        .status-cancelled {
            background-color: #f5c6cb;
            color: #721c24;
        }

        .service-call-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Estilo para o container de botões na página de chamados */
        .page-header .action-buttons {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            .page-header .action-buttons {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tony's AC Repair</div>
                <ul class="nav-links">
                    <li><a href="dashboard_simple.php">Painel</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                    <li><a href="service_calls.php" class="active">Chamados</a></li>
                </ul>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-headset"></i> Chamados de Atendimento</h1>
                <div class="action-buttons">
                    <a href="dashboard_simple.php" class="btn-back"><i class="fas fa-arrow-left"></i> Voltar</a>
                    <a href="service_call_reports.php" class="btn-report"><i class="fas fa-chart-bar"></i> Relatórios</a>
                    <a href="service_call_create.php" class="btn-new-call"><i class="fas fa-plus-circle"></i> Novo Chamado</a>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-list"></i> Lista de Chamados</h2>
                </div>
                <div class="card-body">
                    <?php if (empty($service_calls)): ?>
                        <p>Nenhum chamado de atendimento registrado.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-hashtag"></i> ID</th>
                                        <th><i class="fas fa-user"></i> Cliente</th>
                                        <th><i class="fas fa-tag"></i> Título</th>
                                        <th><i class="fas fa-calendar"></i> Data Agendada</th>
                                        <th><i class="fas fa-info-circle"></i> Status</th>
                                        <th><i class="fas fa-cogs"></i> Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($service_calls as $call): ?>
                                        <tr>
                                            <td><?php echo $call['id']; ?></td>
                                            <td><?php echo htmlspecialchars($call['customer_name']); ?></td>
                                            <td><?php echo htmlspecialchars($call['title']); ?></td>
                                            <td><?php echo format_date($call['scheduled_date']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo get_status_class($call['status']); ?>">
                                                    <?php echo translate_status($call['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="service_call_view.php?id=<?php echo $call['id']; ?>" class="btn" title="Visualizar"><i class="fas fa-eye"></i></a>
                                                    <a href="service_call_edit.php?id=<?php echo $call['id']; ?>" class="btn btn-secondary" title="Editar"><i class="fas fa-edit"></i></a>
                                                    <a href="service_call_delete.php?id=<?php echo $call['id']; ?>" class="btn btn-danger delete-btn" title="Excluir"><i class="fas fa-trash-alt"></i></a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Menu toggle para dispositivos móveis
            $('.menu-toggle').click(function() {
                $('.nav-links').toggleClass('active');
            });

            // Confirmação para excluir
            $('.delete-btn').click(function(e) {
                if (!confirm('Tem certeza que deseja excluir este chamado?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>
