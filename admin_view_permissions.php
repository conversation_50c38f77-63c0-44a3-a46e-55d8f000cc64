<?php
// Habilitar exibição de erros para depuração
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir verificação de sessão
require_once 'check_session.php';

// Incluir funções de permissão
if (!function_exists('user_has_permission') && file_exists('includes/permissions.php')) {
    include_once 'includes/permissions.php';
}

// Verificar se o usuário é administrador
$is_admin = user_has_role('administrator');

// Se não for administrador, redirecionar para o dashboard
if (!$is_admin) {
    // Log the access attempt
    error_log("Access attempt to admin_view_permissions.php by non-admin user: {$_SESSION['username']} with role {$_SESSION['user_role']}");
    header('Location: dashboard_simple.php');
    exit;
}

// Obter todas as roles
$roles = [];
$query = "SELECT id, name, description FROM user_roles ORDER BY name";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $roles[$row['id']] = $row;
    }
}

// Obter todas as permissões
$permissions = [];
$query = "SELECT id, name, description FROM permissions ORDER BY name";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permissions[$row['id']] = $row;
    }
}

// Obter permissões por role
$role_permissions = [];
$query = "SELECT role_id, permission_id FROM role_permissions";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        if (!isset($role_permissions[$row['role_id']])) {
            $role_permissions[$row['role_id']] = [];
        }
        $role_permissions[$row['role_id']][] = $row['permission_id'];
    }
}

// Agrupar permissões por categoria (baseado no prefixo do nome)
$permission_categories = [];
foreach ($permissions as $permission) {
    $parts = explode('_', $permission['name']);
    $category = $parts[0] ?? 'other';

    if (!isset($permission_categories[$category])) {
        $permission_categories[$category] = [];
    }

    $permission_categories[$category][] = $permission;
}

// Ordenar categorias alfabeticamente
ksort($permission_categories);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Permissões</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .role-tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .role-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            white-space: nowrap;
        }

        .role-tab:hover {
            background-color: #f8f9fa;
        }

        .role-tab.active {
            background-color: #fff;
            border-color: #dee2e6;
            border-bottom-color: #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }

        .role-content {
            display: none;
        }

        .role-content.active {
            display: block;
        }

        .permission-category {
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
            text-transform: capitalize;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .permission-item:hover {
            background-color: #f8f9fa;
        }

        .permission-checkbox {
            margin-right: 10px;
        }

        .permission-label {
            font-weight: normal;
            flex: 1;
        }

        .permission-description {
            color: #6c757d;
            font-size: 14px;
            margin-top: 2px;
        }

        .role-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            margin-left: 10px;
        }

        .role-admin {
            background-color: #cfe2ff;
            color: #0d6efd;
        }

        .role-manager {
            background-color: #d1e7dd;
            color: #198754;
        }

        .role-user {
            background-color: #fff3cd;
            color: #ffc107;
        }

        .role-other {
            background-color: #e2e3e5;
            color: #212529;
        }

        .role-description {
            color: #6c757d;
            font-size: 14px;
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">Visualizar Permissões</h1>
                    <a href="dashboard_simple.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar para o Dashboard
                    </a>
                </div>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Permissões por Role</h2>
                    </div>
                    <div class="admin-card-body">
                        <div class="role-tabs">
                            <?php foreach ($roles as $index => $role): ?>
                                <div class="role-tab <?php echo $index === array_key_first($roles) ? 'active' : ''; ?>" data-role="<?php echo $role['id']; ?>">
                                    <?php echo htmlspecialchars($role['name']); ?>
                                    <span class="role-badge <?php echo 'role-' . strtolower($role['name']); ?>">
                                        <?php echo count($role_permissions[$role['id']] ?? []); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php foreach ($roles as $index => $role): ?>
                            <div class="role-content <?php echo $index === array_key_first($roles) ? 'active' : ''; ?>" id="role-<?php echo $role['id']; ?>">
                                <div class="role-description">
                                    <strong>Descrição:</strong> <?php echo htmlspecialchars($role['description']); ?>
                                </div>

                                <?php if (empty($role_permissions[$role['id']])): ?>
                                    <p>Esta role não possui permissões atribuídas.</p>
                                <?php else: ?>
                                    <?php foreach ($permission_categories as $category => $category_permissions): ?>
                                        <div class="permission-category">
                                            <h3 class="category-title"><?php echo htmlspecialchars($category); ?></h3>

                                            <?php foreach ($category_permissions as $permission): ?>
                                                <div class="permission-item">
                                                    <input type="checkbox"
                                                           id="permission-<?php echo $role['id']; ?>-<?php echo $permission['id']; ?>"
                                                           class="permission-checkbox"
                                                           <?php echo in_array($permission['id'], $role_permissions[$role['id']] ?? []) ? 'checked' : ''; ?>
                                                           disabled>
                                                    <label for="permission-<?php echo $role['id']; ?>-<?php echo $permission['id']; ?>" class="permission-label">
                                                        <?php echo htmlspecialchars($permission['name']); ?>
                                                        <div class="permission-description"><?php echo htmlspecialchars($permission['description']); ?></div>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>

                                <div class="text-center mt-4">
                                    <a href="admin_role_edit.php?id=<?php echo $role['id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> Editar Permissões
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabs = document.querySelectorAll('.role-tab');
            const contents = document.querySelectorAll('.role-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const roleId = this.getAttribute('data-role');

                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Add active class to current tab and content
                    this.classList.add('active');
                    document.getElementById('role-' + roleId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
<?php
// Close database connection
if (isset($conn) && $conn instanceof mysqli) {
    $conn->close();
}
?>
