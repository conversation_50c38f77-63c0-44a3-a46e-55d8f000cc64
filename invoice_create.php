<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Determina qual página está ativa
$current_page = basename($_SERVER['PHP_SELF']);

// Verificar se a tabela invoice_payments existe
try {
    $result = db_query("SHOW TABLES LIKE 'invoice_payments'");
    if (db_num_rows($result) == 0) {
        // Criar tabela se não existir
        $create_table = "CREATE TABLE IF NOT EXISTS invoice_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_id INT NOT NULL,
            payment_date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        )";
        db_query($create_table);
    }
} catch (Exception $e) {
    // Registrar erro, mas continuar a execução
    error_log('Erro na verificação da tabela invoice_payments: ' . $e->getMessage());
}

// Initialize variables
$customer_id = $quote_id = 0;
$invoice_date = date('Y-m-d');
$due_date = date('Y-m-d', strtotime('+1 day'));
$notes = '';
$subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
$error = $success = '';
$items = [];



// Check if creating from quote
if (isset($_GET['quote_id'])) {
    $quote_id = intval($_GET['quote_id']);

    // Get quote data
    $result = db_query("SELECT * FROM quotes WHERE id = $quote_id");
    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);

        $customer_id = $quote['customer_id'];
        $subtotal = $quote['subtotal'];
        $tax_rate = $quote['tax_rate'];
        $tax_amount = $quote['tax_amount'];
        $discount_amount = $quote['discount_amount'];
        $total = $quote['total'];
        $notes = $quote['notes'];

        // Get quote items
        $result = db_query("
            SELECT qi.*, i.name as item_name
            FROM quote_items qi
            JOIN inventory i ON qi.inventory_id = i.id
            WHERE qi.quote_id = $quote_id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }
    }
}

// Get all customers for dropdown
$customers = [];
$result = db_query("SELECT id, name FROM customers ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}

// Note: Inventory items are now loaded dynamically via AJAX search
// No need to pre-load all inventory items

// Handle customer creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'create_customer') {
    $name = trim($_POST['customer_name'] ?? '');
    $email = trim($_POST['customer_email'] ?? '');
    $phone = trim($_POST['customer_phone'] ?? '');
    $address = trim($_POST['customer_address'] ?? '');
    $city = trim($_POST['customer_city'] ?? '');
    $state = trim($_POST['customer_state'] ?? '');
    $zip = trim($_POST['customer_zip'] ?? '');
    $notes_customer = trim($_POST['customer_notes'] ?? '');

    // Validate customer data
    if (empty($name)) {
        // If this is an AJAX request, return JSON error response
        if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Customer name is required.'
            ]);
            db_close();
            exit;
        }
        $error = 'Customer name is required.';
    } else {
        // Escape data to prevent SQL injection
        $name = db_escape($name);
        $email = db_escape($email);
        $phone = db_escape($phone);
        $address = db_escape($address);
        $city = db_escape($city);
        $state = db_escape($state);
        $zip = db_escape($zip);
        $notes_customer = db_escape($notes_customer);

        // Insert new customer
        $query = "INSERT INTO customers (name, email, phone, address, city, state, zip, notes, created_at, updated_at) "
               . "VALUES ('$name', '$email', '$phone', '$address', '$city', '$state', '$zip', '$notes_customer', NOW(), NOW())";

        if (db_query($query)) {
            $new_customer_id = db_insert_id();

            // If this is an AJAX request, return JSON response
            if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'customer' => [
                        'id' => $new_customer_id,
                        'name' => $_POST['customer_name'], // Use original unescaped name for display
                        'email' => $_POST['customer_email'],
                        'phone' => $_POST['customer_phone'],
                        'address' => $_POST['customer_address']
                    ],
                    'message' => 'Cliente criado com sucesso!'
                ]);
                db_close();
                exit;
            }

            $success = 'Cliente criado com sucesso!';

            // Set the new customer as selected
            $customer_id = $new_customer_id;

            // Refresh customers list
            $customers = [];
            $result = db_query("SELECT id, name FROM customers ORDER BY name");
            if ($result && db_num_rows($result) > 0) {
                $customers = db_fetch_all($result);
            }
        } else {
            // If this is an AJAX request, return JSON error response
            if (isset($_POST['ajax']) && $_POST['ajax'] == '1') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'Erro ao criar cliente: ' . db_error()
                ]);
                db_close();
                exit;
            }

            $error = 'Erro ao criar cliente: ' . db_error();
        }
    }
}

// Check if form was submitted for invoice creation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && (!isset($_POST['action']) || $_POST['action'] != 'create_customer') && (!isset($_POST['ajax']) || $_POST['ajax'] != '1')) {
    // Get form data
    $customer_id = intval(isset($_POST['customer_id']) ? $_POST['customer_id'] : 0);

    $quote_id = intval(isset($_POST['quote_id']) ? $_POST['quote_id'] : 0);
    $invoice_date = isset($_POST['invoice_date']) ? $_POST['invoice_date'] : date('Y-m-d');
    $due_date = isset($_POST['due_date']) ? $_POST['due_date'] : '';
    $subtotal = floatval(isset($_POST['subtotal']) ? $_POST['subtotal'] : 0);
    $tax_rate = floatval(isset($_POST['tax_rate']) ? $_POST['tax_rate'] : 0);
    $tax_amount = floatval(isset($_POST['tax_amount']) ? $_POST['tax_amount'] : 0);
    $discount_amount = floatval(isset($_POST['discount_amount']) ? $_POST['discount_amount'] : 0);
    $total = floatval(isset($_POST['total']) ? $_POST['total'] : 0);
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';
    $service_address = isset($_POST['service_address']) ? $_POST['service_address'] : '';

    // Capturar dados de pagamento
    $payment_status = isset($_POST['payment_status']) ? $_POST['payment_status'] : 'unpaid';
    $payment_amount = isset($_POST['payment_amount']) ? floatval($_POST['payment_amount']) : 0;
    $payment_method = isset($_POST['payment_method']) ? $_POST['payment_method'] : '';
    $payment_date = isset($_POST['payment_date']) ? $_POST['payment_date'] : date('Y-m-d');
    $payment_notes = isset($_POST['payment_notes']) ? $_POST['payment_notes'] : '';

    // Get items
    $item_ids = isset($_POST['item_id']) ? $_POST['item_id'] : [];
    $item_descriptions = isset($_POST['item_description']) ? $_POST['item_description'] : [];
    $item_quantities = isset($_POST['item_quantity']) ? $_POST['item_quantity'] : [];
    $item_prices = isset($_POST['item_price']) ? $_POST['item_price'] : [];
    $item_subtotals = isset($_POST['item_subtotal']) ? $_POST['item_subtotal'] : [];

    // Build items array
    for ($i = 0; $i < count($item_ids); $i++) {
        if (!empty($item_ids[$i])) {
            $items[] = [
                'id' => intval($item_ids[$i]),
                'description' => $item_descriptions[$i],
                'quantity' => floatval($item_quantities[$i]),
                'price' => floatval($item_prices[$i]),
                'subtotal' => floatval($item_subtotals[$i])
            ];
        }
    }

    // Validate form data
    if ($customer_id <= 0) {
        $error = 'Selecione um cliente.';
    } elseif (empty($invoice_date)) {
        $error = 'A data da fatura é obrigatória.';
    } elseif (empty($items)) {
        $error = 'Adicione pelo menos um item à fatura.';
    } else {
        // We'll use manual transaction management
        db_begin_transaction();
        $transaction_success = true;

        // Generate invoice number in format: INV-YYMMDD-XXX
        $date_part = date('ymd', strtotime($invoice_date));

        // Get the highest sequence number from all invoices (never resets)
        $result = db_query("SELECT MAX(CAST(SUBSTRING_INDEX(invoice_number, '-', -1) AS UNSIGNED)) as max_number FROM invoices WHERE invoice_number LIKE 'INV-%'");
        $row = db_fetch_assoc($result);
        $next_number = 1;

        if ($row && isset($row['max_number']) && $row['max_number']) {
            $next_number = $row['max_number'] + 1;
        }

        $invoice_number = sprintf("INV-%s-%03d", $date_part, $next_number);

        // Escape strings to prevent SQL injection
        $invoice_number = db_escape($invoice_number);
        $invoice_date = db_escape($invoice_date);
        $due_date = db_escape($due_date);
        $notes = db_escape($notes);
        $service_address = db_escape($service_address);

        // Insert invoice
        $query = "INSERT INTO invoices (customer_id, quote_id, invoice_number, invoice_date, due_date, subtotal, tax_rate, tax_amount, discount_amount, total, notes, service_address, status) "
               . "VALUES ($customer_id, " . ($quote_id > 0 ? $quote_id : "NULL") . ", '$invoice_number', '$invoice_date', " . (!empty($due_date) ? "'$due_date'" : "NULL") . ", $subtotal, $tax_rate, $tax_amount, $discount_amount, $total, '$notes', '$service_address', '" . db_escape($payment_status) . "')";

        if (db_query($query)) {
            $invoice_id = db_insert_id();

            // Insert invoice items
            foreach ($items as $item) {
                $inventory_id = $item['id'];
                $description = db_escape($item['description']);
                $quantity = $item['quantity'];
                $price = $item['price'];
                $subtotal = $item['subtotal'];

                $query = "INSERT INTO invoice_items (invoice_id, inventory_id, description, quantity, price, subtotal) "
                       . "VALUES ($invoice_id, $inventory_id, '$description', $quantity, $price, $subtotal)";

                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao inserir item da fatura: ' . db_error();
                    break;
                }
            }
            // Criar registro de pagamento se necessário
            if ($transaction_success && ($payment_status === 'paid' || $payment_status === 'partial')) {
                // Validar dados de pagamento
                if (empty($payment_method)) {
                    $transaction_success = false;
                    $error = 'Método de pagamento é obrigatório quando o status é "' . ($payment_status === 'paid' ? 'Pago' : 'Parcialmente Pago') . '".';
                } else {
                    // Determinar valor do pagamento
                    $payment_value = $payment_amount;
                    if ($payment_status === 'paid' && $payment_amount <= 0) {
                        $payment_value = $total; // Usar valor total se não especificado para status "paid"
                    }

                    if ($payment_value <= 0) {
                        $transaction_success = false;
                        $error = 'Valor do pagamento deve ser maior que zero.';
                    } elseif ($payment_status === 'partial' && $payment_value >= $total) {
                        $transaction_success = false;
                        $error = 'Valor do pagamento parcial deve ser menor que o total da fatura.';
                    } else {
                        // Inserir registro de pagamento
                        $payment_query = "INSERT INTO invoice_payments (invoice_id, payment_date, amount, payment_method, notes) "
                                       . "VALUES ($invoice_id, '" . db_escape($payment_date) . "', $payment_value, '" . db_escape($payment_method) . "', '" . db_escape($payment_notes) . "')";

                        if (!db_query($payment_query)) {
                            $transaction_success = false;
                            $error = 'Erro ao registrar pagamento: ' . db_error();
                        } else {
                            // Atualizar campos paid_amount e balance na invoice
                            $balance = $total - $payment_value;
                            $update_query = "UPDATE invoices SET paid_amount = $payment_value, balance = $balance WHERE id = $invoice_id";

                            if (!db_query($update_query)) {
                                $transaction_success = false;
                                $error = 'Erro ao atualizar valores da fatura: ' . db_error();
                            }
                        }
                    }
                }
            }

            // Update quote status if created from quote
            if ($transaction_success && $quote_id > 0) {
                $query = "UPDATE quotes SET status = 'approved' WHERE id = $quote_id";
                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao atualizar status do orçamento: ' . db_error();
                }
            }

            if ($transaction_success) {
                db_commit();
                $success = 'Fatura criada com sucesso! Número: ' . $invoice_number;

                // Adicionar informação sobre pagamento na mensagem de sucesso
                if ($payment_status === 'paid') {
                    $success .= ' O pagamento foi registrado como PAGO.';
                } elseif ($payment_status === 'partial' && isset($payment_value)) {
                    $success .= ' O pagamento parcial de $' . number_format($payment_value, 2) . ' foi registrado.';
                }

                // Get customer name for the success modal
                $customer_name = 'Customer';
                $customer_result = db_query("SELECT name FROM customers WHERE id = $customer_id");
                if ($customer_result && db_num_rows($customer_result) > 0) {
                    $customer_row = db_fetch_assoc($customer_result);
                    $customer_name = $customer_row['name'];
                }

                // Capturar dados para o modal de sucesso
                $created_invoice_id = $invoice_id;
                $created_invoice_number = $invoice_number;
                $created_customer_name = $customer_name;
                $show_success_modal = true;

                // Clear form data
                $customer_id = $quote_id = 0;
                $invoice_date = date('Y-m-d');
                $due_date = date('Y-m-d', strtotime('+1 day'));
                $notes = '';
                $subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
                $items = [];

                // Clear payment data
                $payment_status = 'unpaid';
                $payment_amount = 0;
                $payment_method = '';
                $payment_date = date('Y-m-d');
                $payment_notes = '';
            } else {
                db_rollback();
            }
        } else {
            db_rollback();
            $error = 'Erro ao criar fatura: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light">
    <title>New Invoice - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard_simple-layout.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /*
         * INVOICE CREATE STYLES - SIMPLIFIED APPROACH
         * Date fields now use only css/style.css rules for consistency
         * Removed complex device-specific adjustments and conflicts
         * Result: Clean, consistent behavior across all devices
         */

        /* Force light mode - prevent dark mode activation on iPhone */
        :root {
            color-scheme: light !important;
        }

        html {
            color-scheme: light !important;
        }

        body {
            color-scheme: light !important;
            background-color: #f8f9fa !important;
            color: #333 !important;
        }

        /* === CORREÇÕES CROSS-PLATFORM PARA FONTES === */

        /* Font-family cross-platform otimizada */
        body, input, button, select, textarea, .form-control, .btn {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
        }

        /* Font-smoothing pode afetar Android diferentemente - COMENTADO PARA TESTE */
        /*
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        */

        /* Text-size-adjust pode afetar Android diferentemente - COMENTADO PARA TESTE */
        /*
        html, body, input, select, textarea, label,
        .totals-display-label, .totals-display-value,
        .form-group label, h1, h2, h3, h4, h5, h6 {
            -webkit-text-size-adjust: 100% !important;
            text-size-adjust: 100% !important;
        }
        */

        /* === CORREÇÕES ESPECÍFICAS PARA iOS SAFARI === */
        @supports (-webkit-touch-callout: none) {
            /* Normalização de renderização para iOS */
            * {
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
            }

            /* REMOVIDO: Força tamanhos específicos - causava conflito */
            /* Mantém apenas os tamanhos universais definidos mais abaixo */
        }

        /* === CORREÇÕES PARA DENSIDADE DE PIXELS - COMENTADO PARA TESTE === */

        /* Ajustes para telas Retina podem afetar Android high-DPI - COMENTADO */
        /*
        @media screen and (-webkit-min-device-pixel-ratio: 2),
               screen and (min-resolution: 192dpi),
               screen and (min-resolution: 2dppx) {

            * {
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
            }
        }
        */


        }



        /* Força visibilidade do texto da data em todos os dispositivos */
        input[type="date"]::-webkit-datetime-edit,
        input[type="date"]::-webkit-datetime-edit-fields-wrapper,
        input[type="date"]::-webkit-datetime-edit-text,
        input[type="date"]::-webkit-datetime-edit-month-field,
        input[type="date"]::-webkit-datetime-edit-day-field,
        input[type="date"]::-webkit-datetime-edit-year-field {
            color: #495057 !important;
            padding: 0;
        }

        /* Remove completamente o ícone de calendário */
        input[type="date"]::-webkit-calendar-picker-indicator {
            display: none;
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: 1px;
        }

        /* === REMOÇÃO COMPLETA DO ÍCONE DE CALENDÁRIO === */

        /* Garante que o campo de data seja totalmente visível */
        input[type="date"] {
            position: relative;
            /* Garante espaço suficiente para o texto completo */
            padding-right: 8px;
        }

        /* Remove ícone de calendário em todos os navegadores */
        input[type="date"]::-webkit-calendar-picker-indicator,
        input[type="date"]::-webkit-inner-spin-button,
        input[type="date"]::-webkit-clear-button {
            display: none;
            opacity: 0;
            width: 0;
            height: 0;
            margin: 0;
            padding: -10px;
        }

        /* Remove ícone específico para iOS */
        @supports (-webkit-touch-callout: none) {
            input[type="date"]::-webkit-calendar-picker-indicator {
                display: none;
                opacity: 0;
                width: 0;
                height: 0;
                margin: 0;
                padding: 1px;
            }
        }

        /* === CAMPOS DE DATA CONTROLADOS POR CSS/STYLE.CSS === */
        /* Campos de data agora usam apenas as regras do css/style.css para consistência */




        /* === NORMALIZAÇÃO DE INPUTS E LABELS === */

        /* Força consistência de inputs entre iOS e Android - COMENTADO PARA TESTE */
        /*
        input, select, textarea {
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
            font-family: inherit !important;
            font-size: 16px !important;
            line-height: 1.4 !important;
        }
        */

        /* Normalização de labels */
        label, .form-group label, .totals-label {
            font-family: inherit !important;
            font-weight: 600 !important;
            line-height: 1.2 !important;
        }

        /* Força renderização consistente de botões */
        button, .btn {
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
            font-family: inherit !important;
        }

        /* === FORÇA APLICAÇÃO APÓS CARREGAMENTO === */

        /* Garante que estilos sejam aplicados após carregamento completo */
        body.loaded .totals-display-label,
        body.loaded .totals-display-value {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        /* Força aplicação em todos os elementos de texto - COMENTADO PARA TESTE */
        /*
        body.loaded * {
            -webkit-text-size-adjust: 100% !important;
            text-size-adjust: 100% !important;
        }
        */

        .customer-search-container {
            position: relative;
        }

        /* Ícone de lupa nos campos de busca */
        .customer-search-container .search-icon,
        .item-search-container .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 14px;
            z-index: 5;
            pointer-events: none;
            opacity: 0.6;
            display: block;
            width: 14px;
            height: 14px;
            line-height: 1;
        }

        /* Ajustar padding dos inputs para acomodar o ícone */
        .customer-search-container input,
        .item-search-container input {
            padding-left: 42px !important;
            position: relative;
            z-index: 1;
            background-color: transparent;
        }

        /* Ajustar padding do campo item search para acomodar botão Custom */
        .item-search-container input {
            padding-right: 60px !important;
        }

        /* Garantir que o placeholder seja visível e não seja sobreposto pelo ícone */
        .customer-search-container input::placeholder,
        .item-search-container input::placeholder {
            color: #6c757d !important;
            opacity: 0.7 !important;
            z-index: 2 !important;
            position: relative !important;
        }

        /* Botão Custom imitando aparência do botão paste */
        .custom-item-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: transparent;
            color: #007bff;
            opacity: 0.28;
            font-size: 12px;
            padding-top: 10px; padding-bottom: 10px; padding-right: 6px; padding-left: 10px;
            line-height: 1;
            border-radius: 4px;
            cursor: pointer;
            z-index: 10;
            font-family: inherit;
        }

        .custom-item-btn:hover,
        .custom-item-btn:focus {
            opacity: 0.5;
        }

        .custom-item-btn:active {
            opacity: 0.65;
        }

        /* Estado inativo quando há item selecionado */
        .custom-item-btn:disabled {
            opacity: 0.15;
            cursor: not-allowed;
            color: #6c757d !important;
        }

        /* Efeito quando o campo está focado - ícone fica mais visível */
        .customer-search-container input:focus + .search-icon,
        .item-search-container input:focus + .search-icon,
        .customer-search-container input:focus ~ .search-icon,
        .item-search-container input:focus ~ .search-icon {
            color: #007bff;
            opacity: 0.8;
        }

        /* Alternativa: usar :focus-within no container */
        .customer-search-container:focus-within .search-icon,
        .item-search-container:focus-within .search-icon {
            color: #007bff;
            opacity: 0.8;
        }

        /* Regra específica para dropdown do cliente - igual ao dos itens */
        .customer-search-container .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        .search-result-item {
            padding: 1px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .search-result-item:hover {
            background-color: #f5f5f5;
        }

        .search-result-item.new-customer {
            background-color: #e8f4fd;
            border-top: 2px solid #007bff;
        }

        .search-result-item.new-customer:hover {
            background-color: #d1ecf1;
        }

        .no-results {
            padding: 15px;
            text-align: center;
            color: #666;
        }

        .selected-customer {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 4px;
            position: relative;
        }

        .customer-name {
            font-weight: bold;
            color: #155724;
        }

        /* Estilos antigos do btn-clear-customer removidos - agora usando .selected-customer-chip .btn-clear-customer */

        .customer-registration-form {
            margin: 8px 0;
            padding: 10px;
            border: 2px solid #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }

        .customer-registration-form h3 {
            margin-top: 0;
            margin-bottom: 8px;
            color: #007bff;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 6px;
        }

        .customer-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin-bottom: 12px;
        }

        .customer-form-buttons {
            text-align: right;
        }

        .customer-form-buttons .btn {
            margin-left: 10px;
        }

        .error {
            color: #dc3545;
            padding: 10px;
            text-align: center;
        }

        .form-row,
.expandable-content-inner .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;                /* usei o menor gap que já estava em uso */

  width: 100%;
  box-sizing: border-box;
  overflow: visible;        /* permite dropdown */
  align-items: start;
}

.form-row .form-group,
.expandable-content-inner .form-row .form-group {
  display: flex;
  flex-direction: column;

  width: 100%;
  min-width: 120px;         /* garante espaço mínimo para data completa */
  box-sizing: border-box;
  overflow: visible;
  position: relative;       /* necessário pro dropdown */
}

.form-row .form-group input[type="date"] {
  position: relative;
  text-align: center;
  padding: 0px;
  z-index: 1;               /* garante que o calendário aparece sobre outros */
}




        @media (max-width: 480px) {
            /* Em mobile, campos de data mantêm direção normal */
            .form-row .form-group input[type="date"] {
                direction: ltr;
                text-align: center;
            }

            /* Garante visibilidade completa */
            .form-row .form-group {
                position: relative;
                overflow: visible;
            }
        }

        /* Estilos para campos de formulário mais evidentes */
        .form-group {
            margin-bottom: 8px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #495057;
            margin-bottom: 3px;
            font-size: 14px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 8px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            color: #495057;
            background-color: #fff;
            transition: all 0.3s ease;
            box-sizing: border-box;
            /* Altura consistente para todos os campos */
            height: auto;
            min-height: 42px;
        }

        /* Garantir altura consistente específica para campos principais */
        #customer_search,
        #service_address,
        #item_search {
            height: auto !important;
            min-height: 42px !important;
            max-height: none !important;
            /* jonatas padding: 10px 14px !important; *//* Placeholder fora da Lupa  */
            font-size: 14px !important;
            box-sizing: border-box !important;
            /* Garantir borda consistente */
            border: 2px solid #e9ecef !important;
            border-radius: 6px !important;
            color: #495057 !important;
            background-color: #fff !important;
            transition: all 0.3s ease !important;
        }

        /* Service address com botão paste - apenas ajustar padding */
        #service_address,
        #item_search {
            padding-right: 52px !important;
        }

        /* Placeholders mais visíveis */
        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #ced4da !important; /* Cor mais visível que #e9ecef */
            opacity: 0.9; /* Mais visível que 0.7 */
        }

        /* Estilos para labels com ícones */
        .label-with-icon {
            display: flex;
            align-items: center;
            gap: 12px; /* Aumentado de 6px para 12px */
        }

        .label-icon {
            font-size: 14px;
            color: #6c757d; /* Cor sutil monocromática */
            opacity: 0.8;
        }

        /* Textarea auto-expandindo */
        .form-group textarea {
            resize: vertical; /* Permitir redimensionamento vertical */
            min-height: 45px; /* Altura mínima reduzida para ~1.5 linhas */
            overflow-y: hidden; /* Ocultar scroll inicialmente */
        }

        /* Correções específicas para auto-expand no iPhone */
        @supports (-webkit-touch-callout: none) {
            #notes, #payment_notes {
                height: auto !important;
                min-height: 45px !important;
                max-height: none !important;
                overflow-y: hidden !important;
                resize: none !important;
                box-sizing: border-box !important;
            }

            /* Correções específicas para campos Tax/Discount - Compatível iOS/Android */
            .input-with-dropdown {
                display: flex !important;
                align-items: stretch !important;
                height: 44px !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 6px !important;
                overflow: visible !important;
                background-color: white !important;
                box-sizing: border-box !important;
                position: relative !important;
                transition: all 0.2s ease !important;
            }

            .input-with-dropdown input {
                flex: 1 !important;
                border: none !important;
                outline: none !important;
                padding: 0 12px !important;
                font-size: 16px !important;
                background: transparent !important;
                height: 44px !important;
                min-height: 44px !important;
                max-height: 44px !important;
                min-width: 0 !important;
                box-sizing: border-box !important;
                line-height: 44px !important;
                vertical-align: top !important;
                text-align: left !important;
                margin: 0 !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
            }

            .input-with-dropdown select {
                border: none !important;
                outline: none !important;
                background-color: #f8f9fa !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                color: #6c757d !important;
                width: 50px !important;
                min-width: 50px !important;
                max-width: 50px !important;
                height: 44px !important;
                min-height: 44px !important;
                max-height: 44px !important;
                border-left: 1px solid #dee2e6 !important;
                cursor: pointer !important;
                box-sizing: border-box !important;
                text-align: left !important;
                /* text-indent removido para evitar corte de texto */
                line-height: 44px !important;
                vertical-align: top !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%236c757d' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
                background-repeat: no-repeat !important;
                background-position: center right 12px !important;
                background-size: 10px !important;
                position: relative !important;
            }

            /* Hover effect para o dropdown */
            .input-with-dropdown select:hover {
                background-color: #e9ecef !important;
                color: #495057 !important;
                border-left-color: #ced4da !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%23495057' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
            }

            /* Focus effect para o dropdown */
            .input-with-dropdown select:focus {
                background-color: #e9ecef !important;
                color: #495057 !important;
                border-left-color: #007bff !important;
                outline: none !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%23495057' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
            }

            /* Remove a borda padrão do container quando o select está em foco */
            .input-with-dropdown:focus-within {
                border-color: #007bff !important;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
            }

            /* Correções específicas para campos Tax/Discount - Compatível iOS/Android */
            .input-with-dropdown {
                display: flex !important;
                align-items: stretch !important;
                height: 44px !important;
                border: 1px solid #dee2e6 !important;
                border-radius: 6px !important;
                overflow: visible !important;
                background-color: white !important;
                box-sizing: border-box !important;
                position: relative !important;
                transition: all 0.2s ease !important;
            }

            .input-with-dropdown input {
                flex: 1 !important;
                border: none !important;
                outline: none !important;
                padding: 0 12px !important;
                font-size: 16px !important;
                background: transparent !important;
                height: 44px !important;
                min-height: 44px !important;
                max-height: 44px !important;
                min-width: 0 !important;
                box-sizing: border-box !important;
                line-height: 44px !important;
                vertical-align: top !important;
                text-align: left !important;
                margin: 0 !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
            }

            .input-with-dropdown select {
                border: none !important;
                outline: none !important;
                background-color: #f8f9fa !important;
                padding: 0 !important;
                margin: 0 !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                color: #6c757d !important;
                width: 50px !important;
                min-width: 50px !important;
                max-width: 50px !important;
                height: 44px !important;
                min-height: 44px !important;
                max-height: 44px !important;
                border-left: 1px solid #dee2e6 !important;
                cursor: pointer !important;
                box-sizing: border-box !important;
                text-align: left !important;
                /* text-indent removido para evitar corte de texto */
                line-height: 44px !important;
                vertical-align: top !important;
                -webkit-appearance: none !important;
                -moz-appearance: none !important;
                appearance: none !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%236c757d' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
                background-repeat: no-repeat !important;
                background-position: center right 12px !important;
                background-size: 10px !important;
            }

            /* Força cor acinzentada com especificidade máxima - sobrescreve todos os outros CSS */
            .totals-field .input-with-dropdown select,
            .input-with-dropdown select#tax_type,
            .input-with-dropdown select#discount_type {
                background-color: #f8f9fa !important;
                color: #6c757d !important;
            }

            /* Hover effect para o dropdown */
            .input-with-dropdown select:hover {
                background-color: #e9ecef !important;
                color: #495057 !important;
                border-left-color: #ced4da !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%23495057' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
            }

            /* Focus effect para o dropdown */
            .input-with-dropdown select:focus {
                background-color: #e9ecef !important;
                color: #495057 !important;
                border-left-color: #007bff !important;
                outline: none !important;
                background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%23495057' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
            }

            /* Remove a borda padrão do container quando o select está em foco */
            .input-with-dropdown:focus-within {
                border-color: #007bff !important;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
            }

            /* Sobrescreve TODOS os CSS conflitantes - especificidade máxima */
            body .totals-section .totals-field .input-with-dropdown select,
            body .input-with-dropdown select[id="tax_type"],
            body .input-with-dropdown select[id="discount_type"],
            body.loaded .input-with-dropdown select {
                background-color: #f8f9fa !important;
                color: #6c757d !important;
                border-color: #dee2e6 !important;
            }

            /* Sobrescreve estados focus que podem estar sendo aplicados */
            body .input-with-dropdown select:focus,
            body .input-with-dropdown select[id="tax_type"]:focus,
            body .input-with-dropdown select[id="discount_type"]:focus {
                background-color: #e9ecef !important;
                color: #495057 !important;
                border-left-color: #007bff !important;
            }

            /* Força alinhamento perfeito no iOS - implementação melhorada */
            @supports (-webkit-touch-callout: none) {
                .input-with-dropdown {



                    display: flex !important;
                    align-items: stretch !important;
                    height: 44px !important;
                    border: 1px solid #dee2e6 !important;
                    border-radius: 6px !important;
                    overflow: visible !important;
                    background-color: white !important;
                    box-sizing: border-box !important;
                    position: relative !important;
                }

                .input-with-dropdown input {
                    flex: 1 !important;
                    border: none !important;
                    outline: none !important;
                    padding: 0 12px !important;
                    font-size: 16px !important;
                    background: transparent !important;
                    height: 44px !important;
                    min-height: 44px !important;
                    box-sizing: border-box !important;
                    margin: 0 !important;
                    line-height: 1.4 !important;
                    vertical-align: top !important;
                    -webkit-appearance: none !important;
                    -moz-appearance: none !important;
                    appearance: none !important;
                    -webkit-text-size-adjust: 100% !important;
                    text-size-adjust: 100% !important;
                }

                .input-with-dropdown select {
                    border: none !important;
                    outline: none !important;
                    background-color: #f8f9fa !important;
                    border-left: 1px solid #dee2e6 !important;
                    font-size: 14px !important;
                    font-weight: 500 !important;
                    color: #6c757d !important;
                    width: 60px !important;
                    min-width: 60px !important;
                    max-width: 60px !important;
                    height: 44px !important;
                    min-height: 44px !important;
                    padding: 0 8px !important;
                    text-align: center !important;
                    cursor: pointer !important;
                    box-sizing: border-box !important;
                    margin: 0 !important;
                    line-height: 1.4 !important;
                    vertical-align: top !important;
                    -webkit-appearance: none !important;
                    -moz-appearance: none !important;
                    appearance: none !important;
                    -webkit-text-size-adjust: 100% !important;
                    text-size-adjust: 100% !important;
                    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' fill='%236c757d' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
                    background-repeat: no-repeat !important;
                    background-position: center right 8px !important;
                    background-size: 10px !important;
                }

                .input-with-dropdown select:hover {
                    background-color: #e9ecef !important;
                    color: #495057 !important;
                }

                .input-with-dropdown select:focus {
                    background-color: #e9ecef !important;
                    color: #495057 !important;
                    border-left-color: #007bff !important;
                }

                .input-with-dropdown:focus-within {
                    border-color: #007bff !important;
                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
                }

                /* Força cor acinzentada no iOS */
                body .input-with-dropdown select,
                body .input-with-dropdown select#tax_type,
                body .input-with-dropdown select#discount_type,
                body.loaded .input-with-dropdown select {
                    background-color: #f8f9fa !important;
                    color: #6c757d !important;
                    border-color: #dee2e6 !important;
                }

                /* Estados hover/focus específicos para iOS */
                body .input-with-dropdown select:hover {
                    background-color: #e9ecef !important;
                    color: #495057 !important;
                }

                body .input-with-dropdown select:focus {
                    background-color: #e9ecef !important;
                    color: #495057 !important;
                    border-left-color: #007bff !important;
                }
            }


        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            background-color: #f8f9fa;
        }








        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #6c757d;
            font-style: italic;
        }

        /* Padronização dos títulos - reduzidos 25% */
        h1.card-title {
            font-size: 18px;
            font-weight: 700;
            color: #343a40;
            margin: 0;
            line-height: 1.2;
        }

        /* Títulos padronizados - otimizado para mobile primeiro */
        h3 {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin: 15px 0 8px 0;
            text-transform: none;
            letter-spacing: normal;
        }

        @media (max-width: 768px) {
            .customer-form-grid {
                grid-template-columns: 1fr;
            }



            /* Altura consistente para campos principais em tablet */
            #customer_search,
            #service_address,
            #item_search {
                height: auto !important;
                min-height: 38px !important;
                max-height: none !important;
                padding: 9px 12px !important;
                font-size: 14px !important;
                border: 2px solid #e9ecef !important;
                border-radius: 6px !important;
            }

            /* Ajustar padding dos campos de busca com ícone em tablet */
            #customer_search {
                padding-left: 30px !important;
            }

            #item_search {
                padding-left: 30px !important;
                padding-right: 55px !important;
            }

            /* Ícones de busca em tablet */
            .customer-search-container .search-icon,
            .item-search-container .search-icon {
                left: 10px;
                font-size: 13px;
                z-index: 5;
                opacity: 0.6;
            }

            /* Botão Custom em tablet - igual ao paste-btn */
            .custom-item-btn {
                right: 6px;
                font-size: 11px;
            }

            /* Service address com botão paste em tablet */
            #service_address {
                padding-right: 46px !important;
            }

            .form-group label {
                font-size: 14px;
                margin-bottom: 5px;
            }

            h1.card-title {
                font-size: 16.5px;
            }

            /* Títulos padronizados - força consistência */
            h3,
            .form-group label,
            .totals-label,
            label {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: #495057 !important;
                margin: 8px 0 4px 0 !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                line-height: 1.2 !important;
            }

            /* Específico para labels de formulário */
            .form-group label,
            .customer-form-grid label,
            .invoice-details label {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: #495057 !important;
                margin-bottom: 8px !important;
                display: block !important;
            }

            /* Campos de data controlados por css/style.css */



            /* Totals responsivo para tablet */
            .totals-section {
                padding: 3px 8px 3px 8px; /* Reduzir padding superior e inferior para tablet */
            }

            .totals-row-group {
                gap: 8px;
            }

            /* Títulos padronizados para mobile */
            h3,
            .form-group label,
            .totals-label,
            label {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: #495057 !important;
                margin: 8px 0 4px 0 !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                line-height: 1.2 !important;
            }

            .form-group label,
            .customer-form-grid label,
            .invoice-details label {
                font-size: 14px !important;
                font-weight: 600 !important;
                color: #495057 !important;
                margin-bottom: 8px !important;
                display: block !important;
            }

            .input-with-dropdown input,
            .input-with-dropdown select,
            .totals-field input {
                padding: 10px 8px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .form-row {
                padding: 10px;
                gap: 8px;
            }

            /* correcao de corte na data
            
            /* Correção específica para form-row das datas em mobile */
            .expandable-content-inner .form-row {
                gap: 15px;
                grid-template-columns: 1fr 1fr; /* Força 2 colunas mesmo em mobile */
            }

            .form-row .form-group label {
                font-size: 13px;
            }

            .form-row .form-group input {
                font-size: 14px;
                padding: 8px 1px;
            }

            .form-group {
                margin-bottom: 6px;
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                padding: 8px 1px;
                border-radius: 5px;
                font-size: 14px;
                min-height: 36px;
            }

            /* Altura consistente para campos principais em mobile */
            #customer_search,
            #service_address,
            #item_search {
                height: auto !important;
                min-height: 36px !important;
                max-height: none !important;
                padding: 8px 10px !important;
                font-size: 14px !important;
                border: 2px solid #e9ecef !important;
                border-radius: 5px !important;
            }

            /* Ajustar padding dos campos de busca com ícone em mobile */
            #customer_search {
                padding-left: 30px !important;
            }

            #item_search {
                padding-left: 30px !important;
                padding-right: 50px !important;
            }

            /* Ícones de busca em mobile */
            .customer-search-container .search-icon,
            .item-search-container .search-icon {
                left: 8px;
                font-size: 12px;
                z-index: 5;
                opacity: 0.6;
            }

            /* Botão Custom em mobile - igual ao paste-btn */
            .custom-item-btn {
                right: 4px;
                font-size: 10px;
                padding: 1px 4px;
            }

            /* Service address com botão paste em mobile */
            #service_address {
                padding-right: 42px !important;
            }

            .form-group label {
                margin-bottom: 4px;
            }

            h1.card-title {
                font-size: 15px;
            }

            h3 {
                font-size: 12px;
                margin: 6px 0 3px 0;
            }

            /* Campos de data controlados por css/style.css */



            /* Totals responsivo para mobile */
            .totals-section {
                padding: 2px 6px 2px 6px; /* Reduzir padding superior e inferior para mobile */
            }

            .totals-row-group {
                display: flex !important;
                flex-direction: row !important;
                gap: 4px !important; /* Gap menor para mobile */
                margin-bottom: 1px !important;
                overflow: hidden !important; /* Previne transbordo */
            }

            /* Forçar campos Taxa e Desconto lado a lado no mobile */
            .totals-row-group:first-child {
                display: flex !important;
                flex-direction: row !important;
                gap: 6px !important;
            }

            .totals-row-group:first-child .totals-field {
                flex: 1 !important;
                min-width: 0 !important;
            }

            /* Formatação igual ao bloco Itens da Fatura - removido para usar CSS unificado */

            /* Campos readonly com mesma formatação */
            .totals-row-group .totals-field input[readonly] {
                background-color: #f8f9fa !important;
                color: #6c757d !important;
                border: 2px solid #e9ecef !important;
                border-radius: 6px !important;
                padding: 9px 12px !important;
                font-size: 14px !important;
                height: 38px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .totals-label {
                font-size: 13px;
                margin-bottom: 6px;
            }

            /* Responsividade para campos de display em mobile */
            .totals-row-group .totals-display-label {
                font-size: 13px !important; /* Força tamanho menor em mobile */
            }

            .totals-row-group .totals-display-value {
                font-size: 13px !important; /* Força tamanho menor em mobile */
            }

            /* Total maior em mobile */
            .totals-row-group .grand-total .totals-display-label {
                font-size: 15px !important; /* Ligeiramente maior para mobile */
            }

            .totals-row-group .grand-total .totals-display-value,
            .totals-row-group .totals-display-total {
                font-size: 15px !important; /* Ligeiramente maior para mobile */
            }

            .totals-display-field {
                padding: 6px 8px; /* Padding menor para mobile */
                min-height: 36px; /* Altura menor para mobile */
                gap: 4px; /* Gap menor em mobile */
            }

            .input-with-dropdown input,
            .input-with-dropdown select,
            .totals-field input {
                padding: 8px 12px;
                font-size: 14px;
            }

            .grand-total .totals-label {
                font-size: 15px;
            }

            /* Fallback para telas muito pequenas - empilha verticalmente se necessário */
            @media (max-width: 360px) {
                .totals-display-field {
                    flex-direction: column !important;
                    align-items: flex-start !important;
                    gap: 2px !important;
                    padding: 4px 6px !important;
                }

                .totals-display-label {
                    max-width: 100% !important;
                    font-size: 12px !important;
                }

                .totals-display-value {
                    margin-left: 0 !important;
                    align-self: flex-end !important;
                    max-width: 100% !important;
                    font-size: 12px !important;
                }

                .totals-row-group .grand-total .totals-display-label,
                .totals-row-group .grand-total .totals-display-value {
                    font-size: 13px !important;
                }
            }

            /* Responsividade para campos de display em tablet */
            .totals-row-group .totals-display-label {
                font-size: 14px !important; /* Força tamanho para tablet */
            }

            .totals-row-group .totals-display-value {
                font-size: 14px !important; /* Força tamanho para tablet */
            }

            /* Total maior em tablet */
            .totals-row-group .grand-total .totals-display-label {
                font-size: 16px !important; /* Ligeiramente maior para tablet */
            }

            .totals-row-group .grand-total .totals-display-value,
            .totals-row-group .totals-display-total {
                font-size: 16px !important; /* Ligeiramente maior para tablet */
            }

            .totals-display-field {
                padding: 6px 10px; /* Padding para tablet */
                gap: 6px; /* Gap menor em tablet */
            }
        }

        /* Styles for new items section - sem borda por padrão */
        .add-item-section {
            background-color: transparent; /* Remover fundo colorido */
            border: none; /* Sem borda por padrão */
            border-radius: 8px;
            padding: 4px 8px 8px 8px; /* Mesmo padding da seção de totais */
            margin-bottom: 8px;
        }

        /* Borda apenas quando há seleção */
        .add-item-section.has-selection {
            border: 1px solid #e0e0e0; /* Mostrar borda apenas quando item selecionado */
        }

        /* Linha separadora removida para eliminar elemento visual indesejado */

        .add-item-form {
            display: grid;
            grid-template-columns: 2fr 80px 100px;
            gap: 8px;
            align-items: start;
            /* Layout inicial: apenas a busca na primeira linha */
            grid-template-areas:
                "search search search";
        }

        .add-item-form .form-group { display: flex; flex-direction: column; }
        .field-search { grid-area: search; }
        .add-item-form .field-qty {
            display: none;
            grid-area: qty;
            align-self: end; /* Alinhar com a parte inferior */
        }
        .add-item-form .field-price {
            display: none;
            grid-area: price;
            align-self: end; /* Alinhar com a parte inferior */
        }

        /* Quando visíveis, usar flexbox para alinhamento */
        .add-item-section.has-selection .add-item-form .field-qty,
        .add-item-section.has-selection .add-item-form .field-price {
            display: flex !important;
            flex-direction: column;
            align-self: end; /* Alinhar com a parte inferior do grid */
            justify-content: flex-end; /* Empurrar input para baixo */
            height: 100%; /* Ocupar toda a altura da célula */
            margin: 0 !important; /* Sem margens */
            padding: 0 !important; /* Sem paddings */
        }

        /* Alinhar o chip com a base dos inputs, considerando labels */
        .selected-row {
            align-self: end !important; /* Forçar alinhamento na base */
            margin-bottom: 0 !important; /* Sem margem inferior */
        }

        /* Garantir que os inputs de qty e price tenham altura consistente */
        .add-item-form .field-qty input,
        .add-item-form .field-price input {
            /* height removido - usar padding + line-height para evitar corte */
            padding: 10px 8px; /* Padding horizontal reduzido para evitar corte */
            line-height: 1.4;
            box-sizing: border-box;
            margin: 0 !important; /* Sem margens */
            border: 2px solid #e9ecef;
            border-radius: 6px;
            align-self: flex-end; /* Alinhar na parte inferior do flexbox */
            flex-shrink: 0; /* Não encolher */
        }

        /* Labels dos campos qty e price */
        .add-item-form .field-qty label,
        .add-item-form .field-price label {
            margin-bottom: 3px !important; /* Espaçamento consistente */
            margin-top: 0 !important; /* Sem margem superior */
            flex-shrink: 0; /* Não encolher */
        }

        /* Remover margem inferior dos form-groups para eliminar espaço vazio */
        .add-item-form .field-qty,
        .add-item-form .field-price {
            margin-bottom: 0 !important; /* Sem margem inferior */
        }

        /* Garantir que os form-groups ocupem apenas o espaço necessário */
        .add-item-form .form-group.field-qty,
        .add-item-form .form-group.field-price {
            margin-bottom: 0 !important; /* Sem margem inferior */
            padding-bottom: 0 !important; /* Sem padding inferior */
            margin-top: 0 !important; /* Sem margem superior */
            padding-top: 0 !important; /* Sem padding superior */
        }

        /* Sobrescrever regras gerais de form-group para os campos qty e price */
        .add-item-form .field-qty.form-group,
        .add-item-form .field-price.form-group {
            margin: 0 !important; /* Remover todas as margens */
            padding: 0 !important; /* Remover todos os paddings */
        }

        /* Chip e botão, ocultos até haver seleção */
        .selected-row { display: none; }
        .selected-item { grid-area: chip; }

        .add-button-inline {
            margin-top: 6px;
            display: none;
            grid-area: addbtn;
            grid-column: 1 / -1; /* botão ocupa a linha inteira */
            align-self: stretch; /* ocupar toda a linha na vertical do track */
            justify-self: stretch; /* ocupar toda a largura do grid */
            width: 100%;
        }

        /* Quando houver seleção: segunda linha com chip+qtd+preço e terceira com botão */
        .add-item-section.has-selection .add-item-form {
            grid-template-areas:
                "search search search"
                "chip   qty    price"
                "addbtn addbtn addbtn";
            align-items: end; /* Alinhar pela parte inferior */
            grid-template-rows: auto minmax(auto, max-content) auto; /* Linha do meio se adapta ao conteúdo */
        }

        .add-item-section.has-selection .selected-row { display: contents; }
        .add-item-section.has-selection .add-item-form .add-button-inline { display: block; grid-area: addbtn; grid-column: 1 / -1; }

        /* Força máxima especificidade para o botão ocupar toda a linha */
        #add_item_section.has-selection #add-button-inline {
            display: block !important;
            grid-area: addbtn;
            grid-column: 1 / -1 !important;
            grid-row: 3 !important;
            justify-self: stretch;
            align-self: stretch;
            width: 100%;
        }

        .add-item-btn-inline {
            width: 100%;
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            font-size: 18px;
            font-weight: bold;
            border-radius: 6px;
            background-color: #28a745;
            border: none;
            color: white;
            cursor: pointer;
        }

        .add-item-btn-inline:hover {
            background-color: #218838;
        }

        /* Fallback legado removido; posicionamento agora é por grid-areas */

        .btn-clear-item {
            background: none !important;
            border: none !important;
            color: #f44336 !important; /* Mesma cor do botão X do chip de cliente */
            font-size: 16px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            padding: 2px !important;
            margin: 0 !important;
            line-height: 1 !important;
            width: 20px !important;
            height: 20px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 50% !important;
            flex-shrink: 0 !important;
            position: absolute !important; /* Posição absoluta para controle preciso */
            top: 50% !important; /* Centralizar verticalmente */
            right: 8px !important; /* Posição da direita */
            transform: translateY(-50%) !important; /* Ajuste fino para centralização */
        }

        .btn-clear-item:hover {
            color: #d32f2f !important;
            background-color: rgba(244, 67, 54, 0.1) !important;
        }

        .item-search-container {
            position: relative;
        }

        .item-search-container .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        .selected-item {
            margin-top: 0; /* Remover margin para alinhamento perfeito */
            padding: 8px 32px 8px 12px; /* Padding para acomodar botão X */
            background-color: #e3f2fd; /* Mesma cor do chip de cliente */
            border: 1px solid #2196f3; /* Mesma borda do chip de cliente */
            border-radius: 8px; /* Mesmo border-radius do chip de cliente */
            position: relative;
            font-size: 14px;
            display: flex;
            align-items: flex-start; /* Alinhar no topo para permitir quebra de linha */
            align-self: end; /* Alinhar com a parte inferior */
            height: auto; /* Altura dinâmica baseada no conteúdo */
            min-height: 40px; /* Altura mínima igual aos inputs */
            box-sizing: border-box;
            margin-bottom: 0; /* Sem margem inferior */
            width: auto; /* Largura dinâmica baseada no conteúdo */
            max-width: 100%; /* Não exceder a largura da coluna */
            overflow: visible; /* Permitir overflow para quebra de linha */
            word-wrap: break-word; /* Quebrar palavras longas */
            line-height: 1.3; /* Line-height para quebra de linha */
        }

        .selected-item .item-name {
            font-weight: 600;
            color: #1976d2; /* Mesma cor do nome no chip de cliente */
            font-size: 14px;
            line-height: 1.3; /* Line-height para quebra de linha */
            white-space: normal; /* Permitir quebra de linha */
            overflow: visible; /* Permitir overflow */
            text-overflow: clip; /* Remover ellipsis */
            flex: 1; /* Ocupar espaço disponível */
            margin-right: 8px; /* Espaço para o botão X */
            word-wrap: break-word; /* Quebrar palavras longas */
            word-break: break-word; /* Quebrar palavras se necessário */
            padding-top: 2px; /* Pequeno padding para alinhamento visual */
            cursor: pointer; /* Indicar que é clicável */
            transition: background-color 0.2s ease;
        }

        .selected-item .item-name:hover {
            background-color: rgba(25, 118, 210, 0.1);
            border-radius: 3px;
        }

        /* Campo de input para editar nome do item */
        .selected-item .item-name-input {
            font-weight: 600;
            color: #1976d2;
            font-size: 14px;
            line-height: 1.3;
            flex: 1;
            margin-right: 8px;
            border: none;
            background: transparent;
            outline: none;
            padding: 2px 4px;
            border-radius: 3px;
            background-color: rgba(25, 118, 210, 0.1);
            border: 1px solid #1976d2;
        }

        .selected-item .item-name-input:focus {
            background-color: rgba(25, 118, 210, 0.15);
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }

        @media (max-width: 768px) {
            .selected-item {
                min-height: 32px; /* Altura mínima dos inputs em tablet */
                padding: 6px 28px 6px 10px; /* Padding ajustado para tablet */
                margin-top: 0; /* Sem offset */
                margin-bottom: 0 !important;
                align-self: end; /* Alinhar com a parte inferior */
                font-size: 13px; /* Fonte menor para tablet */
                align-items: flex-start; /* Manter alinhamento no topo */
            }

            .selected-item .item-name {
                font-size: 13px;
                line-height: 1.3;
                padding-top: 1px; /* Ajuste fino para tablet */
            }

            .selected-item .item-name-input {
                font-size: 13px;
                line-height: 1.3;
                padding: 1px 4px;
            }

            .btn-clear-item {
                top: 50% !important; /* Centralizar verticalmente */
                right: 6px !important;
                width: 18px !important;
                height: 18px !important;
                font-size: 14px !important;
                transform: translateY(-50%) !important; /* Ajuste fino para centralização */
            }
        }
        @media (max-width: 480px) {
            .selected-item {
                min-height: 32px; /* Altura mínima para mobile */
                padding: 4px 24px 4px 8px; /* Padding menor para mobile */
                margin-top: 0; /* Sem offset */
                margin-bottom: 0 !important;
                align-self: end; /* Alinhar com a parte inferior */
                font-size: 12px; /* Fonte menor para mobile */
                align-items: flex-start; /* Manter alinhamento no topo */
            }

            .selected-item .item-name {
                font-size: 12px;
                margin-right: 6px; /* Menos espaço para o botão X */
                line-height: 1.3;
                padding-top: 1px; /* Ajuste fino para mobile */
            }

            .selected-item .item-name-input {
                font-size: 12px;
                line-height: 1.3;
                padding: 1px 3px;
                margin-right: 6px;
            }

            .btn-clear-item {
                top: 50% !important; /* Centralizar verticalmente */
                right: 4px !important;
                width: 16px !important;
                height: 16px !important;
                font-size: 12px !important;
                transform: translateY(-50%) !important; /* Ajuste fino para centralização */
            }
        }



        /* Regra genérica removida - usando regras específicas para cada container */

        .search-result-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .search-result-item:hover {
            background-color: #f8f9fa;
        }


        .search-result-item.new-item {
            background-color: #f0f8ff;
            border-bottom: none;
            color: #007bff;
            font-weight: 500;
        }

        .search-result-item.new-item:hover {
            background-color: #e3f2fd;
        }

        .no-results {
            padding: 15px;
            text-align: center;
            color: #666;
        }

        .no-results p {
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        /* Estilos do .btn-clear-item movidos para cima para evitar duplicação */

        .items-table-section {
            margin-bottom: 8px; /* Mesmo espaçamento do add-item-section */
        }





        .item-actions .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
            width: 100%;
        }

        .item-actions .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .item-actions .btn-primary:hover {
            background-color: #0056b3;
        }

        .item-actions .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .item-actions .btn-danger:hover {
            background-color: #c82333;
        }

        .item-actions-row {
            display: none;
        }

        .item-actions-row td {
            padding: 0;
        }

        .items-table-section {
            margin-bottom: 8px; /* Mesmo espaçamento do add-item-section */
            width: 100%;
        }

        .items-table {
            width: 100%;
            max-width: 100%;
            border-collapse: separate; /* Necessário para border-radius */
            border-spacing: 0; /* Remove espaçamento entre células */
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px; /* Cantos arredondados como outros elementos */
            table-layout: fixed;
            min-height: auto;
            overflow: hidden; /* Garante que o conteúdo respeite o border-radius */
        }

        .items-table th,
        .items-table td {
            padding: 8px 10px; /* Aumentar padding para melhor espaçamento */
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            line-height: 1.3; /* Melhor line-height para quebras de linha */
            vertical-align: middle; /* Centralização vertical */
        }

        .items-table th {
            background-color: #e9ecef; /* Fundo mais escuro para destaque */
            font-weight: 600; /* Peso da fonte mais forte */
            color: #343a40; /* Cor mais escura para melhor contraste */
            border-bottom: 2px solid #ced4da; /* Borda mais escura */
            height: 35px;
            text-transform: uppercase; /* Títulos em maiúsculo */
            font-size: 12px; /* Fonte menor para maiúsculas */
            letter-spacing: 0.5px; /* Espaçamento entre letras */
        }

        /* Cantos arredondados para primeira e última célula do cabeçalho */
        .items-table th:first-child {
            border-top-left-radius: 7px; /* Ligeiramente menor que a tabela */
        }

        .items-table th:last-child {
            border-top-right-radius: 7px; /* Ligeiramente menor que a tabela */
        }

        .items-table tbody tr {
            background-color: white;
            min-height: 40px; /* Altura mínima em vez de fixa */
            height: auto; /* Altura automática para se adaptar ao conteúdo */
            transition: background-color 0.2s ease;
        }

        /* Linhas intercaladas sutis */
        .items-table tbody tr:nth-child(even) .swipe-item-row {
            background-color: #f8f9fa; /* Cor sutil para linhas pares */
        }

        .items-table tbody tr:nth-child(even) .item-cell {
            background-color: #f8f9fa;
        }

        .items-table tbody tr:hover .swipe-item-row,
        .items-table tbody tr:hover .item-cell {
            background-color: #e9ecef !important; /* Hover mais visível que sobrescreve intercalado */
        }

        /* Cantos arredondados para última linha da tabela */
        .items-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 7px;
        }

        .items-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 7px;
        }

        /* Altura mínima da tabela quando vazia */
        .items-table tbody:empty::after {
            content: "";
            display: block;
            height: 40px;
        }

        /* Garantir que a tabela vazia mantenha os cantos arredondados */
        .items-table tbody:empty {
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }

        /* === ESTILOS DO BLOCO DE TOTAIS === */
        .totals-section {
            background-color: transparent; /* Remover fundo colorido */
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0px 8px 0px 8px; /* Remover padding superior e inferior para aproximar das bordas */
            margin-top: 8px; /* Mesmo espaçamento dos outros elementos */
            overflow: hidden; /* Previne transbordo */
            max-width: 100%; /* Limita largura */
            box-sizing: border-box; /* Inclui padding no cálculo da largura */
        }

        .totals-row-group {
            display: flex;
            gap: clamp(4px, 2vw, 8px); /* Gap responsivo */
            margin-bottom: 2px;
            overflow: hidden; /* Previne transbordo */
            max-width: 100%; /* Limita largura */
        }

        .totals-row-group:last-child {
            margin-bottom: 0;
        }

        /* Linha separadora entre taxa/desconto e subtotal/total */
        .totals-row-group:first-child {
            /* jonatas border-bottom: 1px solid #dee2e6; /* Linha fina separadora */
            padding-top: 4px; /* Pequeno espaço do topo para Tax/Discount */
            padding-bottom: 1px; /* Espaço abaixo da linha */
            margin-bottom: 8px; /* Espaço antes do próximo grupo */
        }

        /* Aproximar campos subtotal/total das bordas inferiores */
        .totals-row-group:last-child {
            margin-bottom: 0; /* Remover margem inferior para aproximar da borda */
            padding-bottom: 4px; /* Pequeno espaço da borda inferior para Subtotal/Total */
        }

        .totals-field {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .totals-label {
            font-size: 13px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 1px;
        }

        .input-with-dropdown {
            display: flex;
            align-items: center;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            background-color: white;
            height: 38px;
        }

        .input-with-dropdown input {
            flex: 1;
            border: none;
            /*jonatas padding: 9px 12px;*/
            font-size: 14px;
            outline: none;
            background: transparent;
            height: 100%;
            min-width: 0;
            box-sizing: border-box;
        }

        .input-with-dropdown select {
            border: none;
            background-color: #f8f9fa;
            padding: 9px 8px;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            cursor: pointer;
            outline: none;
            width: 45px;
            min-width: 45px;
            max-width: 45px;
            height: 100%;
            border-left: 2px solid #e9ecef;
            box-sizing: border-box;
        }

        .totals-field input[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 14px;
        }

        .grand-total .totals-label {
            color: #28a745;
            font-size: 18px !important;
        }

        .grand-total input {
            background-color: #d4edda !important;
            border-color: #28a745 !important;
            color: #155724 !important;
            font-weight: 600;
        }

        /* Estilos para campos de display (subtotal e total) */
        .totals-display-field {
            flex: 1;
            display: flex;
            align-items: center;
            gap: clamp(4px, 2vw, 8px); /* Gap responsivo */
            padding: 8px 12px;
            background-color: transparent; /* Sem fundo */
            border: none; /* Sem borda */
            border-radius: 0; /* Sem cantos arredondados */
            min-height: 40px;
            overflow: visible; /* Alterado para visible para evitar corte de texto */
            max-width: 100%; /* Limita largura máxima */
            box-sizing: border-box; /* Inclui padding no cálculo da largura */
        }

        .totals-display-label {
            font-size: 16px !important; /* 16px */
            font-weight: 500; /* Peso normal para subtotal */
            color: #495057;
            white-space: nowrap;
            overflow: hidden; /* Previne transbordo */
            text-overflow: ellipsis; /* Adiciona ... se necessário */
            flex-shrink: 0; /* Não encolhe */
            max-width: 60%; /* Limita largura máxima */
        }

        .totals-display-value {
            font-size: 16px !important; /* 16px */
            font-weight: 400; /* Peso normal para subtotal */
            color: #333;
            margin-left: auto; /* Empurra o valor para a direita */
            overflow: hidden; /* Previne transbordo */
            text-overflow: ellipsis; /* Adiciona ... se necessário */
            white-space: nowrap; /* Não quebra linha */
            flex-shrink: 1; /* Pode encolher se necessário */
            max-width: 40%; /* Limita largura máxima */
            text-align: right; /* Alinha texto à direita */
        }

        /* Estilo específico para o total (negrito e maior) */
        .totals-display-total {
            font-weight: 700 !important; /* Negrito apenas para o valor do total */
            font-size: 18px !important; /* 18px */
            color: #333 !important; /* Cor normal, sem destaque colorido */
        }

        /* Label do total também em negrito e maior */
        .grand-total .totals-display-label {
            font-weight: 700 !important; /* Label "Total:" em negrito */
            font-size: 18px !important; /* 18px */
        }

        /* Overrides finais para garantir tamanhos no resumo Subtotal/Total (Android/iOS) */
        .totals-row-group .totals-display-label,
        .totals-row-group .totals-display-value {
            font-size: 16px !important;
            line-height: 1.25 !important;
        }

        .totals-row-group .grand-total .totals-display-label,
        .totals-row-group .grand-total .totals-display-value {
            font-size: 18px !important;
        }

        /* Garante que Subtotal fique em 16px (sem afetar Total) */
        .totals-row-group .totals-display-field:not(.grand-total) .totals-display-label,
        .totals-row-group .totals-display-field:not(.grand-total) .totals-display-value {
            font-size: 16px !important;
        }

        /* === FORÇA TAMANHOS IDÊNTICOS ENTRE iOS E ANDROID === */

        /* Força tamanhos universais - sobrescreve qualquer diferença entre dispositivos */
        @supports (-webkit-touch-callout: none) {
            /* iOS Safari - força os mesmos tamanhos do Android */
            .totals-display-label {
                font-size: 16px !important;
                line-height: 1.25 !important;
                font-weight: 500 !important;
            }

            .totals-display-value {
                font-size: 16px !important;
                line-height: 1.25 !important;
                font-weight: 400 !important;
            }

            .grand-total .totals-display-label {
                font-size: 18px !important;
                line-height: 1.25 !important;
                font-weight: 700 !important;
            }

            .grand-total .totals-display-value,
            .totals-display-total {
                font-size: 18px !important;
                line-height: 1.25 !important;
                font-weight: 700 !important;
            }

            /* Mobile - força os mesmos tamanhos em ambos os dispositivos */
            @media (max-width: 480px) {
                .totals-display-label,
                .totals-display-value {
                    font-size: 13px !important;
                    line-height: 1.2 !important;
                }

                .grand-total .totals-display-label,
                .grand-total .totals-display-value,
                .totals-display-total {
                    font-size: 15px !important;
                    line-height: 1.2 !important;
                }
            }

            /* Telas muito pequenas - força os mesmos tamanhos */
            @media (max-width: 360px) {
                .totals-display-label,
                .totals-display-value {
                    font-size: 12px !important;
                    line-height: 1.2 !important;
                }

                .grand-total .totals-display-label,
                .grand-total .totals-display-value,
                .totals-display-total {
                    font-size: 13px !important;
                    line-height: 1.2 !important;
                }
            }
        }


        /* Larguras das colunas simples */
        .items-table th:nth-child(1),
        .items-table td:nth-child(1) {
            width: 55%;
            text-align: left;
            overflow: visible; /* Permitir overflow para quebra de linha */
            text-overflow: clip; /* Remover ellipsis */
            white-space: normal; /* Permitir quebra de linha */
            word-wrap: break-word; /* Quebrar palavras longas */
            word-break: break-word; /* Quebrar palavras se necessário */
            color: #333;
            vertical-align: top; /* Alinhar no topo para textos longos */
            line-height: 1.4; /* Line-height específico para o nome */
        }

        .items-table th:nth-child(2),
        .items-table td:nth-child(2) {
            width: 8%;
            text-align: center;
            min-width: 40px;
            color: #666;
            vertical-align: middle; /* Centralização vertical */
            white-space: nowrap; /* Não quebrar linha */
        }

        .items-table th:nth-child(3),
        .items-table td:nth-child(3) {
            width: 18.5%;
            text-align: right;
            color: #666;
            vertical-align: middle; /* Centralização vertical */
            white-space: nowrap; /* Não quebrar linha */
        }

        .items-table th:nth-child(4),
        .items-table td:nth-child(4) {
            width: 18.5%;
            text-align: right;
            color: #333;
            font-weight: 500;
            vertical-align: middle; /* Centralização vertical */
            white-space: nowrap; /* Não quebrar linha */
            position: relative; /* Para posicionar o botão de remover */
        }

        /* Estilos para edição inline na tabela */
        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: background-color 0.2s ease;
        }

        .editable-cell:hover {
            background-color: #f0f8ff !important;
        }

        .editable-cell .cell-value {
            display: block;
            width: 100%;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .editable-cell .cell-input {
            width: 100%;
            border: 2px solid #007bff;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 14px;
            font-family: inherit;
            background-color: white;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
            outline: none;
        }

        .editable-cell .cell-input:focus {
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
        }

        /* Estilos para swipe */
        .swipe-row {
            position: relative;
            overflow: hidden;
        }

        .swipe-container {
            position: relative;
            padding: 0 !important;
            background: transparent;
            border-bottom: 1px solid #f0f0f0;
        }

        .swipe-content {
            position: relative;
            background: white;
            transition: transform 0.3s ease;
            z-index: 2;
            touch-action: pan-y;
            user-select: none;
            -webkit-user-select: none;
        }

        .swipe-item-row {
            display: flex;
            align-items: stretch;
            min-height: 40px;
            background: white;
            position: relative;
        }

        .item-cell {
            padding: 8px 10px;
            font-size: 14px;
            line-height: 1.3;
            vertical-align: middle;
            background: white;
            position: relative;
            z-index: 3;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        /* Padding específico para a coluna total */
        .item-cell:nth-child(4) {
            padding: 8px 2px 8px 10px;
        }

        .item-cell.editable-cell {
            cursor: pointer;
            z-index: 4;
        }

        .item-cell.total-cell {
            cursor: grab;
            z-index: 2;
        }

        .item-cell.total-cell:active {
            cursor: grabbing;
        }

        /* Larguras das colunas para o swipe */
        .item-cell:nth-child(1) {
            flex: 1;
            min-width: 0;
            text-align: left;
            overflow: visible;
            text-overflow: clip;
            white-space: normal;
            word-wrap: break-word;
            color: #333;
            vertical-align: top;
            line-height: 1.4;
        }

        .item-cell:nth-child(2) {
            width: 60px;
            flex-shrink: 0;
            text-align: center;
            min-width: 40px;
            color: #666;
            white-space: nowrap;
        }

        .item-cell:nth-child(3) {
            width: 80px;
            flex-shrink: 0;
            text-align: right;
            color: #666;
            white-space: nowrap;
        }

        .item-cell:nth-child(4) {
            width: 75px;
            flex-shrink: 0;
            text-align: right;
            color: #333;
            font-weight: 500;
            white-space: nowrap;
            padding-right: 0;
        }

        .swipe-delete-btn {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 80px;
            background: #dc3545;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1;
            transition: background-color 0.2s ease;
        }

        .swipe-delete-btn:hover {
            background: #c82333;
        }

        .swipe-delete-btn i {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .swipe-delete-btn span {
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Indicador visual para campos editáveis */
        .editable-cell::after {
            content: '✏️';
            position: absolute;
            right: 4px;
            top: 2px;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .editable-cell:hover::after {
            opacity: 0.5;
        }

        /* Esconder indicador na coluna total */
        .total-cell::after {
            display: none;
        }



        /* Modal Popup para ações do item */
        .item-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease;
        }

        .item-modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: slideIn 0.3s ease;
        }

        .item-modal-header {
            background-color: #f8f9fa;
            color: #333;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .item-modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .item-modal-body {
            padding: 30px 20px;
            text-align: center;
        }

        .item-modal-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .modal-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modal-btn-edit {
            background-color: #007bff;
            color: white;
        }

        .modal-btn-edit:hover {
            background-color: #0056b3;
        }

        .modal-btn-delete {
            background-color: #dc3545;
            color: white;
        }

        .modal-btn-delete:hover {
            background-color: #c82333;
        }

        .modal-close {
            position: absolute;
            top: 12px;
            right: 15px;
            color: #666;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s;
        }

        .modal-close:hover {
            opacity: 1;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }



        /* === RESPONSIVIDADE PARA TABLETS === */
        @media (max-width: 768px) {
            .add-item-section {
                padding: 3px 8px 8px 8px; /* Mesmo padding da seção de totais em tablet */
                background-color: transparent; /* Garantir fundo transparente em tablet */
                border: none; /* Sem borda por padrão em tablet */
            }

            .add-item-section.has-selection {
                border: 1px solid #e0e0e0; /* Borda apenas quando selecionado em tablet */
            }

            .add-item-form {
                grid-template-columns: 1fr 60px 80px;
                gap: 8px;
            }


            .add-item-btn-inline {
                height: 32px;
                font-size: 16px;
            }







            .add-item-form .form-group label {
                font-size: 12px;
                margin-bottom: 2px;
            }

            .add-item-form .form-group input,
            .add-item-form .form-group select {
                font-size: 14px;
                padding: 6px;
                height: 32px;
            }

            /* Ajustar altura dos campos qty e price para alinhar com chip em tablet */
            .add-item-form .field-qty input,
            .add-item-form .field-price input {
                height: 32px !important;
                align-self: flex-end; /* Alinhar na parte inferior do flexbox */
                margin: 0 !important; /* Sem margens */
                flex-shrink: 0; /* Não encolher */
            }

            /* Form-groups sem espaço extra em tablet */
            .add-item-form .field-qty,
            .add-item-form .field-price {
                margin: 0 !important;
                padding: 0 !important;
                height: 100% !important;
            }



            .items-table {
                font-size: 13px;
            }

            .items-table th,
            .items-table td {
                padding: 6px 8px;
            }

            .items-table th {
                height: 30px;
                font-size: 11px; /* Fonte menor para tablet */
                letter-spacing: 0.3px; /* Espaçamento menor para tablet */
            }

            .items-table tbody tr {
                min-height: 35px; /* Altura mínima em vez de fixa */
                height: auto; /* Altura automática para se adaptar ao conteúdo */
            }

            .items-table th,
            .items-table td {
                padding: 12px 8px;
                font-size: 13px;
            }

            /* Estilos responsivos para edição inline no tablet */
            .editable-cell .cell-input {
                font-size: 13px;
                padding: 5px 7px;
            }



            /* Estilos responsivos para swipe no tablet */
            .item-cell {
                padding: 6px 8px;
                font-size: 13px;
            }

            .item-cell:nth-child(2) {
                width: 50px;
            }

            .item-cell:nth-child(3) {
                width: 70px;
            }

            .item-cell:nth-child(4) {
                width: 65px;
                padding: 6px 1px 6px 8px;
            }

            .swipe-delete-btn {
                width: 70px;
            }

            .swipe-delete-btn i {
                font-size: 14px;
            }

            .swipe-delete-btn span {
                font-size: 9px;
            }

        }

        /* === RESPONSIVIDADE PARA SMARTPHONES === */
        @media (max-width: 480px) {
            .add-item-form {
                grid-template-columns: 1fr 50px 70px;
                gap: 6px;
            }


            .add-item-btn-inline {
                height: 30px;
                font-size: 14px;
            }









            .add-item-section {
                padding: 2px 6px 6px 6px; /* Mesmo padding da seção de totais em mobile */
                background-color: transparent; /* Garantir fundo transparente em mobile */
                border: none; /* Sem borda por padrão em mobile */
            }

            .add-item-section.has-selection {
                border: 1px solid #e0e0e0; /* Borda apenas quando selecionado em mobile */
            }

            /* Ajustar altura dos campos qty e price para alinhar com chip em mobile */
            .add-item-form .field-qty input,
            .add-item-form .field-price input {
                height: 32px !important;
                padding: 4px 6px !important;
                align-self: flex-end; /* Alinhar na parte inferior do flexbox */
                margin: 0 !important; /* Sem margens */
                flex-shrink: 0; /* Não encolher */
            }

            /* Form-groups sem espaço extra em mobile */
            .add-item-form .field-qty,
            .add-item-form .field-price {
                margin: 0 !important;
                padding: 0 !important;
                height: 100% !important;
            }

            .items-table {
                font-size: 12px;
            }

            .items-table th,
            .items-table td {
                padding: 6px 6px; /* Aumentar padding vertical para mobile */
                vertical-align: middle; /* Manter centralização vertical */
            }

            .items-table th {
                height: 28px;
                font-size: 10px; /* Fonte menor para mobile */
                letter-spacing: 0.2px; /* Espaçamento menor para mobile */
            }

            .items-table tbody tr {
                min-height: 32px; /* Altura mínima em vez de fixa */
                height: auto; /* Altura automática para se adaptar ao conteúdo */
            }

            /* Modal responsivo para mobile */
            .item-modal-content {
                margin: 20% auto;
                width: 95%;
                max-width: 350px;
            }

            .item-modal-header {
                padding: 15px;
            }

            .item-modal-header h3 {
                font-size: 16px;
            }

            .item-modal-body {
                padding: 20px 15px;
            }

            .modal-btn {
                padding: 10px 15px;
                font-size: 13px;
            }

            .items-table th,
            .items-table td {
                padding: 8px 6px; /* Padding otimizado para mobile */
                font-size: 12px;
                vertical-align: middle; /* Centralização vertical */
            }

            /* Estilos responsivos para edição inline no mobile */
            .editable-cell .cell-input {
                font-size: 12px;
                padding: 4px 6px;
                border-width: 1px;
            }



            /* Estilos responsivos para swipe no mobile */
            .item-cell {
                padding: 6px 6px;
                font-size: 12px;
            }

            .swipe-item-row {
                min-height: 32px;
            }

            .item-cell:nth-child(2) {
                width: 40px;
                font-size: 11px;
            }

            .item-cell:nth-child(3) {
                width: 60px;
                font-size: 11px;
            }

            .item-cell:nth-child(4) {
                width: 55px;
                font-size: 11px;
                padding: 6px 1px 6px 6px;
            }

            .swipe-delete-btn {
                width: 60px;
            }

            .swipe-delete-btn i {
                font-size: 12px;
            }

            .swipe-delete-btn span {
                font-size: 8px;
            }

            /* Indicador de edição menor no mobile */
            .editable-cell::after {
                font-size: 8px;
                right: 2px;
                top: 1px;
            }



            /* Coluna do nome com quebra de linha em mobile */
            .items-table td:nth-child(1) {
                vertical-align: top; /* Alinhar no topo para textos longos */
                line-height: 1.3; /* Line-height específico para mobile */
                padding: 8px 6px; /* Padding específico para nome */
            }

            .items-table th {
                padding: 12px 6px;
                font-size: 10px; /* Fonte menor para mobile */
                letter-spacing: 0.2px; /* Espaçamento menor para mobile */
            }

            .item-actions {
                gap: 6px;
                padding: 6px;
            }

            .item-actions .btn {
                padding: 6px 10px;
                font-size: 12px;
            }
        }

        /* === OTIMIZAÇÕES ESPECÍFICAS PARA iOS === */
        @supports (-webkit-touch-callout: none) {
            .items-table-container {
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            .items-table tbody tr {
                -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            }

            .item-actions .btn {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                border-radius: 8px;
            }
        }

        /* === OTIMIZAÇÕES PARA ANDROID === */
        @media (pointer: coarse) {
            .items-table tbody tr {
                min-height: 48px;
            }

            .item-actions .btn {
                min-height: 44px;
                touch-action: manipulation;
            }
        }

        /* === MODO ESCURO REMOVIDO === */
        /* Regras de modo escuro removidas para evitar ativação automática no iPhone */

        /* Estilos para campos de pagamento */
        #payment_details {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 8px;
            margin-top: 6px;
            transition: all 0.3s ease;
        }

        #payment_details.show {
            display: block !important;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #payment_status {
            border: 2px solid #007bff;
            border-radius: 6px;
            padding: 8px 12px;
            font-weight: 500;
        }

        #payment_status:focus {
            border-color: #0056b3;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .form-text.text-muted {
            font-size: 0.875rem;
            color: #6c757d !important;
            margin-top: 5px;
        }

        .payment-field-required {
            border-color: #dc3545 !important;
        }

        .payment-field-valid {
            border-color: #28a745 !important;
        }

        /* Reduzir espaçamento dos títulos das seções */
        h3 {
            margin-top: 12px !important;
            margin-bottom: 8px !important;
            font-size: 16px !important;
        }

        /* Garantir que body e html usem toda a largura */
        html, body {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden; /* Previne scroll horizontal */
        }

        /* Container otimizado para usar toda a tela */
        .container-full {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 0 8px; /* Padding mínimo nas laterais */
            box-sizing: border-box;
        }

        /* Primeira seção sem margin-top */
        .container h3:first-of-type,
        .container-full h3:first-of-type {
            margin-top: 0 !important;
        }

        /* Espaçamento geral entre seções */
        .form-section {
            margin-bottom: 8px;
        }

        /* Reduzir espaço acima do primeiro campo (Cliente) */
        .card-body .form-group:first-of-type {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }

        /* Reduzir consideravelmente o padding superior do card-body */
        .card-body {
            padding-top: 4px !important; /* Reduzido de 12px para 4px */
        }

        /* Estilos para seção expansível de datas - minimalista */
        .expandable-section {
            border: 1px solid #e0e0e0;
            border-radius: 3px;
            margin-bottom: 8px;
            overflow: visible;
        }

        .expandable-header {
            background-color: #f8f9fa;
            padding: 3px 8px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
            transition: background-color 0.2s;
            min-height: 20px;
        }

        .expandable-header:hover {
            background-color: #e9ecef;
        }

        .expandable-header h4 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            color: #495057;
        }

        .expandable-icon {
            font-size: 10px;
            transition: transform 0.2s;
            color: #6c757d;
        }

        .expandable-section.expanded .expandable-icon {
            transform: rotate(180deg);
        }

        .expandable-content {

            overflow: visible;
            transition: max-height 0.3s ease-out;
            background-color: #fff;
        }

        .expandable-section.expanded .expandable-content {
            max-height: 200px;
        }

        .expandable-content-inner {
            padding: 8px;
        }

        /* Otimização para mobile - card usa toda a largura */
        @media (max-width: 768px) {
            .container-full {
                padding: 0 4px; /* Padding ainda menor em mobile */
            }

            .card {
                margin: 0; /* Remove margens laterais */
                border-radius: 8px; /* Mantém bordas arredondadas */
                border-left: 1px solid #dee2e6; /* Mantém bordas */
                border-right: 1px solid #dee2e6;
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }

            .card-body {
                padding: 12px 8px; /* Padding otimizado */
            }

            /* Reduz padding do main-content em mobile */
            .main-content {
                padding: 0.5rem 0.25rem; /* Padding reduzido */
            }

            .expandable-header {
                padding: 2px 6px;
                min-height: 18px;
            }

            .expandable-header h4 {
                font-size: 13px;
            }

            .expandable-content-inner {
                padding: 6px;
            }

            .expandable-icon {
                font-size: 9px;
            }
        }

        /* Melhorar UX para campos numéricos */
        input[type="number"] {
            text-align: right;
        }

        input[type="number"]:focus {
            background-color: #fff3cd;
            border-color: #ffc107;
        }

        /* Campo de Datas no Header */
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1rem 0px 1rem; /* Padding-bottom ajustado para 0px */
            position: relative;
            border-bottom: none !important; /* Remover qualquer borda inferior */
        }

        .header-dates-section {
            flex-shrink: 0;
            position: relative;
            border: none !important; /* Garantir que não há bordas */
        }

        /* Garantir que não há linhas visíveis quando fechado */
        .header-dates-section .expandable-section:not(.expanded)::after,
        .header-dates-section .expandable-section:not(.expanded)::before {
            display: none !important;
        }

        .header-dates-section .expandable-section:not(.expanded) {
            border: none !important;
            box-shadow: none !important;
        }

        .header-dates-section .expandable-section {
            border: none; /* Remover borda para eliminar linha cinza */
            border-radius: 3px;
            margin-bottom: 0;
        }

        .header-dates-section .expandable-header {
            background-color: #f8f9fa;
            padding: 6px 10px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-radius: 4px;
            border: none !important; /* Garantir que não há bordas no botão */
        }

        .header-dates-section .expandable-header:hover {
            background-color: #e9ecef;
        }

        .header-dates-section .expandable-header i {
            color: #6c757d;
            font-size: 18px;
            line-height: 1;
        }

        .header-dates-section .expandable-icon {
            font-size: 12px;
            transition: transform 0.2s;
            color: #6c757d;
            line-height: 1;
        }

        .header-dates-section .expandable-section.expanded .expandable-icon {
            transform: rotate(180deg);
        }

        .header-dates-section .expandable-content {
            position: absolute;
            right: 0;
            top: 100%;
            z-index: 1000;
            background: white;
            border: none; /* Remover borda quando fechado */
            border-radius: 6px;
            box-shadow: none; /* Remover sombra quando fechado */
            min-width: 320px;
            max-height: 0;
            overflow: visible;
            opacity: 0; /* Tornar completamente invisível quando fechado */
            visibility: hidden; /* Garantir que não há interação quando fechado */
            transition: max-height 0.3s ease-out, border 0.3s ease-out, box-shadow 0.3s ease-out, opacity 0.3s ease-out, visibility 0.3s ease-out;
        }

        .header-dates-section .expandable-section.expanded .expandable-content {
            max-height: 200px;
            border: 1px solid #dee2e6; /* Mostrar borda apenas quando expandido */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Mostrar sombra apenas quando expandido */
            opacity: 1; /* Tornar visível quando expandido */
            visibility: visible; /* Permitir interação quando expandido */
        }

        /* Responsivo para campo de datas no header */
        @media (max-width: 768px) {
            .card-header {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
            }

            .header-dates-section .expandable-content {
                right: 0;
                min-width: 280px;
            }

            .header-dates-section .expandable-header {
                padding: 4px 8px;
                min-height: 28px;
            }

            .header-dates-section .expandable-header i {
                font-size: 14px;
            }

            .header-dates-section .expandable-icon {
                font-size: 10px;
            }
        }

        @media (max-width: 480px) {
            .container-full {
                padding: 0 2px; /* Padding mínimo em telas pequenas */
            }

            .card {
                border-radius: 6px; /* Bordas menores em mobile */
            }

            .card-body {
                padding: 8px 6px; /* Padding ainda menor */
            }

            /* Padding ainda menor do main-content em telas pequenas */
            .main-content {
                padding: 0.25rem 0.125rem; /* Padding mínimo */
            }

            .card-header {
                padding: 0.75rem 0.75rem 0px 0.75rem; /* Padding-bottom ajustado para 0px */
            }

            .header-dates-section .expandable-content {
                min-width: 260px;
                right: -10px;
            }

            .header-dates-section .expandable-header {
                padding: 3px 6px;
                min-height: 24px;
            }

            .header-dates-section .expandable-header i {
                font-size: 12px;
            }
        }

        /* Chip do cliente melhorado */
        .selected-customer-chip {
            background-color: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 8px 40px 8px 12px; /* Padding direito maior para o botão X */
            margin-top: 6px;
            display: none; /* Oculto por padrão */
            position: relative;
            min-height: 40px;
        }

        .selected-customer-chip .customer-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
            width: 100%;
        }

        .selected-customer-chip .customer-name {
            font-weight: 600;
            color: #1976d2;
            font-size: 14px;
            line-height: 1.2;
        }

        .selected-customer-chip .customer-phone,
        .selected-customer-chip .customer-email {
            font-size: 12px;
            color: #666;
            line-height: 1.2;
        }

        .selected-customer-chip .btn-clear-customer {
            background: none !important;
            border: none !important;
            color: #f44336 !important;
            font-size: 18px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            padding: 4px !important;
            margin: 0 !important;
            line-height: 1 !important;
            width: 24px !important;
            height: 24px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            border-radius: 50% !important;
            flex-shrink: 0 !important;
            position: absolute !important;
            right: 8px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
        }

        .selected-customer-chip .btn-clear-customer:hover {
            color: #d32f2f !important;
            background-color: rgba(244, 67, 54, 0.1) !important;
        }

        /* Responsivo para layout lado a lado */
        @media (max-width: 768px) {
            .customer-dates-row {
                flex-direction: column;
                gap: 12px;
            }

            .dates-section {
                flex: none;
                order: -1; /* Coloca as datas acima do cliente em mobile */
            }

            .customer-section {
                order: 1;
            }

            .selected-customer-chip {
                padding: 6px 32px 6px 10px; /* Padding direito menor em mobile */
                min-height: 36px;
            }

            .selected-customer-chip .customer-name {
                font-size: 13px;
            }

            .selected-customer-chip .customer-phone,
            .selected-customer-chip .customer-email {
                font-size: 11px;
            }

            .selected-customer-chip .btn-clear-customer {
                font-size: 16px !important;
                width: 20px !important;
                height: 20px !important;
                right: 6px !important;
            }
        }

        /* Reduzir altura do cabeçalho */
        header {
        background: #0b5eb7 !important;
            padding: 0.5rem 10px;
        }

        .navbar {
            min-height: auto !important;
            padding: 0.25rem 10px !important;
        }

        .logo {
            font-size: 1.3rem !important;
            font-weight: bold !important;
        }

        .nav-links {
            margin: 0 !important;
        }

        .nav-links li {
            margin-left: 1rem !important;
        }

        .nav-links a {
            padding: 0.25rem 0 !important;
            font-size: 0.9rem !important;
        }

        /* Ajustes responsivos para o header reduzido */
        @media (max-width: 768px) {
            header {
            background: #0b5eb7 !important;
                padding: 0.3rem 10px;
            }

            .logo {
                font-size: 1.2rem !important;
            }

            .nav-links {
                margin-top: 0.5rem !important;
            }

            .nav-links li {
                margin: 0.25rem 0 !important;
            }
        }

        /* Modal para novo cliente */
        .modal {
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border: none;
            width: 45%;
            max-width: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s;
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 10px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #495057;
            font-size: 1.5rem;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: color 0.2s;
        }

        .close:hover,
        .close:focus {
            color: #000;
        }

        .modal-body {
            padding: 10px 20px 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .modal-body .form-group {
            margin-bottom: 5px;
        }

        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 5px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .form-row .form-group:first-child {
            flex: 2; /* Cidade um pouco maior */
        }

        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-width: none;
                margin: 2% auto;
            }
        }

        /* Modal para novo item - largura maior que o de cliente */
        #newItemModal .modal-content {
            width: 60%;
            max-width: 450px;
        }

        @media (max-width: 768px) {
            #newItemModal .modal-content {
                width: 95%;
                max-width: none;
            }
        }

        /* Modal de Sucesso da Fatura */
        #invoiceSuccessModal .modal-content {
            width: 50%;
            max-width: 500px;
        }

        #invoiceSuccessModal .btn {
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #invoiceSuccessModal .btn-primary {
            background-color: #007bff;
            color: white;
        }

        #invoiceSuccessModal .btn-primary:hover {
            background-color: #0056b3;
        }

        #invoiceSuccessModal .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        #invoiceSuccessModal .btn-secondary:hover {
            background-color: #545b62;
        }

        #invoiceSuccessModal .btn-outline-secondary {
            background-color: transparent;
            color: #6c757d;
            border: 1px solid #6c757d;
        }

        #invoiceSuccessModal .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
        }

        #invoiceSuccessModal .btn-success {
            background-color: #28a745;
            color: white;
        }

        #invoiceSuccessModal .btn-success:hover {
            background-color: #1e7e34;
        }

        @media (max-width: 768px) {
            #invoiceSuccessModal .modal-content {
                width: 95%;
                max-width: none;
                margin: 2% auto;
            }
        }

        /* Paste button inside Service Address input */
        .input-with-paste {
            position: relative;
        }
        .input-with-paste input {
            /* Manter mesma borda e estilo dos outros campos */
            padding: 10px 52px 10px 8px !important;
            border: 2px solid #e9ecef !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            color: #495057 !important;
            background-color: #fff !important;
            transition: all 0.3s ease !important;
            box-sizing: border-box !important;
            /* Altura consistente com outros campos */
            height: auto !important;
            min-height: 42px !important;
            max-height: none !important;
        }

        /* Estados focus/hover iguais aos outros campos */
        .input-with-paste input:focus {
            outline: none !important;
            border-color: #007bff !important;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
            background-color: #f8f9fa !important;
        }
        .paste-btn {
            position: absolute; right: 8px; top: 50%; transform: translateY(-50%);
            border: none; background: transparent; color: #007bff;
            opacity: 0.28; font-size: 12px; padding-top: 10px; padding-bottom: 10px; padding-right: 6px; padding-left: 10px; line-height: 1; border-radius: 4px;
        }
        .paste-btn:hover, .paste-btn:focus { opacity: 0.5; }
        .paste-btn:active { opacity: 0.65; }

        /* Responsividade para tablet */
        @media (max-width: 768px) {
            .input-with-paste input {
                padding: 9px 46px 9px 12px !important;
                font-size: 14px !important;
                height: auto !important;
                min-height: 38px !important;
                max-height: none !important;
                border: 2px solid #e9ecef !important;
                border-radius: 6px !important;
            }
            .paste-btn {
                right: 6px;
                font-size: 11px;
            }
        }

        /* Responsividade para mobile */
        @media (max-width: 480px) {
            .input-with-paste input {
                padding: 8px 42px 8px 10px !important;
                font-size: 14px !important;
                height: auto !important;
                min-height: 36px !important;
                max-height: none !important;
                border: 2px solid #e9ecef !important;
                border-radius: 5px !important;
            }
            .paste-btn {
                right: 4px;
                font-size: 10px;
                padding: 1px 4px;
            }
        }

        /* Optional: ensure the button doesn't shrink in tiny viewports */
        @media (max-width: 360px) {
            .input-with-paste input { padding-right: 40px !important; }
            .paste-btn { right: 4px; font-size: 10px; }
        }

        /* Page Header Section - New Layout */
        .page-header-section {
            margin-bottom: 1.5rem;
            /*jonatas padding-bottom: 1rem;*/
            padding: 10px 5px;
            border-bottom: 1px solid #dee2e6;
        }

        .page-title-dates-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }

        .page-title-section {
            flex: 1;
        }

        .page-title {
            margin: 0;
            color: #2c3e50;
            font-size: 1.75rem;
            font-weight: 600;
            line-height: 1.2;
        }

        .page-dates-section {
            flex-shrink: 0;
            position: relative;
        }

        /* Copiar estilos do header-dates-section para page-dates-section */
        .page-dates-section .expandable-section:not(.expanded)::after,
        .page-dates-section .expandable-section:not(.expanded)::before {
            display: none !important;
        }

        .page-dates-section .expandable-section:not(.expanded) {
            border: none !important;
            box-shadow: none !important;
        }

        .page-dates-section .expandable-section {
            border: none;
            border-radius: 3px;
            margin-bottom: 0;
        }

        .page-dates-section .expandable-header {
            background-color: #f8f9fa;
            padding: 6px 10px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-radius: 4px;
            border: none !important;
        }

        .page-dates-section .expandable-header:hover {
            background-color: #e9ecef;
        }

        .page-dates-section .expandable-header i {
            color: #6c757d;
            font-size: 18px;
            line-height: 1;
        }

        .page-dates-section .expandable-icon {
            font-size: 12px;
            transition: transform 0.2s;
            color: #6c757d;
            line-height: 1;
        }

        .page-dates-section .expandable-section.expanded .expandable-icon {
            transform: rotate(180deg);
        }

        .page-dates-section .expandable-content {
            position: absolute;
            right: 5px;
            top: 100%;
            z-index: 1000;
            background: white;
            border: none;
            border-radius: 6px;
            box-shadow: none;
            min-width: 320px;
            max-height: 0;
            overflow: visible;
            opacity: 0;
            visibility: hidden;
            transition: max-height 0.3s ease-out, border 0.3s ease-out, box-shadow 0.3s ease-out, opacity 0.3s ease-out, visibility 0.3s ease-out;
        }

        .page-dates-section .expandable-section.expanded .expandable-content {
            max-height: 200px;
            border: 1px solid #dee2e6;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            opacity: 1;
            visibility: visible;
        }

        .page-dates-section .expandable-content-inner {
           /*jonatas padding: 1px; */
        }

        /* Responsivo para page-dates-section */
        @media (max-width: 768px) {


        }

        @media (max-width: 480px) {
            .page-title-dates-row {
                gap: 0.5rem; /* Reduzir gap em telas muito pequenas */
            }

            .page-title {
                font-size: 1.25rem;
            }

            .page-dates-section .expandable-content {
                min-width: 260px;
                right: 5px;
            }

            .page-dates-section .expandable-header {
                padding: 3px 6px;
                min-height: 24px;
            }

            .page-dates-section .expandable-header i {
                font-size: 12px;
            }
        }

    </style>


</head>
<body>
    <!-- Mobile Header (igual ao dashboard) -->
    <header class="mobile-header">
        <h1>Tony's AC Repair</h1>
        <button class="menu-toggle" id="menuToggle" aria-expanded="false" aria-controls="slideMenu" aria-label="Toggle navigation menu">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Slide Menu (igual ao dashboard) -->
    <nav class="slide-menu" id="slideMenu" role="navigation" aria-label="Main navigation">
        <div class="menu-grid">
            <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">
                <i class="fa fa-home"></i>
                <span>Painel</span>
            </a>
            <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">
                <i class="fa fa-users"></i>
                <span>Clientes</span>
            </a>
            <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">
                <i class="fa fa-box"></i>
                <span>Estoque</span>
            </a>
            <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice"></i>
                <span>Orçamentos</span>
            </a>
            <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice-dollar"></i>
                <span>Faturas</span>
            </a>
            <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php') ? 'active' : ''; ?>">
                <i class="fa fa-headset"></i>
                <span>Chamados</span>
            </a>
            <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">
                <i class="fa fa-share-alt"></i>
                <span>Links</span>
            </a>
            <a href="logout.php" class="menu-item">
                <i class="fa fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </nav>

    <!-- Menu Overlay (igual ao dashboard) -->
    <div class="menu-overlay" id="menuOverlay" aria-hidden="true"></div>

    <!-- Menu lateral removido -->
    <div class="main-content">
        <div class="container-full">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success) && !isset($show_success_modal)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <form method="POST" action="invoice_create.php" id="invoice-form">
                        <?php if ($quote_id > 0): ?>
                            <input type="hidden" name="quote_id" value="<?php echo $quote_id; ?>">
                        <?php endif; ?>

                        <!-- Page Title and Dates Section -->
                        <div class="page-header-section">
                            <div class="page-title-dates-row">
                                <div class="page-title-section">
                                    <h1 class="page-title">New Invoice</h1>
                                </div>
                                <div class="page-dates-section">
                                    <div class="expandable-section" id="page-dates-section">
                                        <div class="expandable-header" onclick="togglePageDatesSection()">
                                            <i class="fas fa-calendar-alt"></i>
                                            <span class="expandable-icon">▼</span>
                                        </div>
                                        <div class="expandable-content">
                                            <div class="expandable-content-inner">
                                                <div class="form-row">
                                                    <div class="form-group">
                                                        <label for="invoice_date">Invoice Date *</label>
                                                        <input type="date" id="invoice_date" name="invoice_date" value="<?php echo $invoice_date; ?>" required onchange="updateDueDate()">
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="due_date">Due Date</label>
                                                        <input type="date" id="due_date" name="due_date" value="<?php echo $due_date; ?>">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Section -->
                        <div class="form-group">
                            <label for="customer_search" class="label-with-icon">
                                <i class="fas fa-user label-icon"></i>
                                Customer *
                            </label>
                            <div class="customer-search-container">
                                <input type="text" id="customer_search" placeholder="Search customer..." enterkeyhint="next" inputmode="text" autocomplete="off" onkeydown="handleCustomerSearchKeydown(event)" <?php echo ($quote_id > 0) ? 'readonly' : ''; ?>>
                                <i class="fas fa-search search-icon"></i>
                                <input type="hidden" id="customer_id" name="customer_id" value="<?php echo $customer_id; ?>">
                                <div id="customer_results" class="search-results"></div>
                            </div>
                        </div>

                        <!-- Chip do cliente embaixo do campo de busca -->
                        <div id="selected_customer" class="selected-customer-chip" style="display: none;">
                            <?php if ($customer_id > 0): ?>
                                <?php
                                $selected_customer_result = db_query("SELECT name, phone, email FROM customers WHERE id = $customer_id");
                                if ($selected_customer_result && db_num_rows($selected_customer_result) > 0) {
                                    $selected_customer = db_fetch_assoc($selected_customer_result);
                                    echo '<div class="customer-info">';
                                    echo '<span class="customer-name">' . htmlspecialchars($selected_customer['name']) . '</span>';
                                    if (!empty($selected_customer['phone'])) {
                                        echo '<span class="customer-phone">Tel: ' . htmlspecialchars($selected_customer['phone']) . '</span>';
                                    }
                                    if (!empty($selected_customer['email'])) {
                                        echo '<span class="customer-email">Email: ' . htmlspecialchars($selected_customer['email']) . '</span>';
                                    }
                                    echo '</div>';
                                    echo '<button type="button" class="btn-clear-customer" onclick="clearCustomer()">×</button>';
                                }
                                ?>
                                <script>
                                    // Mostrar chip se há cliente selecionado
                                    document.addEventListener('DOMContentLoaded', function() {
                                        document.getElementById('selected_customer').style.display = 'block';
                                    });
                                </script>
                            <?php endif; ?>
                        </div>





                        <div class="form-group">
                            <label for="service_address" class="label-with-icon">
                                <i class="fas fa-home label-icon"></i>
                                Service Address
                            </label>
                            <div class="input-with-paste">
                              <input type="text" id="service_address" name="service_address" placeholder="Service address..." value="<?php echo htmlspecialchars($service_address ?? ''); ?>">
                              <button type="button" id="paste_service_address" class="paste-btn" aria-label="Colar">Paste</button>
                            </div>
                        </div>


                        <!-- Add Item Section -->
                        <div class="add-item-section" id="add_item_section">
                            <div class="add-item-form">
                                <div class="form-group field-search">
                                    <label for="item_search" class="label-with-icon">
                                        <i class="fas fa-box label-icon"></i>
                                        Add Item
                                    </label>
                                    <div class="item-search-container">
                                        <input type="text" id="item_search" placeholder="Search item..." autocomplete="off" onkeydown="handleItemSearchKeydown(event)">
                                        <i class="fas fa-search search-icon"></i>
                                        <button type="button" id="custom_item_btn" class="custom-item-btn" onclick="selectCustomItem()" title="Add AC Services" tabindex="-1">Custom</button>
                                        <div id="item_results" class="search-results"></div>
                                    </div>
                                </div>

                                <div class="form-group field-qty">
                                    <label for="item_quantity">Qty</label>
                                    <input type="number" id="item_quantity" step="0.001" min="0.001" value="1" placeholder="1" onkeydown="handleQuantityKeydown(event)">
                                </div>

                                <div class="form-group field-price">
                                    <label for="item_price">Price ($)</label>
                                    <input type="number" id="item_price" step="0.01" min="0" placeholder="0.00" onkeydown="handlePriceKeydown(event)">
                                </div>
                                <div id="selected_row" class="selected-row">
                                    <div id="selected_item" class="selected-item">
                                        <span class="item-name" onclick="editItemName()" title="Click to edit item name"></span>
                                        <input type="text" class="item-name-input" style="display: none;" placeholder="Enter item name..." onblur="saveItemName()" onkeydown="handleItemNameKeydown(event)">
                                        <button type="button" class="btn-clear-item" onclick="clearSelectedItem()">×</button>
                                    </div>
                                </div>
                                <div id="add-button-inline" class="add-button-inline">
                                    <button type="button" class="btn btn-primary add-item-btn-inline" onclick="addItemToTable()">+</button>
                                </div>

                            </div>
                        </div>


                        <!-- Tabela dinâmica de itens -->
                        <div class="items-table-section">
                            <table class="items-table" id="items-table">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Qty</th>
                                        <th>PRICE</th>
                                        <th>TOTAL</th>
                                    </tr>
                                </thead>
                                <tbody id="items-table-body">
                                    <!-- Items will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Modal para ações do item -->
                        <div id="itemModal" class="item-modal">
                            <div class="item-modal-content">
                                <div class="item-modal-header">
                                    <span class="modal-close" onclick="closeItemModal()">&times;</span>
                                    <h3 id="modalItemName">Item</h3>
                                </div>
                                <div class="item-modal-body">
                                    <p>O que você deseja fazer com este item?</p>
                                    <div class="item-modal-buttons">
                                        <button type="button" class="modal-btn modal-btn-edit" onclick="editItemFromModal(event)">
                                            ✏️ Editar
                                        </button>
                                        <button type="button" class="modal-btn modal-btn-delete" onclick="deleteItemFromModal(event)">
                                            🗑️ Excluir
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden inputs for form submission -->
                        <div id="hidden-items-inputs"></div>

                        <div class="totals-section">
                            <!-- First row: Tax and Discount side by side -->
                            <div class="totals-row-group">
                                <div class="totals-field">
                                    <label class="totals-label">Tax:</label>
                                    <div class="input-with-dropdown">
                                        <input type="number" id="tax_rate" name="tax_rate" step="0.01" min="0" value="<?php echo $tax_rate; ?>" onchange="updateTaxAmount()">
                                        <select id="tax_type" name="tax_type" onchange="updateTaxAmount()">
                                            <option value="$">$</option>
                                            <option value="%">%</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="totals-field">
                                    <label class="totals-label">Discount:</label>
                                    <div class="input-with-dropdown">
                                        <input type="number" id="discount_amount" name="discount_amount" step="0.01" min="0" value="<?php echo $discount_amount; ?>" onchange="calculateTotals()">
                                        <select id="discount_type" name="discount_type" onchange="calculateTotals()">
                                            <option value="$">$</option>
                                            <option value="%">%</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Second row: Subtotal and Total side by side -->
                            <div class="totals-row-group">
                                <div class="totals-display-field">
                                    <span class="totals-display-label">Subtotal:</span>
                                    <span class="totals-display-value" id="subtotal-display">$<?php echo number_format($subtotal, 2); ?></span>
                                    <input type="hidden" id="subtotal" name="subtotal" value="<?php echo $subtotal; ?>">
                                </div>

                                <div class="totals-display-field grand-total">
                                    <span class="totals-display-label">Total:</span>
                                    <span class="totals-display-value totals-display-total" id="total-display">$<?php echo number_format($total, 2); ?></span>
                                    <input type="hidden" id="total" name="total" value="<?php echo $total; ?>">
                                </div>
                            </div>

                            <!-- Campo oculto para valor da taxa calculado -->
                            <input type="hidden" id="tax_amount" name="tax_amount" value="<?php echo $tax_amount; ?>">
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea id="notes" name="notes" rows="1" placeholder="Additional notes about the invoice..." oninput="autoExpandTextarea(this)" onpaste="setTimeout(() => autoExpandTextarea(this), 0)"><?php echo htmlspecialchars($notes); ?></textarea>
                        </div>

                        <!-- Payment Status -->
                        <div class="form-group">
                            <label for="payment_status">Payment Status</label>
                            <select id="payment_status" name="payment_status" class="form-control">
                                <option value="unpaid">Unpaid</option>
                                <option value="paid">Paid</option>
                                <option value="partial">Partially Paid</option>
                            </select>
                        </div>

                        <!-- Additional payment fields (appear when status = partial) -->
                        <div id="payment_details" style="display: none;">
                            <div class="form-group" id="payment_amount_group">
                                <label for="payment_amount">Payment Amount</label>
                                <input type="number" id="payment_amount" name="payment_amount" step="0.01" min="0" class="form-control" placeholder="0.00">
                            </div>

                            <div class="form-group">
                                <label for="payment_method">Payment Method</label>
                                <select id="payment_method" name="payment_method" class="form-control">
                                    <option value="">Select...</option>
                                    <option value="zelle">Zelle</option>
                                    <option value="card">Card</option>
                                    <option value="cash">Cash</option>
                                    <option value="check">Check</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="payment_date">Payment Date</label>
                                <input type="date" id="payment_date" name="payment_date" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <div class="form-group">
                                <label for="payment_notes">Payment Notes</label>
                                <textarea id="payment_notes" name="payment_notes" class="form-control" rows="1" placeholder="Notes about the payment..." oninput="autoExpandTextarea(this)" onpaste="setTimeout(() => autoExpandTextarea(this), 0)"></textarea>
                            </div>
                        </div>

                        <button type="submit" class="btn">Create Invoice</button>
                    </form>
                </div>
            </div>
        </div> <!-- Fecha container-full -->
    </div> <!-- Fecha main-content -->

    <!-- Invoice Success Modal -->
    <div id="invoiceSuccessModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header" style="text-align: center; justify-content: center;">
                <h2>Invoice Created Successfully!</h2>
                <span class="close" onclick="closeInvoiceSuccessModal()" style="position: absolute; right: 20px; top: 15px;">&times;</span>
            </div>
            <div class="modal-body">
                <div style="text-align: center; margin-bottom: 20px;">
                    <i class="fas fa-check-circle" style="font-size: 48px; color: #28a745; margin-bottom: 20px;"></i>
                    <div style="margin-bottom: 25px;">
                        <p id="customerNameDisplay" style="font-size: 24px; font-weight: bold; color: #495057; margin: 0 0 10px 0;">Name: </p>
                        <p id="invoiceNumberDisplay" style="font-size: 22px; font-weight: bold; color: #007bff; margin: 0;"></p>
                    </div>
                </div>

                <div style="display: grid; gap: 10px;">
                    <button type="button" class="btn btn-primary" onclick="viewAndPrintInvoicePDF()" style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                        <i class="fas fa-print"></i>
                        View/Print PDF
                    </button>
                    <button type="button" class="btn btn-success" onclick="shareInvoicePDFLink()" id="shareInvoiceLinkBtn" style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                        <i class="fas fa-share-alt"></i>
                        Share PDF Link
                    </button>
                </div>
            </div>
        </div>
    </div>





    <!-- Modal para Novo Cliente (fora do formulário principal) -->
    <div id="newCustomerModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Customer</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="newCustomerForm">
                    <div class="form-group">
                        <label for="customer_name">Name <span style="color: red;">*</span></label>
                        <input type="text" id="customer_name" name="customer_name" required placeholder="Enter full name">
                    </div>
                    <div class="form-group">
                        <label for="customer_email">Email</label>
                        <input type="email" id="customer_email" name="customer_email" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="customer_phone">Phone</label>
                        <input type="tel" id="customer_phone" name="customer_phone" placeholder="(*************">
                    </div>
                    <div class="form-group">
                        <label for="customer_address">Street</label>
                        <input type="text" id="customer_address" name="customer_address" placeholder="123 Main Street">
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer_city">City</label>
                            <input type="text" id="customer_city" name="customer_city" placeholder="Orlando">
                        </div>
                        <div class="form-group">
                            <label for="customer_state">State</label>
                            <input type="text" id="customer_state" name="customer_state" placeholder="FL">
                        </div>
                        <div class="form-group">
                            <label for="customer_zip">ZIP</label>
                            <input type="text" id="customer_zip" name="customer_zip" placeholder="32801">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="customer_notes">Notes</label>
                        <textarea id="customer_notes" name="customer_notes" rows="3" placeholder="Additional notes about the customer"></textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Customer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para Novo Item (fora do formulário principal) -->
    <div id="newItemModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Item</h2>
                <span class="close" onclick="closeNewItemModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="newItemForm">
                    <div class="form-group">
                        <label for="item_name">Name <span style="color: red;">*</span></label>
                        <input type="text" id="item_name" name="item_name" required placeholder="Enter item name">
                    </div>
                    <div class="form-group">
                        <label for="item_sku">SKU</label>
                        <input type="text" id="item_sku" name="item_sku" placeholder="Product code">
                    </div>
                    <div class="form-group">
                        <label for="item_description">Description</label>
                        <textarea id="item_description" name="item_description" rows="2" placeholder="Item description"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item_price_input">Price <span style="color: red;">*</span></label>
                            <input type="number" id="item_price_input" name="item_price" step="0.01" min="0" required placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="item_cost">Cost</label>
                            <input type="number" id="item_cost" name="item_cost" step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="item_category">Category</label>
                        <input type="text" id="item_category" name="item_category" placeholder="Product category">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeNewItemModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer removido para eliminar linha indesejada na parte inferior -->

    <!-- Item template -->
    <template id="item-template">
        <div class="item-row">
            <div class="form-group">
                <label>Produto</label>
                <select name="item_id[]" class="item-select" required onchange="updateItemDetails(this)">
                    <option value="">Selecione um produto</option>
                    <?php foreach ($inventory as $item): ?>
                        <option value="<?php echo $item['id']; ?>" data-price="<?php echo $item['price']; ?>">
                            <?php echo htmlspecialchars($item['name']); ?> <?php echo !empty($item['sku']) ? '(' . htmlspecialchars($item['sku']) . ')' : ''; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label>Descrição</label>
                <input type="text" name="item_description[]" class="item-description">
            </div>

            <div class="form-group">
                <label>Quantidade</label>
                <input type="number" name="item_quantity[]" class="item-quantity" step="0.001" min="0.001" value="1" required onchange="calculateItemTotal(this)" placeholder="Ex: 5.33">
            </div>

            <div class="form-group">
                <label>Preço Unitário</label>
                <input type="number" name="item_price[]" class="item-price" step="0.01" min="0" required onchange="calculateItemTotal(this)">
            </div>

            <div class="form-group">
                <label>Subtotal</label>
                <input type="number" name="item_subtotal[]" class="item-subtotal" step="0.01" min="0" readonly>
            </div>

            <button type="button" class="btn btn-danger remove-item" onclick="removeItem(this)">Remover</button>
        </div>
    </template>

    <!-- <script src="js/main.js"></script> REMOVIDO - causava conflito com validação -->
    <script>
        // Initialize items on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');

            // Wait a bit to ensure all elements are rendered
            setTimeout(() => {
                initializeTotalsDropdowns();
                // Inicializar estado do botão Custom
                updateCustomButtonState();
                calculateTotals();
                setupCustomerSearch();
                setupFormValidation();
                setupPaymentStatusHandling();
                setupDateHandling();
                setupItemSearch();
                setupItemCalculation();
                setupNumericFieldsAutoSelect();
                setupAutoCloseDatesSection(); // Configurar fechamento automático do campo de datas
                loadExistingItems();
                loadFormDataFromStorage(); // Carregar dados salvos se existirem
                initializeTextareas(); // Inicializar textareas auto-expandindo
                setupEnterKeyNavigation();


	                // Pular selects $/% na navega e3o por "Pr 13ximo"
	                adjustTotalsTabOrder();

                // Código de correção manual removido - agora usando Bootstrap Input Groups
                // que não precisam de correções específicas de alinhamento
            }, 100);
        });

        // Função removida - não mais necessária com Bootstrap Input Groups

        function initializeTotalsDropdowns() {
            // Set default values to "$" for both dropdowns
            document.getElementById('tax_type').value = '$';
            document.getElementById('discount_type').value = '$';
        }

        function setupDateHandling() {
            // Set initial due date based on invoice date
            const invoiceDateInput = document.getElementById('invoice_date');
            const dueDateInput = document.getElementById('due_date');

            if (invoiceDateInput && dueDateInput) {
                // If due date is empty or same as invoice date, update it
                if (!dueDateInput.value || dueDateInput.value === invoiceDateInput.value) {
                    updateDueDate();
                }
            }
        }

        function updateDueDate() {
            const invoiceDateInput = document.getElementById('invoice_date');
            const dueDateInput = document.getElementById('due_date');

            if (invoiceDateInput && dueDateInput && invoiceDateInput.value) {
                // Create date object from invoice date (add 'T00:00:00' to avoid timezone issues)
                const invoiceDate = new Date(invoiceDateInput.value + 'T00:00:00');

                // Add 1 day
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(dueDate.getDate() + 1);

                // Format date as YYYY-MM-DD for input
                const year = dueDate.getFullYear();
                const month = String(dueDate.getMonth() + 1).padStart(2, '0');
                const day = String(dueDate.getDate()).padStart(2, '0');

                dueDateInput.value = `${year}-${month}-${day}`;
            }
        }

        function toggleDatesSection() {
            const section = document.getElementById('dates-section');
            const content = section.querySelector('.expandable-content');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                content.style.maxHeight = '0';
            } else {
                section.classList.add('expanded');
                content.style.maxHeight = content.scrollHeight + 'px';
            }
        }

        function togglePageDatesSection() {
            const section = document.getElementById('page-dates-section');
            const content = section.querySelector('.expandable-content');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                content.style.maxHeight = '0';
            } else {
                section.classList.add('expanded');
                content.style.maxHeight = content.scrollHeight + 'px';
            }
        }

        function closePageDatesSection() {
            const section = document.getElementById('page-dates-section');
            const content = section.querySelector('.expandable-content');

            if (section.classList.contains('expanded')) {
                section.classList.remove('expanded');
                content.style.maxHeight = '0';
            }
        }

        // Manter funções antigas para compatibilidade
        function toggleHeaderDatesSection() {
            togglePageDatesSection();
        }

        function closeHeaderDatesSection() {
            closePageDatesSection();
        }

        // Função para configurar o fechamento automático do campo de datas
        function setupAutoCloseDatesSection() {
            // Fechar ao clicar fora da seção de datas
            document.addEventListener('click', function(event) {
                const datesSection = document.querySelector('.page-dates-section');
                const expandedSection = document.getElementById('page-dates-section');

                // Verificar se o clique foi fora da seção de datas
                if (datesSection && !datesSection.contains(event.target)) {
                    // Se a seção estiver expandida, fechá-la
                    if (expandedSection && expandedSection.classList.contains('expanded')) {
                        closePageDatesSection();
                    }
                }
            });

            // Fechar quando focar em outros campos de input
            document.addEventListener('focusin', function(event) {
                const datesSection = document.querySelector('.page-dates-section');
                const expandedSection = document.getElementById('page-dates-section');

                // Se o foco for em um input que não está dentro da seção de datas
                if (event.target.tagName === 'INPUT' &&
                    datesSection &&
                    !datesSection.contains(event.target) &&
                    expandedSection &&
                    expandedSection.classList.contains('expanded')) {
                    closePageDatesSection();
                }
            });

            // Fechar ao pressionar a tecla Escape
            document.addEventListener('keydown', function(event) {
                const expandedSection = document.getElementById('page-dates-section');

                if (event.key === 'Escape' &&
                    expandedSection &&
                    expandedSection.classList.contains('expanded')) {
                    closePageDatesSection();
                }
            });

            // Fechar quando rolar a página (opcional, para melhor UX)
            let scrollTimeout;
            document.addEventListener('scroll', function() {
                const expandedSection = document.getElementById('page-dates-section');

                if (expandedSection && expandedSection.classList.contains('expanded')) {
                    // Usar timeout para evitar fechamento muito agressivo
                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        closePageDatesSection();
                    }, 150);
                }
            });
        }

        function setupNumericFieldsAutoSelect() {
            // Função para aplicar auto-select a um campo
            function applyAutoSelect(field) {
                // Adicionar evento de click para selecionar todo o conteúdo
                field.addEventListener('click', function() {
                    this.select();
                });

                // Adicionar evento de focus para selecionar todo o conteúdo
                field.addEventListener('focus', function() {
                    // Usar setTimeout para garantir que a seleção aconteça após o focus
                    setTimeout(() => {
                        this.select();
                    }, 10);
                });

                // Prevenir que o mouse desfaça a seleção
                field.addEventListener('mouseup', function(e) {
                    e.preventDefault();
                });
            }

            // Aplicar a todos os campos numéricos existentes
            const numericFields = document.querySelectorAll('input[type="number"]');
            numericFields.forEach(applyAutoSelect);

            // Observer para campos adicionados dinamicamente
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Verificar se o próprio node é um input numérico
                            if (node.tagName === 'INPUT' && node.type === 'number') {
                                applyAutoSelect(node);
                            }
                            // Verificar inputs numéricos dentro do node
                            const newNumericFields = node.querySelectorAll('input[type="number"]');
                            newNumericFields.forEach(applyAutoSelect);
                        }
                    });
                });
            });

            // Observar mudanças no documento
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        function setupFormValidation() {
            const form = document.getElementById('invoice-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const customerId = document.getElementById('customer_id').value;

                    if (!customerId || customerId == '0') {
                        e.preventDefault();
                        alert('Por favor, selecione um cliente antes de criar a fatura.');
                        return false;
                    }

                    // Check if there are items in the new structure
                    if (itemsData.length === 0) {
                        e.preventDefault();
                        alert('Por favor, adicione pelo menos um item à fatura.');
                        return false;
                    }

                    // Validar campos de pagamento se necessário
                    const paymentStatus = document.getElementById('payment_status').value;
                    if (paymentStatus === 'paid' || paymentStatus === 'partial') {
                        const paymentMethod = document.getElementById('payment_method').value;
                        const paymentDate = document.getElementById('payment_date').value;

                        if (!paymentMethod) {
                            e.preventDefault();
                            alert('Por favor, selecione o método de pagamento.');
                            return false;
                        }

                        if (!paymentDate) {
                            e.preventDefault();
                            alert('Por favor, informe a data do pagamento.');
                            return false;
                        }

                        // Validação específica para pagamento parcial
                        if (paymentStatus === 'partial') {
                            const paymentAmount = parseFloat(document.getElementById('payment_amount').value);
                            const totalAmount = parseFloat(document.getElementById('total').value);

                            if (!paymentAmount || paymentAmount <= 0) {
                                e.preventDefault();
                                alert('Por favor, informe o valor do pagamento parcial.');
                                return false;
                            }

                            if (paymentAmount >= totalAmount) {
                                e.preventDefault();
                                alert('O valor do pagamento parcial deve ser menor que o total da fatura.');
                                return false;
                            }
                        }
                    }

                    console.log('Valid items:', itemsData.length);
                    console.log('Form validation passed, submitting...');

                    // Log all form data before submit
                    const formData = new FormData(form);
                    console.log('Form data being submitted:');
                    for (let [key, value] of formData.entries()) {
                        console.log(key, value);
                    }
                });
            }


        }

        // Impedir envio do formulário com Enter e navegar para o próximo campo
        function setupEnterKeyNavigation() {
            const form = document.getElementById('invoice-form');
            if (!form) return;

            // Handler de Enter: evita submit e foca o próximo campo (exceto em TEXTAREA)
            form.addEventListener('keydown', function (e) {
                if (e.key !== 'Enter') return;
                const target = e.target;

                // Permitir Enter em textarea (quebra de linha)
                if (target && target.tagName === 'TEXTAREA') return;

                // Evitar submit implícito do formulário
                e.preventDefault();

                const list = getFocusableElements();
                const idx = list.indexOf(target);
                for (let i = idx + 1; i < list.length; i++) {
                    const el = list[i];
                    if (isVisible(el) && !el.disabled && el.tabIndex !== -1) {
                        el.focus();
                        // Selecionar conteúdo em inputs numéricos/texto para agilizar edição
                        if (el.select && el.tagName === 'INPUT') {
                            try { el.select(); } catch (err) {}
                        }
                        break;
                    }
                }
            }, true);

            // Sugerir rótulo "Próximo" no teclado virtual (iOS/Android) quando possível
            function applyEnterKeyHint(el) {
                if (!el) return;
                if (el.tagName === 'TEXTAREA') {
                    el.setAttribute('enterkeyhint', 'done');
                } else if (el.tagName === 'INPUT' || el.tagName === 'SELECT') {
                    el.setAttribute('enterkeyhint', 'next');
                }
            }

            function isVisible(el) {
                return !!(el.offsetWidth || el.offsetHeight || el.getClientRects().length);
            }

            function getFocusableElements() {
                const nodes = form.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="button"]), select, textarea, button[type="button"]');
                return Array.from(nodes).filter(el => isVisible(el) && !el.disabled);
            }

            // Aplicar dicas de teclado inicialmente
            getFocusableElements().forEach(applyEnterKeyHint);

            // Observar campos adicionados dinamicamente (ex.: itens da fatura)
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((m) => {
                    m.addedNodes.forEach((node) => {
                        if (node.nodeType !== 1) return; // não é elemento
                        if (node.matches && node.matches('input, select, textarea')) applyEnterKeyHint(node);
                        if (node.querySelectorAll) {
                            node.querySelectorAll('input, select, textarea').forEach(applyEnterKeyHint);
                        }
                    });
                });
            });
            observer.observe(form, { childList: true, subtree: true });
        }

            // Remover selects de Tax/Discount da ordem de tabula e7 e3o (pular bot f5es de $/% ao usar "Pr f3ximo")
            function adjustTotalsTabOrder() {
                const skipIds = ['tax_type', 'discount_type'];
                skipIds.forEach(id => {
                    const el = document.getElementById(id);
                    if (el) {
                        el.setAttribute('tabindex', '-1');
                        el.setAttribute('data-skip-tab', 'true');
                    }
                });
            }


        function setupPaymentStatusHandling() {
            const paymentStatus = document.getElementById('payment_status');
            const paymentDetails = document.getElementById('payment_details');
            const paymentAmount = document.getElementById('payment_amount');
            const paymentAmountGroup = document.getElementById('payment_amount_group');
            const paymentMethod = document.getElementById('payment_method');
            const paymentDate = document.getElementById('payment_date');
            const totalField = document.getElementById('total');

            if (paymentStatus && paymentDetails) {
                paymentStatus.addEventListener('change', function() {
                    const status = this.value;

                    if (status === 'paid' || status === 'partial') {
                        paymentDetails.style.display = 'block';
                        paymentDetails.classList.add('show');

                        // Marcar campos como obrigatórios
                        if (paymentMethod) paymentMethod.required = true;
                        if (paymentDate) paymentDate.required = true;

                        // Para status "paid", ocultar campo de valor e usar valor total automaticamente
                        if (status === 'paid') {
                            if (paymentAmountGroup) paymentAmountGroup.style.display = 'none';
                            if (paymentAmount && totalField) {
                                paymentAmount.value = totalField.value;
                                paymentAmount.required = false;
                            }
                        } else if (status === 'partial') {
                            if (paymentAmountGroup) paymentAmountGroup.style.display = 'block';
                            if (paymentAmount) {
                                paymentAmount.value = '';
                                paymentAmount.required = true;
                                paymentAmount.title = 'Informe o valor do pagamento parcial';
                            }
                        }

                        // Adicionar validação em tempo real
                        setupPaymentValidation();
                    } else {
                        paymentDetails.style.display = 'none';
                        paymentDetails.classList.remove('show');

                        // Mostrar campo de valor novamente
                        if (paymentAmountGroup) paymentAmountGroup.style.display = 'block';

                        // Remover obrigatoriedade dos campos
                        if (paymentMethod) paymentMethod.required = false;
                        if (paymentDate) paymentDate.required = false;
                        if (paymentAmount) {
                            paymentAmount.required = false;
                            paymentAmount.value = '';
                            paymentAmount.title = '';
                        }

                        // Limpar classes de validação
                        clearPaymentValidation();
                    }
                });

                // Atualizar valor do pagamento quando o total da fatura mudar (para status "paid")
                if (totalField) {
                    const observer = new MutationObserver(function() {
                        if (paymentStatus.value === 'paid' && paymentAmount) {
                            paymentAmount.value = totalField.value;
                        }
                    });

                    observer.observe(totalField, { attributes: true, attributeFilter: ['value'] });

                    totalField.addEventListener('input', function() {
                        if (paymentStatus.value === 'paid' && paymentAmount) {
                            paymentAmount.value = this.value;
                        }
                    });
                }
            }
        }

        function setupPaymentValidation() {
            const paymentAmount = document.getElementById('payment_amount');
            const paymentMethod = document.getElementById('payment_method');
            const paymentDate = document.getElementById('payment_date');
            const paymentStatus = document.getElementById('payment_status');
            const totalField = document.getElementById('total');

            // Validação do valor do pagamento
            if (paymentAmount && paymentStatus.value === 'partial') {
                paymentAmount.addEventListener('input', function() {
                    const amount = parseFloat(this.value);
                    const total = parseFloat(totalField.value);

                    this.classList.remove('payment-field-required', 'payment-field-valid');

                    if (!amount || amount <= 0) {
                        this.classList.add('payment-field-required');
                        this.title = 'Valor deve ser maior que zero';
                    } else if (amount >= total) {
                        this.classList.add('payment-field-required');
                        this.title = 'Valor deve ser menor que o total da fatura';
                    } else {
                        this.classList.add('payment-field-valid');
                        this.title = 'Valor válido para pagamento parcial';
                    }
                });
            }

            // Validação do método de pagamento
            if (paymentMethod) {
                paymentMethod.addEventListener('change', function() {
                    this.classList.remove('payment-field-required', 'payment-field-valid');

                    if (!this.value) {
                        this.classList.add('payment-field-required');
                    } else {
                        this.classList.add('payment-field-valid');
                    }
                });
            }

            // Validação da data de pagamento
            if (paymentDate) {
                paymentDate.addEventListener('change', function() {
                    this.classList.remove('payment-field-required', 'payment-field-valid');

                    if (!this.value) {
                        this.classList.add('payment-field-required');
                    } else {
                        this.classList.add('payment-field-valid');
                    }
                });
            }
        }

        function clearPaymentValidation() {
            const fields = ['payment_amount', 'payment_method', 'payment_date'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.classList.remove('payment-field-required', 'payment-field-valid');
                }
            });
        }

        let searchTimeout;
        let itemSearchTimeout;
        let isCustomerSelected = <?php echo $customer_id > 0 ? 'true' : 'false'; ?>;
        let selectedItemData = null;
        let itemsData = [];
        let isEditing = false;

        // Cache para otimizar buscas
        const customerSearchCache = new Map();
        const itemSearchCache = new Map();

        function updateAddButtonLabel() {
            const btn = document.querySelector('.add-item-btn-inline');
            if (!btn) return;
            if (isEditing) {
                btn.textContent = 'Salvar';
                btn.title = 'Salvar alterações do item';
            } else {
                btn.textContent = '+';
                btn.title = 'Adicionar item';
            }
        }


        function setupCustomerSearch() {
            const searchInput = document.getElementById('customer_search');
            const resultsDiv = document.getElementById('customer_results');

            if (!searchInput) return;

            // Add touch handlers for mobile dropdown interaction
            resultsDiv.addEventListener('touchstart', function(e) {
                e.stopPropagation();
            }, { passive: true });

            resultsDiv.addEventListener('touchmove', function(e) {
                e.stopPropagation();
            }, { passive: true });

            resultsDiv.addEventListener('touchend', function(e) {
                e.stopPropagation();
            }, { passive: true });

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    resultsDiv.innerHTML = '';
                    resultsDiv.style.display = 'none';
                    return;
                }

                // Verificar cache primeiro
                if (customerSearchCache.has(query)) {
                    displayCustomerResults(customerSearchCache.get(query), query);
                    return;
                }

                searchTimeout = setTimeout(() => {
                    searchCustomers(query);
                }, 150);
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.customer-search-container')) {
                    resultsDiv.style.display = 'none';
                }
            });

            // Hide results when field loses focus (with delay to allow clicks on results)
            searchInput.addEventListener('blur', function() {
                setTimeout(() => {
                    resultsDiv.style.display = 'none';
                }, 200);
            });
        }

        function searchCustomers(query) {
            fetch(`search_customers.php?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(customers => {
                    // Salvar no cache
                    customerSearchCache.set(query, customers);

                    // Aplicar priorização e exibir resultados
                    displayCustomerResults(customers, query);
                })
                .catch(error => {
                    console.error('Erro na busca:', error);
                    const resultsDiv = document.getElementById('customer_results');
                    resultsDiv.innerHTML = '<div class="error">Erro ao buscar clientes</div>';
                    resultsDiv.style.display = 'block';
                });
        }

        function displayCustomerResults(customers, query) {
            const resultsDiv = document.getElementById('customer_results');
            resultsDiv.innerHTML = '';

            if (customers.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="no-results">
                        <p>Nenhum cliente encontrado para "${query}"</p>
                        <button type="button" class="btn btn-primary btn-sm" onclick="showCustomerRegistration('${query}')">
                            New Customer
                        </button>
                    </div>
                `;
            } else {
                // Aplicar priorização nos resultados
                const prioritizedCustomers = prioritizeCustomerResults(customers, query);

                prioritizedCustomers.forEach(customer => {
                    const div = document.createElement('div');
                    div.className = 'search-result-item';
                    div.innerHTML = `
                        <div class="customer-info">
                            <strong>${customer.name}</strong>
                            ${customer.phone ? `<br><small>Tel: ${customer.phone}</small>` : ''}
                            ${customer.email ? `<br><small>Email: ${customer.email}</small>` : ''}
                        </div>
                    `;
                    div.onclick = () => selectCustomer(customer);
                    // Add mousedown event to prevent blur from hiding results before click
                    div.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                        selectCustomer(customer);
                    });
                    resultsDiv.appendChild(div);
                });

                // Add option to create new customer
                const newCustomerDiv = document.createElement('div');
                newCustomerDiv.className = 'search-result-item new-customer';
                newCustomerDiv.innerHTML = `
                    <div class="customer-info">
                        <strong>New Customer "${query}"</strong>
                    </div>
                `;
                newCustomerDiv.onclick = () => showCustomerRegistration(query);
                // Add mousedown event to prevent blur from hiding results before click
                newCustomerDiv.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    showCustomerRegistration(query);
                });
                resultsDiv.appendChild(newCustomerDiv);
            }

            resultsDiv.style.display = 'block';
        }

        function prioritizeCustomerResults(customers, query) {
            const queryLower = query.toLowerCase();

            // Função para encontrar a posição da palavra que contém o termo
            function findWordPosition(text, searchTerm) {
                const words = text.toLowerCase().split(/\s+/);
                for (let i = 0; i < words.length; i++) {
                    if (words[i].startsWith(searchTerm)) {
                        return i + 1; // Posição da palavra (1-based)
                    }
                }
                // Se não encontrou palavra que começa com o termo, verificar se contém
                if (text.toLowerCase().includes(searchTerm)) {
                    return 999; // Prioridade baixa para termos que apenas contêm
                }
                return 1000; // Não encontrado
            }

            // Criar array com prioridades
            const customersWithPriority = customers.map(customer => ({
                ...customer,
                wordPosition: findWordPosition(customer.name, queryLower)
            }));

            // Ordenar por posição da palavra, depois alfabeticamente
            customersWithPriority.sort((a, b) => {
                if (a.wordPosition !== b.wordPosition) {
                    return a.wordPosition - b.wordPosition;
                }
                return a.name.localeCompare(b.name);
            });

            // Retornar apenas os clientes (sem a propriedade wordPosition)
            return customersWithPriority.map(({wordPosition, ...customer}) => customer);
        }

        function selectCustomer(customer) {
            document.getElementById('customer_id').value = customer.id;
            document.getElementById('customer_search').value = '';
            document.getElementById('customer_results').style.display = 'none';

            const selectedDiv = document.getElementById('selected_customer');
            selectedDiv.innerHTML = `
                <div class="customer-info">
                    <span class="customer-name">${customer.name}</span>
                    ${customer.phone ? `<span class="customer-phone">Tel: ${customer.phone}</span>` : ''}
                    ${customer.email ? `<span class="customer-email">Email: ${customer.email}</span>` : ''}
                </div>
                <button type="button" class="btn-clear-customer" onclick="clearCustomer()">×</button>
            `;
            selectedDiv.style.display = 'block';

            isCustomerSelected = true;

            // Auto-focar pr ximo campo de edi e7 e3o ap 13s selecionar o cliente
            setTimeout(() => {
                const candidates = ['service_address', 'item_search', 'tax_rate', 'discount_amount'];
                for (const id of candidates) {
                    const el = document.getElementById(id);
                    if (el && !el.disabled && el.offsetParent !== null) {
                        try { el.focus(); if (el.select) el.select(); } catch (e) {}
                        break;
                    }
                }
            }, 80);
        }

        function clearCustomer() {
            document.getElementById('customer_id').value = '';
            document.getElementById('customer_search').value = '';
            document.getElementById('selected_customer').style.display = 'none';
            isCustomerSelected = false;
        }

        function showCustomerRegistration(customerName = '') {
            openNewCustomerModal(customerName);
        }

        function openNewCustomerModal(customerName = '') {
            const modal = document.getElementById('newCustomerModal');
            const nameField = document.getElementById('customer_name');

            document.getElementById('customer_results').style.display = 'none';

            if (customerName && nameField) {
                nameField.value = customerName;
            }

            modal.style.display = 'block';

            setTimeout(() => {
                if (customerName && nameField) {
                    const emailField = document.getElementById('customer_email');
                    if (emailField) emailField.focus();
                } else if (nameField) {
                    nameField.focus();
                }
            }, 100);
        }

        function closeModal() {
            const modal = document.getElementById('newCustomerModal');
            const form = document.getElementById('newCustomerForm');

            modal.style.display = 'none';
            form.reset();
        }

        function cancelCustomerRegistration() {
            closeModal();
        }

        function saveCustomer() {
            const name = document.getElementById('customer_name').value.trim();

            if (!name) {
                alert('Customer name is required');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('action', 'create_customer');
            formData.append('ajax', '1'); // Flag to indicate AJAX request
            formData.append('customer_name', name);
            formData.append('customer_email', document.getElementById('customer_email').value);
            formData.append('customer_phone', document.getElementById('customer_phone').value);
            formData.append('customer_address', document.getElementById('customer_address').value);
            formData.append('customer_city', document.getElementById('customer_city').value);
            formData.append('customer_state', document.getElementById('customer_state').value);
            formData.append('customer_zip', document.getElementById('customer_zip').value);
            formData.append('customer_notes', document.getElementById('customer_notes').value);

            // Submit form to create customer
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Fechar o modal
                    closeModal();

                    // Select the newly created customer
                    selectCustomer(data.customer);

                    // Show success message
                    alert('Customer created successfully!');
                } else {
                    alert(data.error || 'Erro ao criar cliente');
                }
            })
            .catch(error => {
                console.error('Erro ao criar cliente:', error);
                alert('Erro ao criar cliente');
            });
        }

        // Item search and management functions
        function setupItemSearch() {
            const searchInput = document.getElementById('item_search');
            const resultsDiv = document.getElementById('item_results');

            if (!searchInput) {
                return;
            }

            // Add touch handlers for mobile dropdown interaction
            resultsDiv.addEventListener('touchstart', function(e) {
                e.stopPropagation();
            }, { passive: true });

            resultsDiv.addEventListener('touchmove', function(e) {
                e.stopPropagation();
            }, { passive: true });

            resultsDiv.addEventListener('touchend', function(e) {
                e.stopPropagation();
            }, { passive: true });

            searchInput.addEventListener('input', function() {
                clearTimeout(itemSearchTimeout);
                const query = this.value.trim();

                if (query.length < 2) {
                    resultsDiv.innerHTML = '';
                    resultsDiv.style.display = 'none';
                    return;
                }

                // Verificar cache primeiro
                if (itemSearchCache.has(query)) {
                    displayItemResults(itemSearchCache.get(query), query);
                    return;
                }

                itemSearchTimeout = setTimeout(() => {
                    searchItems(query);
                }, 150);
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.item-search-container')) {
                    resultsDiv.style.display = 'none';
                }

                // Hide item actions when clicking outside the table

                if (!e.target.closest('.items-table') && !e.target.closest('.item-actions')) {
                    hideAllItemActions();
                }
            });

            // Hide results when field loses focus (with delay to allow clicks on results)
            searchInput.addEventListener('blur', function() {
                setTimeout(() => {
                    resultsDiv.style.display = 'none';
                }, 200);
            });

            // Permitir rolagem do dropdown com o teclado (impede a página de rolar)



        }

        function hideAllItemActions() {
            const allActionRows = document.querySelectorAll('.item-actions');
            const allItemRows = document.querySelectorAll('.item-row');

            allActionRows.forEach(actions => {
                actions.classList.remove('show');
            });

            allItemRows.forEach(row => {
                row.classList.remove('selected');
            });
        }

        function searchItems(query) {
            // Use AJAX to search items dynamically
            fetch(`search_items_api_mysqli.php?search=${encodeURIComponent(query)}`)
                .then(response => response.text())
                .then(text => {
                    try {
                        const items = JSON.parse(text);

                        if (items.error) {
                            const resultsDiv = document.getElementById('item_results');
                            resultsDiv.innerHTML = `<div class="error">Error: ${items.error}</div>`;
                            resultsDiv.style.display = 'block';
                            return;
                        }

                        // Salvar no cache
                        itemSearchCache.set(query, items);

                        // Aplicar priorização e exibir resultados
                        displayItemResults(items, query);

                    } catch (parseError) {
                        const resultsDiv = document.getElementById('item_results');
                        resultsDiv.innerHTML = '<div class="error">Response is not valid JSON.</div>';
                        resultsDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    const resultsDiv = document.getElementById('item_results');
                    resultsDiv.innerHTML = '<div class="error">Network error. Please try again.</div>';
                    resultsDiv.style.display = 'block';
                });
        }

        function displayItemResults(items, query) {
            const resultsDiv = document.getElementById('item_results');
            resultsDiv.innerHTML = '';

            if (items.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="no-results">
                        <p>Product "${query}" not found</p>
                        <button type="button" class="btn btn-primary btn-sm" onclick="openNewItemModal('${query}')">
                            Create "${query}"
                        </button>
                    </div>
                `;
            } else {
                // Aplicar priorização nos resultados
                const prioritizedItems = prioritizeItemResults(items, query);

                prioritizedItems.forEach(item => {
                    const div = document.createElement('div');
                    div.className = 'search-result-item';
                    div.innerHTML = `
                        <div class="item-info">
                            <strong>${item.name}</strong>
                            ${item.sku ? `<br><small>SKU: ${item.sku}</small>` : ''}
                            <br><small>Price: $${parseFloat(item.price).toFixed(2)}</small>
                            ${item.quantity !== undefined ? `<br><small>Stock: ${item.quantity}</small>` : ''}
                        </div>
                    `;
                    div.onclick = () => selectItem(item);
                    // Add mousedown event to prevent blur from hiding results before click
                    div.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                        selectItem(item);
                    });
                    resultsDiv.appendChild(div);
                });

                // Add option to create new item
                const newItemDiv = document.createElement('div');
                newItemDiv.className = 'search-result-item new-item';
                newItemDiv.innerHTML = `
                    <div class="item-info">
                        <strong style="color: #007bff;">Create "${query}"</strong>
                    </div>
                `;
                newItemDiv.onclick = () => openNewItemModal(query);
                // Add mousedown event to prevent blur from hiding results before click
                newItemDiv.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    openNewItemModal(query);
                });
                resultsDiv.appendChild(newItemDiv);
            }

            resultsDiv.style.display = 'block';
        }

        function prioritizeItemResults(items, query) {
            const queryLower = query.toLowerCase();
            const escapedQuery = queryLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

            // Função para encontrar a posição da palavra que contém o termo
            function findWordPosition(text, searchTerm) {
                const words = text.toLowerCase().split(/\s+/);
                for (let i = 0; i < words.length; i++) {
                    if (words[i].startsWith(searchTerm)) {
                        return i + 1; // Posição da palavra (1-based)
                    }
                }
                // Se não encontrou palavra que começa com o termo, verificar se contém
                if (text.toLowerCase().includes(searchTerm)) {
                    return 999; // Prioridade baixa para termos que apenas contêm
                }
                return 1000; // Não encontrado
            }

            // Criar array com prioridades
            const itemsWithPriority = items.map(item => ({
                ...item,
                wordPosition: findWordPosition(item.name, queryLower)
            }));

            // Ordenar por posição da palavra, depois alfabeticamente
            itemsWithPriority.sort((a, b) => {
                if (a.wordPosition !== b.wordPosition) {
                    return a.wordPosition - b.wordPosition;
                }
                return a.name.localeCompare(b.name);
            });

            // Retornar apenas os itens (sem a propriedade wordPosition)
            return itemsWithPriority.map(({wordPosition, ...item}) => item);
        }

        function selectItem(item, skipAutoFocus = false) {
            selectedItemData = item;
            document.getElementById('item_search').value = '';
            document.getElementById('item_results').style.display = 'none';


            const selectedDiv = document.getElementById('selected_item');
            selectedDiv.querySelector('.item-name').textContent = item.name;

            // Ao selecionar pela busca, n e3o  e9 edi e7 e3o
            isEditing = false;
            updateAddButtonLabel();

            // Ativa o layout com chip + QTD + PRE c7O e bot e3o na linha de baixo
            const section = document.getElementById('add_item_section');
            if (section) section.classList.add('has-selection');

            // Auto-fill price
            document.getElementById('item_price').value = parseFloat(item.price).toFixed(2);

            // Atualizar estado do botão Custom
            updateCustomButtonState();

            // Focar no campo de quantidade após selecionar item (apenas se não for skipAutoFocus)
            if (!skipAutoFocus) {
                setTimeout(() => {
                    const qtyField = document.getElementById('item_quantity');
                    if (qtyField) {
                        qtyField.focus();
                        qtyField.select();
                    }
                }, 100);
            }
        }

        function clearSelectedItem() {
            selectedItemData = null;
            document.getElementById('item_price').value = '';

            // Reset bot e3o e estado de edi e7 e3o
            isEditing = false;
            updateAddButtonLabel();

            // Volta ao estado inicial (apenas busca vis edvel)
            const section = document.getElementById('add_item_section');
            if (section) section.classList.remove('has-selection');

            // Atualizar estado do botão Custom
            updateCustomButtonState();
        }

        // Funções para editar o nome do item no chip
        function editItemName() {
            const nameSpan = document.querySelector('.selected-item .item-name');
            const nameInput = document.querySelector('.selected-item .item-name-input');

            if (!nameSpan || !nameInput) return;

            // Esconder span e mostrar input
            nameSpan.style.display = 'none';
            nameInput.style.display = 'block';
            nameInput.value = nameSpan.textContent;
            nameInput.focus();
            nameInput.select();
        }

        function saveItemName() {
            const nameSpan = document.querySelector('.selected-item .item-name');
            const nameInput = document.querySelector('.selected-item .item-name-input');

            if (!nameSpan || !nameInput) return;

            const newName = nameInput.value.trim();
            if (newName && selectedItemData) {
                // Atualizar o nome customizado no objeto selectedItemData
                selectedItemData.customName = newName;
                nameSpan.textContent = newName;
            }

            // Voltar ao estado normal
            nameInput.style.display = 'none';
            nameSpan.style.display = 'block';
        }

        function handleItemNameKeydown(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                saveItemName();
                // Focar no campo de quantidade após salvar o nome
                setTimeout(() => {
                    const qtyField = document.getElementById('item_quantity');
                    if (qtyField) {
                        qtyField.focus();
                        qtyField.select();
                    }
                }, 50);
            } else if (event.key === 'Escape') {
                event.preventDefault();
                // Cancelar edição - voltar ao nome original
                const nameSpan = document.querySelector('.selected-item .item-name');
                const nameInput = document.querySelector('.selected-item .item-name-input');

                if (nameSpan && nameInput) {
                    nameInput.style.display = 'none';
                    nameSpan.style.display = 'block';
                }
            }
        }

        // Função para gerenciar navegação no campo de quantidade
        function handleQuantityKeydown(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                // Focar no campo de preço
                const priceField = document.getElementById('item_price');
                if (priceField) {
                    priceField.focus();
                    priceField.select();
                }
            }
        }

        // Função para gerenciar navegação no campo de preço
        function handlePriceKeydown(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                // Adicionar item à tabela
                addItemToTable();
            }
        }

        // Função para gerenciar navegação no campo de busca de item
        function handleItemSearchKeydown(event) {
            const resultsDiv = document.getElementById('item_results');

            if (event.key === 'Enter') {
                event.preventDefault();

                // Fechar dropdown de resultados
                if (resultsDiv) {
                    resultsDiv.style.display = 'none';
                }

                // Se há um item selecionado, ir para quantidade
                if (selectedItemData) {
                    const qtyField = document.getElementById('item_quantity');
                    if (qtyField) {
                        qtyField.focus();
                        qtyField.select();
                    }
                }
                // Se não há item selecionado, manter foco no campo de busca
            } else if (event.key === 'Escape') {
                event.preventDefault();
                // Fechar dropdown de resultados
                if (resultsDiv) {
                    resultsDiv.style.display = 'none';
                }
                // Manter foco no campo atual
                event.target.blur();
                event.target.focus();
            }
        }

        // Função para selecionar item "Ac Services" customizado
        function selectCustomItem() {
            // Verificar se já há um item selecionado
            if (selectedItemData) {
                return; // Não fazer nada se já há item selecionado
            }

            // Buscar o item "Ac Services" no banco de dados
            fetch('search_items_api_mysqli.php?search=Ac Services')
                .then(response => response.text())
                .then(text => {
                    try {
                        const data = JSON.parse(text);
                        if (data && data.length > 0) {
                            // Encontrar exatamente "Ac Services"
                            const acServicesItem = data.find(item =>
                                item.name.toLowerCase() === 'ac services'
                            );

                            if (acServicesItem) {
                                // Selecionar o item como se fosse clicado no dropdown (sem foco automático)
                                selectItem(acServicesItem, true);

                                // Após selecionar, ir direto para edição do nome
                                setTimeout(() => {
                                    editItemName();
                                }, 150);
                            } else {
                                console.warn('Item "Ac Services" não encontrado no banco de dados');
                            }
                        } else {
                            console.warn('Nenhum item encontrado para "Ac Services"');
                        }
                    } catch (e) {
                        console.error('Erro ao fazer parse da resposta JSON:', e);
                        console.log('Resposta recebida:', text);
                    }
                })
                .catch(error => {
                    console.error('Erro ao buscar item Ac Services:', error);
                });
        }

        // Função para atualizar estado do botão Custom
        function updateCustomButtonState() {
            const customBtn = document.getElementById('custom_item_btn');
            if (customBtn) {
                customBtn.disabled = selectedItemData !== null;
            }
        }

        // Função para gerenciar navegação no campo de busca de cliente
        function handleCustomerSearchKeydown(event) {
            const resultsDiv = document.getElementById('customer_results');

            if (event.key === 'Enter') {
                event.preventDefault();
                // Fechar dropdown de resultados
                if (resultsDiv) {
                    resultsDiv.style.display = 'none';
                }

                // Ir para o próximo campo (service_address)
                const serviceAddressField = document.getElementById('service_address');
                if (serviceAddressField) {
                    serviceAddressField.focus();
                }
            } else if (event.key === 'Escape') {
                event.preventDefault();
                // Fechar dropdown de resultados
                if (resultsDiv) {
                    resultsDiv.style.display = 'none';
                }
                // Manter foco no campo atual
                event.target.blur();
                event.target.focus();
            }
        }

        function setupItemCalculation() {
            // No longer needed since we removed the subtotal display field
        }

        // Funções do modal de novo item
        function openNewItemModal(itemName = '') {
            const modal = document.getElementById('newItemModal');
            const nameInput = document.getElementById('item_name');

            // Limpar formulário
            document.getElementById('newItemForm').reset();

            // Preencher nome se fornecido
            if (itemName) {
                nameInput.value = itemName;
                // Focar no próximo campo (SKU)
                setTimeout(() => {
                    document.getElementById('item_sku').focus();
                }, 100);
            } else {
                // Focar no campo nome
                setTimeout(() => {
                    nameInput.focus();
                }, 100);
            }

            // Esconder resultados da busca
            document.getElementById('item_results').style.display = 'none';

            // Mostrar modal
            modal.style.display = 'block';
        }

        function closeNewItemModal() {
            const modal = document.getElementById('newItemModal');
            const form = document.getElementById('newItemForm');

            modal.style.display = 'none';
            form.reset();
        }

        function saveItem() {
            const name = document.getElementById('item_name').value.trim();
            const price = document.getElementById('item_price_input').value.trim();

            if (!name) {
                alert('Item name is required');
                return;
            }

            if (!price || parseFloat(price) < 0) {
                alert('Valid price is required');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('ajax', '1');
            formData.append('item_name', name);
            formData.append('item_sku', document.getElementById('item_sku').value);
            formData.append('item_description', document.getElementById('item_description').value);
            formData.append('item_price', price);
            formData.append('item_cost', document.getElementById('item_cost').value || '0');
            formData.append('item_category', document.getElementById('item_category').value);

            // Send AJAX request
            fetch('create_item_api_mysqli.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        // Fechar o modal
                        closeNewItemModal();

                        // Selecionar o item recém-criado
                        selectItem(data.item);

                        // Mostrar mensagem de sucesso
                        alert('Item created successfully!');
                    } else {
                        alert('Error: ' + (data.error || 'Failed to create item'));
                    }
                } catch (parseError) {
                    alert('Response is not valid JSON.');
                }
            })
            .catch(error => {
                alert('Network error. Please try again.');
            });
        }

        function redirectToCreateProduct(productName) {
            // Salvar dados do formulário atual no localStorage
            saveFormDataToStorage();

            // Redirecionar para página de cadastro de produto com o nome pré-preenchido
            const encodedName = encodeURIComponent(productName);
            window.location.href = `inventory_create.php?name=${encodedName}&return_to=invoice_create`;
        }

        function saveFormDataToStorage() {
            const formData = {
                customer_id: document.getElementById('customer_id').value,
                customer_search: document.getElementById('customer_search').value,
                invoice_date: document.getElementById('invoice_date').value,
                due_date: document.getElementById('due_date').value,
                service_address: document.getElementById('service_address').value,
                tax_rate: document.getElementById('tax_rate').value,
                discount_amount: document.getElementById('discount_amount').value,
                items: itemsData,
                timestamp: Date.now()
            };

            localStorage.setItem('invoice_form_backup', JSON.stringify(formData));
            console.log('Form data saved to localStorage');
        }

        function loadFormDataFromStorage() {
            const savedData = localStorage.getItem('invoice_form_backup');
            if (savedData) {
                try {
                    const formData = JSON.parse(savedData);

                    // Verificar se os dados não são muito antigos (1 hora)
                    if (Date.now() - formData.timestamp < 3600000) {
                        // Restaurar dados do formulário
                        if (formData.customer_id) document.getElementById('customer_id').value = formData.customer_id;
                        if (formData.customer_search) document.getElementById('customer_search').value = formData.customer_search;
                        if (formData.invoice_date) document.getElementById('invoice_date').value = formData.invoice_date;
                        if (formData.due_date) document.getElementById('due_date').value = formData.due_date;
                        if (formData.service_address) document.getElementById('service_address').value = formData.service_address;
                        if (formData.tax_rate) document.getElementById('tax_rate').value = formData.tax_rate;
                        if (formData.discount_amount) document.getElementById('discount_amount').value = formData.discount_amount;

                        // Restaurar itens
                        if (formData.items && formData.items.length > 0) {
                            itemsData = formData.items;
                            updateItemsTable();
                            updateHiddenInputs();
                            calculateTotals();
                        }

                        // Verificar se voltou do cadastro de produto
                        const urlParams = new URLSearchParams(window.location.search);
                        if (urlParams.get('product_created') === 'true') {
                            const newProductId = urlParams.get('new_product_id');
                            if (newProductId) {
                                // Buscar e inserir automaticamente o produto recém-criado
                                addNewlyCreatedProduct(newProductId);
                            } else {
                                showSuccessMessage('Produto cadastrado com sucesso! Você pode agora selecioná-lo na lista.');
                            }
                        }
                    }

                    // Limpar dados salvos
                    localStorage.removeItem('invoice_form_backup');
                } catch (error) {
                    localStorage.removeItem('invoice_form_backup');
                }
            }
        }

        function addNewlyCreatedProduct(productId) {
            // Fetch product data via AJAX
            fetch(`get_product.php?id=${productId}&t=${Date.now()}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(product => {
                    if (product && product.id) {
                        addProductToInvoice(product);
                    } else {
                        showSuccessMessage('Produto cadastrado com sucesso! Recarregando página para atualizar inventário...');
                        setTimeout(() => {
                            const url = new URL(window.location);
                            url.searchParams.delete('product_created');
                            url.searchParams.delete('new_product_id');
                            window.location.href = url.toString();
                        }, 2000);
                    }
                })
                .catch(error => {
                    showSuccessMessage('Produto cadastrado com sucesso! Recarregando página para atualizar inventário...');
                    setTimeout(() => {
                        const url = new URL(window.location);
                        url.searchParams.delete('product_created');
                        url.searchParams.delete('new_product_id');
                        window.location.href = url.toString();
                    }, 2000);
                });
        }

        function addProductToInvoice(product) {
            // Adicionar automaticamente o produto à tabela com quantidade 1
            const item = {
                id: product.id,
                name: product.name,
                description: product.name,
                quantity: 1,
                price: parseFloat(product.price),
                subtotal: parseFloat(product.price)
            };

            itemsData.push(item);
            updateItemsTable();
            updateHiddenInputs();
            calculateTotals();

            showSuccessMessage(`✅ Produto "${product.name}" foi cadastrado e adicionado automaticamente à fatura!`);

            // Limpar URL parameters
            const url = new URL(window.location);
            url.searchParams.delete('product_created');
            url.searchParams.delete('new_product_id');
            window.history.replaceState({}, document.title, url);

            console.log('Produto adicionado com sucesso. Total de itens:', itemsData.length);
        }

        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'success-message';
            messageDiv.innerHTML = `
                <div class="alert alert-success" style="
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                    padding: 12px 16px;
                    border-radius: 6px;
                    margin-bottom: 20px;
                    position: relative;
                ">
                    <strong>✅ ${message}</strong>
                    <button type="button" style="
                        position: absolute;
                        top: 8px;
                        right: 12px;
                        background: none;
                        border: none;
                        font-size: 18px;
                        cursor: pointer;
                        color: #155724;
                    " onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
            `;

            const form = document.querySelector('form');
            form.insertBefore(messageDiv, form.firstChild);

            // Auto-remover após 5 segundos
            setTimeout(() => {
                if (messageDiv.parentElement) {
                    messageDiv.remove();
                }
            }, 5000);
        }

        function addItemToTable() {
            const quantity = parseFloat(document.getElementById('item_quantity').value) || 0;
            const price = parseFloat(document.getElementById('item_price').value) || 0;

            if (!selectedItemData && !document.getElementById('item_search').value.trim()) {
                alert('Por favor, selecione ou digite um item.');
                return;
            }

            if (quantity <= 0) {
                alert('Por favor, informe uma quantidade válida.');
                return;
            }

            if (price <= 0) {
                alert('Por favor, informe um preço válido.');
                return;
            }

            const subtotal = quantity * price;
            // Usar nome customizado se disponível, senão usar nome original
            const itemName = selectedItemData ?
                (selectedItemData.customName || selectedItemData.name) :
                document.getElementById('item_search').value.trim();

            const item = {
                id: selectedItemData ? selectedItemData.id : 0,
                name: itemName,
                originalName: selectedItemData ? selectedItemData.name : itemName, // Salvar nome original
                description: itemName,
                quantity: quantity,
                price: price,
                subtotal: subtotal
            };

            itemsData.push(item);
            updateItemsTable();
            updateHiddenInputs();
            calculateTotals();
            clearItemForm();
        }

        function updateItemsTable() {
            const tbody = document.getElementById('items-table-body');

            if (itemsData.length === 0) {
                tbody.innerHTML = '';
                return;
            }

            tbody.innerHTML = itemsData.map((item, index) => {
                const quantity = parseFloat(item.quantity);
                const price = parseFloat(item.price);
                const subtotal = parseFloat(item.subtotal);

                // Format quantity (no decimals for whole numbers, up to 3 for fractions)
                const formattedQty = quantity % 1 === 0 ? quantity.toString() : quantity.toFixed(3).replace(/\.?0+$/, '');

                return `
                    <tr data-index="${index}" class="swipe-row">
                        <td colspan="4" class="swipe-container">
                            <div class="swipe-content">
                                <div class="swipe-item-row" data-row-index="${index}">
                                    <div class="item-cell editable-cell" data-field="name" data-index="${index}" title="Clique para editar o nome">
                                        <span class="cell-value">${item.name}</span>
                                        <input type="text" class="cell-input" style="display: none;" value="${item.name}" onblur="saveCellValue(${index}, 'name', this)" onkeydown="handleCellKeydown(event, ${index}, 'name', this)">
                                    </div>
                                    <div class="item-cell editable-cell" data-field="quantity" data-index="${index}" title="Clique para editar a quantidade">
                                        <span class="cell-value">${formattedQty}</span>
                                        <input type="number" class="cell-input" style="display: none;" value="${quantity}" step="0.001" min="0.001" onblur="saveCellValue(${index}, 'quantity', this)" onkeydown="handleCellKeydown(event, ${index}, 'quantity', this)">
                                    </div>
                                    <div class="item-cell editable-cell" data-field="price" data-index="${index}" title="Clique para editar o preço">
                                        <span class="cell-value">$${price.toFixed(2)}</span>
                                        <input type="number" class="cell-input" style="display: none;" value="${price}" step="0.01" min="0" onblur="saveCellValue(${index}, 'price', this)" onkeydown="handleCellKeydown(event, ${index}, 'price', this)">
                                    </div>
                                    <div class="item-cell total-cell" data-index="${index}" title="Deslize para excluir ou clique para editar">
                                        $${subtotal.toFixed(2)}
                                    </div>
                                </div>
                            </div>
                            <div class="swipe-delete-btn" onclick="deleteItem(${index})">
                                <i class="fas fa-trash"></i>
                                <span>Excluir</span>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function updateHiddenInputs() {
            const container = document.getElementById('hidden-items-inputs');
            container.innerHTML = '';

            itemsData.forEach((item, index) => {
                container.innerHTML += `
                    <input type="hidden" name="item_id[]" value="${item.id}">
                    <input type="hidden" name="item_description[]" value="${item.description}">
                    <input type="hidden" name="item_quantity[]" value="${item.quantity}">
                    <input type="hidden" name="item_price[]" value="${item.price}">
                    <input type="hidden" name="item_subtotal[]" value="${item.subtotal}">
                `;
            });
        }

        function clearItemForm() {
            document.getElementById('item_search').value = '';
            document.getElementById('item_quantity').value = '1';
            document.getElementById('item_price').value = '';
            clearSelectedItem();

            // Retornar foco para o campo de busca de item
            setTimeout(() => {
                const itemSearchField = document.getElementById('item_search');
                if (itemSearchField) {
                    itemSearchField.focus();
                    // Abrir teclado em dispositivos móveis
                    if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                        itemSearchField.click();
                    }
                }
            }, 100);
        }

        function editItem(index) {
            const item = itemsData[index];

            // Fill form with item data
            document.getElementById('item_quantity').value = item.quantity;
            document.getElementById('item_price').value = item.price;

            // Always show chip with the item's name (inventory or manual)
            // Preservar nome customizado se existir
            selectedItemData = {
                id: item.id || 0,
                name: item.originalName || item.name,
                price: item.price,
                customName: item.name !== (item.originalName || item.name) ? item.name : undefined
            };
            const selectedDiv = document.getElementById('selected_item');
            selectedDiv.querySelector('.item-name').textContent = item.name;


            // Entrar em modo edição: trocar "+" por "Salvar"
            isEditing = true;
            updateAddButtonLabel();

            // Activate selection layout so QTD/PREÇO and Add button appear
            const section = document.getElementById('add_item_section');
            if (section) {
                section.classList.add('has-selection');
                try { section.scrollIntoView({ behavior: 'smooth', block: 'center' }); } catch (e) {}
            }

            // Remove item immediately (no confirm). It will be re-added when clicking "+"
            itemsData.splice(index, 1);
            updateItemsTable();
            updateHiddenInputs();
            calculateTotals();
        }

        function deleteItem(index) {
            if (confirm('Tem certeza que deseja excluir este item?')) {
                itemsData.splice(index, 1);
                updateItemsTable();
                updateHiddenInputs();
                calculateTotals();

                console.log('Item deleted successfully, items remaining:', itemsData.length);
            }
        }

        let currentItemIndex = -1;

        function showItemActions(index) {
            // Redireciona direto para edi e7 e3o inline (sem modal)
            editItem(index);
        }

        // Funções para edição inline na tabela
        function editCellInline(index, field, cellElement) {
            // Fechar todos os swipes abertos
            closeAllSwipes();

            // Prevenir edição se já existe uma célula sendo editada
            const existingInput = document.querySelector('.cell-input[style*="block"]');
            if (existingInput) {
                existingInput.blur(); // Salvar a edição atual
                return;
            }

            const span = cellElement.querySelector('.cell-value');
            const input = cellElement.querySelector('.cell-input');

            if (!span || !input) return;

            // Esconder o span e mostrar o input
            span.style.display = 'none';
            input.style.display = 'block';

            // Focar no input e selecionar o texto
            input.focus();
            if (field === 'name') {
                input.select();
            } else {
                // Para campos numéricos, posicionar cursor no final
                setTimeout(() => {
                    input.setSelectionRange(input.value.length, input.value.length);
                }, 10);
            }
        }

        function saveCellValue(index, field, inputElement) {
            const cellElement = inputElement.parentElement;
            const span = cellElement.querySelector('.cell-value');
            const input = cellElement.querySelector('.cell-input');

            if (!span || !input) return;

            const newValue = input.value.trim();
            const item = itemsData[index];

            // Validar o valor baseado no campo
            let isValid = true;
            let processedValue = newValue;

            if (field === 'name') {
                if (newValue === '') {
                    alert('O nome do item não pode estar vazio.');
                    input.focus();
                    return;
                }
                processedValue = newValue;
            } else if (field === 'quantity') {
                const numValue = parseFloat(newValue);
                if (isNaN(numValue) || numValue <= 0) {
                    alert('A quantidade deve ser um número maior que zero.');
                    input.focus();
                    return;
                }
                processedValue = numValue;
            } else if (field === 'price') {
                const numValue = parseFloat(newValue);
                if (isNaN(numValue) || numValue < 0) {
                    alert('O preço deve ser um número maior ou igual a zero.');
                    input.focus();
                    return;
                }
                processedValue = numValue;
            }

            // Atualizar o item nos dados
            if (field === 'name') {
                item.name = processedValue;
                item.description = processedValue; // Manter sincronizado
            } else if (field === 'quantity') {
                item.quantity = processedValue;
                item.subtotal = processedValue * item.price;
            } else if (field === 'price') {
                item.price = processedValue;
                item.subtotal = item.quantity * processedValue;
            }

            // Atualizar a tabela e recalcular totais
            updateItemsTable();
            updateHiddenInputs();
            calculateTotals();
        }

        function handleCellKeydown(event, index, field, inputElement) {
            if (event.key === 'Enter') {
                event.preventDefault();
                inputElement.blur(); // Isso vai chamar saveCellValue
            } else if (event.key === 'Escape') {
                event.preventDefault();
                cancelCellEdit(inputElement);
            }
        }

        function cancelCellEdit(inputElement) {
            const cellElement = inputElement.parentElement;
            const span = cellElement.querySelector('.cell-value');
            const input = cellElement.querySelector('.cell-input');

            if (!span || !input) return;

            // Restaurar valor original
            const originalValue = span.textContent;
            input.value = originalValue;

            // Mostrar span e esconder input
            span.style.display = 'block';
            input.style.display = 'none';
        }

        // Variáveis para controle do swipe
        let swipeStartX = 0;
        let swipeStartY = 0;
        let swipeCurrentX = 0;
        let swipeCurrentY = 0;
        let isSwipeActive = false;
        let swipeIndex = -1;
        let swipeThreshold = 50; // Pixels mínimos para ativar o swipe
        let swipeMaxDistance = 80; // Distância máxima do swipe

        // Variáveis para controle do delay de clique
        let clickDelayTimer = null;
        let pendingClickData = null;
        let clickDelay = 100; // 100ms de delay para detectar intenção
        let hasMovedDuringClick = false;
        let initialInteraction = false;

        function handleCellInteraction(event, cell) {
            // Prevenir se estiver editando uma célula
            if (event.target.closest('.cell-input')) {
                return;
            }

            // Fechar qualquer swipe aberto
            closeAllSwipes();

            const clientX = event.touches ? event.touches[0].clientX : event.clientX;
            const clientY = event.touches ? event.touches[0].clientY : event.clientY;
            const index = parseInt(cell.dataset.index);
            const field = cell.dataset.field;

            // Armazenar dados para possível clique
            swipeStartX = clientX;
            swipeStartY = clientY;
            swipeCurrentX = clientX;
            swipeCurrentY = clientY;
            hasMovedDuringClick = false;

            // Se é uma célula editável, preparar para clique com delay
            if (field) {
                pendingClickData = {
                    index: index,
                    field: field,
                    cell: cell
                };

                // Timer para executar clique se não houver movimento
                clickDelayTimer = setTimeout(() => {
                    if (pendingClickData && !hasMovedDuringClick) {
                        executePendingClick();
                    }
                }, clickDelay);
            }

            // Prevenir seleção de texto
            event.preventDefault();
        }

        function cancelPendingClick() {
            if (clickDelayTimer) {
                clearTimeout(clickDelayTimer);
                clickDelayTimer = null;
            }
            pendingClickData = null;
            hasMovedDuringClick = false;
        }

        function executePendingClick() {
            if (pendingClickData) {
                const { index, field, cell } = pendingClickData;
                editCellInline(index, field, cell);
                cancelPendingClick();
            }
        }

        function startSwipeFromPending() {
            if (pendingClickData) {
                swipeIndex = pendingClickData.index;
                isSwipeActive = true;

                // Fechar qualquer swipe aberto
                closeAllSwipes();

                cancelPendingClick();
            }
        }

        function startSwipe(event, index) {
            // Prevenir swipe se estiver editando uma célula
            if (event.target.closest('.cell-input')) {
                return;
            }

            // Fechar qualquer swipe aberto
            closeAllSwipes();

            const clientX = event.touches ? event.touches[0].clientX : event.clientX;
            const clientY = event.touches ? event.touches[0].clientY : event.clientY;

            swipeStartX = clientX;
            swipeStartY = clientY;
            swipeCurrentX = clientX;
            swipeCurrentY = clientY;
            isSwipeActive = true;
            swipeIndex = index;

            // Prevenir seleção de texto
            event.preventDefault();
        }

        function handleSwipe(event, index) {
            if (!isSwipeActive) return;

            const clientX = event.touches ? event.touches[0].clientX : event.clientX;
            const clientY = event.touches ? event.touches[0].clientY : event.clientY;

            swipeCurrentX = clientX;
            swipeCurrentY = clientY;

            const deltaX = swipeStartX - swipeCurrentX;
            const deltaY = Math.abs(swipeStartY - swipeCurrentY);

            // Se o movimento vertical for muito grande, cancelar o swipe
            if (deltaY > 30) {
                endSwipe(event, swipeIndex);
                return;
            }

            // Só permitir swipe para a esquerda
            if (deltaX > 0) {
                const swipeDistance = Math.min(deltaX, swipeMaxDistance);
                const swipeContent = document.querySelector(`tr[data-index="${swipeIndex}"] .swipe-content`);

                if (swipeContent) {
                    swipeContent.style.transform = `translateX(-${swipeDistance}px)`;
                    swipeContent.style.transition = 'none';
                }
            }

            event.preventDefault();
        }

        function endSwipe(event, index) {
            if (!isSwipeActive) return;

            const deltaX = swipeStartX - swipeCurrentX;
            const swipeContent = document.querySelector(`tr[data-index="${swipeIndex}"] .swipe-content`);

            if (swipeContent) {
                swipeContent.style.transition = 'transform 0.3s ease';

                if (deltaX >= swipeThreshold) {
                    // Swipe suficiente - mostrar botão de excluir
                    swipeContent.style.transform = `translateX(-${swipeMaxDistance}px)`;
                    swipeContent.parentElement.classList.add('swipe-open');
                } else {
                    // Swipe insuficiente - voltar ao normal
                    swipeContent.style.transform = 'translateX(0)';
                    swipeContent.parentElement.classList.remove('swipe-open');
                }
            }

            isSwipeActive = false;
            swipeIndex = -1;
        }

        function closeAllSwipes() {
            const openSwipes = document.querySelectorAll('.swipe-container.swipe-open');
            openSwipes.forEach(container => {
                const swipeContent = container.querySelector('.swipe-content');
                if (swipeContent) {
                    swipeContent.style.transform = 'translateX(0)';
                    swipeContent.style.transition = 'transform 0.3s ease';
                }
                container.classList.remove('swipe-open');
            });
        }

        // Fechar swipes quando clicar fora
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.swipe-container')) {
                closeAllSwipes();
            }
        });

        // Event delegation unificado para clique e swipe
        document.addEventListener('mousedown', function(event) {
            const cell = event.target.closest('.item-cell');
            if (cell && cell.dataset.index !== undefined) {
                handleCellInteraction(event, cell);
            }
        });

        document.addEventListener('mousemove', function(event) {
            if (isSwipeActive) {
                handleSwipe(event, swipeIndex);
            } else if (pendingClickData && !hasMovedDuringClick) {
                // Se moveu durante o delay, cancelar clique e iniciar swipe
                const deltaX = event.clientX - swipeStartX;
                const deltaY = Math.abs(event.clientY - swipeStartY);

                // Movimento para a esquerda com pelo menos 10px
                if (deltaX < -10 && deltaY < 20) {
                    hasMovedDuringClick = true;
                    startSwipeFromPending();
                    handleSwipe(event, swipeIndex);
                } else if (Math.abs(deltaX) > 5 || deltaY > 5) {
                    // Qualquer outro movimento cancela o clique
                    hasMovedDuringClick = true;
                    cancelPendingClick();
                }
            }
        });

        document.addEventListener('mouseup', function(event) {
            if (isSwipeActive) {
                endSwipe(event, swipeIndex);
            } else if (pendingClickData && !hasMovedDuringClick) {
                // Executar clique se não houve movimento
                executePendingClick();
            } else {
                cancelPendingClick();
            }
        });

        // Touch events para mobile
        document.addEventListener('touchstart', function(event) {
            const cell = event.target.closest('.item-cell');
            if (cell && cell.dataset.index !== undefined) {
                handleCellInteraction(event, cell);
            }
        });

        document.addEventListener('touchmove', function(event) {
            if (isSwipeActive) {
                handleSwipe(event, swipeIndex);
            } else if (pendingClickData && !hasMovedDuringClick) {
                const touch = event.touches[0];
                const deltaX = touch.clientX - swipeStartX;
                const deltaY = Math.abs(touch.clientY - swipeStartY);

                // Movimento para a esquerda com pelo menos 10px
                if (deltaX < -10 && deltaY < 20) {
                    hasMovedDuringClick = true;
                    startSwipeFromPending();
                    handleSwipe(event, swipeIndex);
                } else if (Math.abs(deltaX) > 5 || deltaY > 5) {
                    // Qualquer outro movimento cancela o clique
                    hasMovedDuringClick = true;
                    cancelPendingClick();
                }
            }
        });

        document.addEventListener('touchend', function(event) {
            if (isSwipeActive) {
                endSwipe(event, swipeIndex);
            } else if (pendingClickData && !hasMovedDuringClick) {
                executePendingClick();
            } else {
                cancelPendingClick();
            }
        });





        function closeItemModal() {
            document.getElementById('itemModal').style.display = 'none';
            currentItemIndex = -1;
        }

        function editItemFromModal(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            if (currentItemIndex >= 0) {
                editItem(currentItemIndex);
                closeItemModal();
            }
        }

        function deleteItemFromModal(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            if (currentItemIndex >= 0) {
                deleteItem(currentItemIndex);
                closeItemModal();
            }
        }

        // Fechar modal ao clicar fora dele
        window.onclick = function(event) {
            const modal = document.getElementById('itemModal');
            if (event.target === modal) {
                closeItemModal();
            }
        }



        function calculateTotals() {
            // Calculate totals from items data
            let subtotal = 0;

            itemsData.forEach(item => {
                subtotal += parseFloat(item.subtotal) || 0;
            });

            // Get tax values
            const taxValue = parseFloat(document.getElementById('tax_rate').value) || 0;
            const taxType = document.getElementById('tax_type').value;

            // Calculate tax amount based on type
            let taxAmount = 0;
            if (taxType === '%') {
                taxAmount = (subtotal * taxValue) / 100;
            } else {
                taxAmount = taxValue;
            }

            // Get discount values
            const discountValue = parseFloat(document.getElementById('discount_amount').value) || 0;
            const discountType = document.getElementById('discount_type').value;

            // Calculate discount amount based on type
            let discountAmount = 0;
            if (discountType === '%') {
                discountAmount = (subtotal * discountValue) / 100;
            } else {
                discountAmount = discountValue;
            }

            const total = subtotal + taxAmount - discountAmount;

            // Update form fields (hidden inputs)
            document.getElementById('subtotal').value = subtotal.toFixed(2);
            document.getElementById('tax_amount').value = taxAmount.toFixed(2);
            document.getElementById('total').value = Math.max(0, total).toFixed(2);

            // Update display fields
            document.getElementById('subtotal-display').textContent = '$' + subtotal.toFixed(2);
            document.getElementById('total-display').textContent = '$' + Math.max(0, total).toFixed(2);
        }

        function updateTaxAmount() {
            calculateTotals();
        }

        function autoExpandTextarea(textarea) {
            // Reset height to auto to get the correct scrollHeight
            textarea.style.setProperty('height', 'auto', 'important');

            // Calculate new height based on content
            const newHeight = Math.max(45, textarea.scrollHeight);

            // Set height with !important to override CSS conflicts
            textarea.style.setProperty('height', newHeight + 'px', 'important');

            // Debug para iPhone
            console.log('Textarea expanded to:', newHeight + 'px', 'scrollHeight:', textarea.scrollHeight);
        }

        function initializeTextareas() {
            // Initialize all textareas with auto-expand functionality
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                // Set initial height if there's content
                if (textarea.value.trim()) {
                    autoExpandTextarea(textarea);
                }
            });
        }

        function loadExistingItems() {
            // Load items from PHP if editing an existing invoice
            <?php if (!empty($items) && $quote_id > 0): ?>
                <?php foreach ($items as $item): ?>
                    itemsData.push({
                        id: <?php echo $item['inventory_id']; ?>,
                        name: '<?php echo addslashes($item['item_name'] ?? $item['description']); ?>',
                        description: '<?php echo addslashes($item['description']); ?>',
                        quantity: <?php echo $item['quantity']; ?>,
                        price: <?php echo $item['price']; ?>,
                        subtotal: <?php echo $item['subtotal']; ?>
                    });
                <?php endforeach; ?>
                updateItemsTable();
                updateHiddenInputs();
            <?php endif; ?>
        }
    </script>

    <script>
      // Auto-scroll simples: ao focar um input/textarea/select, rola para o centro vis D 0Di vel
      (function(){
        // Guard to avoid ReferenceError from undefined 'e' in this legacy block
        var e = { target: null };

        // document.addEventListener('focusin', function(e){
          var el = e.target;
          var isField = el && (el.tagName === 'INPUT' || el.tagName === 'SELECT');
          // Remover TEXTAREA para evitar interferência na seleção de texto
          if (!isField) return;
          setTimeout(function(){
            try {

              // el.scrollIntoView({ block: 'center', behavior: 'smooth' }); // REMOVIDO PARA TESTE
            } catch(err) {
              // Fallback para navegadores sem op D 07 D 07es
              // el.scrollIntoView(true); // REMOVIDO PARA TESTE
            }
          }, 120);
        // });
      })();

      // Event listener para o formulário do modal de cliente
      const newCustomerForm = document.getElementById('newCustomerForm');
      if (newCustomerForm) {
          newCustomerForm.addEventListener('submit', function(e) {
              e.preventDefault();
              saveCustomer();
          });
      }

      // Event listener para o formulário do modal de item
      const newItemForm = document.getElementById('newItemForm');
      if (newItemForm) {
          newItemForm.addEventListener('submit', function(e) {
              e.preventDefault();
              saveItem();
          });
      }

      // Fechar modais ao clicar fora
      window.addEventListener('click', function(e) {
          const customerModal = document.getElementById('newCustomerModal');
          const itemModal = document.getElementById('newItemModal');

          if (e.target === customerModal) {
              closeModal();
          }

          if (e.target === itemModal) {
              closeNewItemModal();
          }
      });
    </script>
    <script>
    (function(){
      function setImportant(el, prop, value){
        try { el.style.setProperty(prop, value, 'important'); }
        catch(e){ try{ el.style[prop] = value; }catch(_){} }
      }
      // Função syncHeights removida - causava inconsistências de altura
      // Os campos agora usam CSS consistente para altura uniforme

      var btn=document.getElementById('paste_service_address');
      if(btn){
        btn.addEventListener('click', async function(){
          var input=document.getElementById('service_address');
          try{
            if (navigator.clipboard && navigator.clipboard.readText){
              var text=await navigator.clipboard.readText();
              if(text!=null && input){
                input.value = text;
                try{ input.dispatchEvent(new Event('input', {bubbles:true})); }catch(e){}
                input.focus();
              }
            } else {
              alert('Seu navegador não permite leitura da área de transferência. Toque e segure para colar.');
            }
          } catch(e){
            alert('Permita acesso à área de transferência ou use toque longo para colar.');
          }
        });
      }
    })();

    // Variáveis globais para o modal de sucesso
    let currentInvoiceId = null;
    let currentInvoiceNumber = null;

    // Funções para o modal de sucesso da fatura
    function showInvoiceSuccessModal(invoiceId, invoiceNumber, customerName) {
        currentInvoiceId = invoiceId;
        currentInvoiceNumber = invoiceNumber;

        document.getElementById('customerNameDisplay').textContent = 'Name: ' + customerName;
        document.getElementById('invoiceNumberDisplay').textContent = invoiceNumber;
        document.getElementById('invoiceSuccessModal').style.display = 'block';
    }

    function closeInvoiceSuccessModal() {
        document.getElementById('invoiceSuccessModal').style.display = 'none';
        currentInvoiceId = null;
        currentInvoiceNumber = null;
    }

    function createNewInvoice() {
        closeInvoiceSuccessModal();
        // Recarregar a página para criar uma nova fatura
        window.location.href = 'invoice_create.php';
    }

    // Function to view and print invoice PDF
    function viewAndPrintInvoicePDF() {
        if (!currentInvoiceId) {
            alert('Error: Invoice ID not found.');
            return;
        }

        // Open PDF in new window/tab
        const pdfUrl = `generate_invoice_pdf.php?id=${currentInvoiceId}`;
        window.open(pdfUrl, '_blank');
    }

    // Function to share invoice PDF link using device sharing options
    function shareInvoicePDFLink() {
        if (!currentInvoiceId) {
            alert('Error: Invoice ID not found.');
            return;
        }

        // Show loading
        const button = document.getElementById('shareInvoiceLinkBtn');
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Link...';

        // Create shared link via API
        const formData = new FormData();
        formData.append('type', 'invoice');
        formData.append('item_id', currentInvoiceId);
        formData.append('expires_days', 30);

        fetch('create_shared_link_api.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                shareInvoiceWithDeviceOptions(data.link_url);
            } else {
                alert('Error creating link: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error creating link:', error);
            alert('Error creating link. Please try again.');
        })
        .finally(() => {
            // Restore button
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    // Function to share using device native sharing options
    function shareInvoiceWithDeviceOptions(shareUrl) {
        // Get customer name for the message
        const customerName = getCustomerNameFromForm();
        const webShareMessage = `Hello! Here is the link to view invoice ${currentInvoiceNumber} for ${customerName}. This link expires in 30 days. Thank you!`;

        // Check if Web Share API is supported (mobile devices)
        if (navigator.share) {
            navigator.share({
                title: `Invoice ${currentInvoiceNumber} - Tony's AC Repair`,
                text: webShareMessage,
                url: shareUrl
            }).catch(err => {
                console.log('Error sharing:', err);
                // Fallback to manual sharing options with full message including URL
                const fullMessage = `Hello! Here is the link to view invoice ${currentInvoiceNumber} for ${customerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
                showManualShareOptions(shareUrl, fullMessage);
            });
        } else {
            // Fallback for desktop or unsupported browsers - include URL in message
            const fullMessage = `Hello! Here is the link to view invoice ${currentInvoiceNumber} for ${customerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
            showManualShareOptions(shareUrl, fullMessage);
        }
    }

    // Function to show manual sharing options
    function showManualShareOptions(shareUrl, message) {
        // Create a temporary textarea to copy the message
        const textarea = document.createElement('textarea');
        textarea.value = message;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);

        alert('Link and message copied to clipboard! You can now paste it in your preferred messaging app.');
    }

    // Helper function to get customer name from form
    function getCustomerNameFromForm() {
        const customerChip = document.querySelector('.selected-customer-chip');
        if (customerChip) {
            const nameElement = customerChip.querySelector('.customer-name');
            if (nameElement) {
                return nameElement.textContent.trim();
            }
        }
        return 'Customer';
    }

    function printInvoice() {
        if (currentInvoiceId) {
            window.open('invoice_print.php?id=' + currentInvoiceId, '_blank');
        }
    }

    // Função para gerar e compartilhar PDF
    function generateAndSharePDF() {
        if (!currentInvoiceId) {
            alert('Erro: ID da fatura não encontrado.');
            return;
        }

        // Mostrar loading
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando PDF...';

        // Detectar se é dispositivo móvel
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // URLs para PDF
        const pdfViewUrl = `generate_invoice_pdf.php?id=${currentInvoiceId}`;
        const pdfDownloadUrl = `download_invoice_pdf.php?id=${currentInvoiceId}`;

        // Verificar se o navegador suporta Web Share API (principalmente mobile)
        if (navigator.share && isMobile) {
            // Usar Web Share API nativa para compartilhamento
            const shareData = {
                title: `Fatura #${currentInvoiceNumber} - Tony's AC Repair`,
                text: `Confira sua fatura #${currentInvoiceNumber} da Tony's AC Repair`,
                url: pdfViewUrl
            };

            navigator.share(shareData)
                .then(() => {
                    console.log('Compartilhamento realizado com sucesso');
                })
                .catch((error) => {
                    console.log('Erro no compartilhamento:', error);
                    // Fallback: abrir PDF para visualização
                    window.open(pdfViewUrl, '_blank');
                });
        } else {
            // Desktop ou navegadores sem Web Share API
            // Abrir PDF em nova aba
            window.open(pdfViewUrl, '_blank');
        }

        // Restaurar botão após um delay
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        }, 2000);
    }









    // Inicialização do sistema
    document.addEventListener('DOMContentLoaded', function() {

        // Fechar modais ao clicar fora
        window.addEventListener('click', function(e) {
            const invoiceSuccessModal = document.getElementById('invoiceSuccessModal');

            if (e.target === invoiceSuccessModal) {
                closeInvoiceSuccessModal();
            }
        });
    });
    </script>




    <?php if (isset($show_success_modal) && $show_success_modal): ?>
    <script>
        // Mostrar modal de sucesso quando a fatura for criada
        document.addEventListener('DOMContentLoaded', function() {
            showInvoiceSuccessModal(
                <?php echo $created_invoice_id; ?>,
                '<?php echo addslashes($created_invoice_number); ?>',
                '<?php echo addslashes($created_customer_name); ?>'
            );
        });
    </script>
    <?php endif; ?>

    <!-- JavaScript para Menu Mobile (igual ao dashboard) -->
    <script src="js/dashboard-mobile-menu.js"></script>

    <!-- JavaScript para aplicar correções de fonte cross-platform -->
    <script>
        // Adiciona classe 'loaded' ao body após carregamento completo
        document.addEventListener('DOMContentLoaded', function() {
            // Adiciona classe imediatamente
            document.body.classList.add('loaded');

            // Força aplicação de estilos após um pequeno delay para iOS
            setTimeout(function() {
                document.body.classList.add('loaded');

                // Força recálculo de layout para iOS Safari
                if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
                    // Força reflow para aplicar estilos
                    document.body.style.display = 'none';
                    document.body.offsetHeight; // Força reflow
                    document.body.style.display = '';
                }
            }, 100);
        });

        // Força aplicação após carregamento completo da página
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });


    </script>

</body>
</html>
<?php
// Close database connection
db_close();
?>
