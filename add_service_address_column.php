<?php
// Script para adicionar a coluna service_address na tabela invoices
// Include simple session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Set timezone to Florida
date_default_timezone_set('America/New_York');

echo "<!DOCTYPE html>";
echo "<html lang='pt-BR'>";
echo "<head>
    <link rel="stylesheet" href="css/hamburger-fix.css">";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Adicionar Coluna Service Address - Tony's AC Repair</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background-color: #f5f5f5; }";
echo ".container { background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; font-weight: bold; }";
echo ".error { color: #dc3545; font-weight: bold; }";
echo ".info { color: #007bff; font-weight: bold; }";
echo ".warning { color: #ffc107; font-weight: bold; }";
echo ".step { background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; border-radius: 5px; }";
echo ".btn { background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px 5px; }";
echo ".btn:hover { background-color: #0056b3; }";
echo ".btn-success { background-color: #28a745; }";
echo ".btn-success:hover { background-color: #1e7e34; }";
echo ".btn-danger { background-color: #dc3545; }";
echo ".btn-danger:hover { background-color: #c82333; }";
echo "pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";

echo "<h1>🔧 Adicionar Coluna Service Address</h1>";
echo "<p>Este script irá adicionar a coluna <code>service_address</code> na tabela <code>invoices</code>.</p>";

// Verificar se a coluna já existe
echo "<div class='step'>";
echo "<h3>📋 Passo 1: Verificando se a coluna já existe...</h3>";

$check_query = "SHOW COLUMNS FROM invoices LIKE 'service_address'";
$check_result = db_query($check_query);

if (!$check_result) {
    echo "<p class='error'>❌ Erro ao verificar a estrutura da tabela: " . db_error() . "</p>";
    echo "</div></div></body></html>";
    exit;
}

$column_exists = db_num_rows($check_result) > 0;

if ($column_exists) {
    echo "<p class='success'>✅ A coluna 'service_address' já existe na tabela 'invoices'.</p>";
} else {
    echo "<p class='warning'>⚠️ A coluna 'service_address' NÃO existe na tabela 'invoices'.</p>";
}

echo "</div>";

// Se a coluna não existe e o usuário confirmou a adição
if (!$column_exists && isset($_POST['add_column'])) {
    echo "<div class='step'>";
    echo "<h3>🔨 Passo 2: Adicionando a coluna service_address...</h3>";
    
    $alter_query = "ALTER TABLE invoices ADD COLUMN service_address TEXT AFTER notes";
    
    if (db_query($alter_query)) {
        echo "<p class='success'>✅ Coluna 'service_address' adicionada com sucesso!</p>";
        $column_exists = true;
    } else {
        echo "<p class='error'>❌ Erro ao adicionar a coluna: " . db_error() . "</p>";
    }
    echo "</div>";
}

// Se a coluna não existe, mostrar botão para adicionar
if (!$column_exists && !isset($_POST['add_column'])) {
    echo "<div class='step'>";
    echo "<h3>🚀 Pronto para adicionar a coluna</h3>";
    echo "<p>A coluna <code>service_address</code> será adicionada com as seguintes especificações:</p>";
    echo "<ul>";
    echo "<li><strong>Nome:</strong> service_address</li>";
    echo "<li><strong>Tipo:</strong> TEXT</li>";
    echo "<li><strong>Posição:</strong> Após a coluna 'notes'</li>";
    echo "<li><strong>Permite NULL:</strong> Sim</li>";
    echo "</ul>";
    echo "<p><strong>Comando SQL que será executado:</strong></p>";
    echo "<pre>ALTER TABLE invoices ADD COLUMN service_address TEXT AFTER notes;</pre>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='add_column' class='btn btn-success'>✅ Adicionar Coluna</button>";
    echo "</form>";
    echo "</div>";
}

// Se a coluna existe, verificar e testar
if ($column_exists) {
    echo "<div class='step'>";
    echo "<h3>🔍 Passo 3: Verificando a estrutura da tabela...</h3>";
    
    $structure_query = "DESCRIBE invoices";
    $structure_result = db_query($structure_query);
    
    if ($structure_result) {
        echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background-color: #f8f9fa;'>";
        echo "<th style='padding: 8px; text-align: left;'>Campo</th>";
        echo "<th style='padding: 8px; text-align: left;'>Tipo</th>";
        echo "<th style='padding: 8px; text-align: left;'>Null</th>";
        echo "<th style='padding: 8px; text-align: left;'>Chave</th>";
        echo "<th style='padding: 8px; text-align: left;'>Padrão</th>";
        echo "</tr>";
        
        while ($row = db_fetch_assoc($structure_result)) {
            $highlight = ($row['Field'] === 'service_address') ? 'background-color: #d4edda;' : '';
            echo "<tr style='$highlight'>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p class='success'>✅ Estrutura da tabela verificada com sucesso!</p>";
    } else {
        echo "<p class='error'>❌ Erro ao verificar a estrutura da tabela: " . db_error() . "</p>";
    }
    echo "</div>";
    
    // Teste de funcionalidade
    echo "<div class='step'>";
    echo "<h3>🧪 Passo 4: Testando a funcionalidade...</h3>";
    
    $test_query = "SELECT id, invoice_number, service_address FROM invoices LIMIT 5";
    $test_result = db_query($test_query);
    
    if ($test_result) {
        echo "<p class='success'>✅ Teste de consulta realizado com sucesso!</p>";
        
        if (db_num_rows($test_result) > 0) {
            echo "<p><strong>Primeiras 5 faturas (exemplo):</strong></p>";
            echo "<table border='1' style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background-color: #f8f9fa;'>";
            echo "<th style='padding: 8px; text-align: left;'>ID</th>";
            echo "<th style='padding: 8px; text-align: left;'>Número da Fatura</th>";
            echo "<th style='padding: 8px; text-align: left;'>Endereço do Serviço</th>";
            echo "</tr>";
            
            while ($row = db_fetch_assoc($test_result)) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($row['invoice_number']) . "</td>";
                echo "<td style='padding: 8px;'>" . (empty($row['service_address']) ? '<em style="color: #999;">Não definido</em>' : htmlspecialchars($row['service_address'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='info'>ℹ️ Nenhuma fatura encontrada no banco de dados.</p>";
        }
    } else {
        echo "<p class='error'>❌ Erro no teste de consulta: " . db_error() . "</p>";
    }
    echo "</div>";
    
    // Sucesso final
    echo "<div class='step' style='border-left-color: #28a745; background-color: #d4edda;'>";
    echo "<h3 class='success'>🎉 Migração Concluída com Sucesso!</h3>";
    echo "<p>A coluna <code>service_address</code> foi adicionada à tabela <code>invoices</code> e está funcionando corretamente.</p>";
    echo "<p><strong>Agora você pode:</strong></p>";
    echo "<ul>";
    echo "<li>Criar novas faturas com endereços de serviço</li>";
    echo "<li>Editar faturas existentes para adicionar endereços de serviço</li>";
    echo "<li>Visualizar e imprimir faturas com informações de endereço de serviço</li>";
    echo "</ul>";
    echo "</div>";
    
    // Links para próximos passos
    echo "<div class='step'>";
    echo "<h3>📋 Próximos Passos</h3>";
    echo "<a href='invoice_create.php' class='btn btn-success'>Criar Nova Fatura</a>";
    echo "<a href='invoices.php' class='btn'>Ver Todas as Faturas</a>";
    echo "<a href='test_service_address_feature.php' class='btn'>Testar Funcionalidade</a>";
    echo "</div>";
}

// Informações do sistema
echo "<div class='step'>";
echo "<h3>ℹ️ Informações do Sistema</h3>";
echo "<p><strong>Data/Hora Atual:</strong> " . date('d/m/Y H:i:s T') . "</p>";
echo "<p><strong>Fuso Horário:</strong> " . date_default_timezone_get() . " (Florida USA)</p>";
echo "<p><strong>Versão do PHP:</strong> " . phpversion() . "</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

// Close database connection
db_close();
?>
