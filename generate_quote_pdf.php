<?php
// Include session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Check if this is a shared link view
$is_shared = isset($_GET['shared']) && $_GET['shared'] == 1;

// Check if user is logged in (only if not a shared view)
if (!$is_shared && !isset($_SESSION['user_id'])) {
    die('Acesso negado. Faça login para continuar.');
}

// Get quote ID
$quote_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($quote_id <= 0) {
    die('ID do orçamento inválido.');
}

// Fetch quote data
$query = "SELECT q.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.address as customer_address
          FROM quotes q
          JOIN customers c ON q.customer_id = c.id
          WHERE q.id = $quote_id";

$result = db_query($query);
if (!$result || db_num_rows($result) == 0) {
    die('Orçamento não encontrado.');
}

$quote = db_fetch_assoc($result);

// Fetch quote items
$items_query = "SELECT * FROM quote_items WHERE quote_id = $quote_id ORDER BY id";
$items_result = db_query($items_query);

// Convert result to array
$items = [];
while ($item = db_fetch_assoc($items_result)) {
    $items[] = $item;
}

// Close database connection
db_close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Quote <?php echo htmlspecialchars($quote['quote_number']); ?> - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/invoice-pdf.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
    /* Cores personalizadas em verde moderno apenas para o PDF das quotes */

    /* Botão de impressão */
    .print-button {
        background: #28a745 !important; /* Verde moderno e vibrante */
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        color: white;
        border: none;
        border-radius: 0;
        padding: 12px 20px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        z-index: 1000;
        transition: background-color 0.3s ease;
        margin: 0;
    }

    .print-button:hover {
        background: #218838 !important; /* Verde mais escuro no hover */
    }

    /* Header - borda inferior */
    .header {
        border-bottom: 2px solid #28a745 !important;
    }

    /* Título da empresa */
    .company-info h1 {
        color: #28a745 !important;
    }

    /* Número da quote */
    .invoice-number {
        color: #28a745 !important;
    }

    /* Títulos das seções */
    .section-title {
        color: #28a745 !important;
    }

    /* Ícones das seções */
    .section-title .label-icon {
        color: #28a745 !important;
    }

    /* Bordas das caixas de informação */
    .customer-info,
    .payment-info,
    .notes {
        border: 1px solid #6fc276 !important;
    }

    /* Cabeçalho da tabela de itens */
    .items-table th {
        background: #28a745 !important;
        border: 1px solid #218838 !important;
        color: white !important;
    }

    /* Total geral - bordas */
    .grand-total {
        border-top: 2px solid #28a745 !important;
        border-bottom: 2px solid #28a745 !important;
        background: #d4f6d4 !important;
    }

    /* Hover da tabela (opcional - cor mais suave) */
    .items-table tbody tr:hover {
        background: #e8f5e9 !important; /* Verde claro para hover */
    }

    /* Título QUOTE com mesmo tamanho da empresa */
    .quote-title {

        font-weight: bold !important;
        color: #28a745 !important;
        margin-bottom: 8px !important;
        text-align: right !important;
        line-height: 1.2 !important;
    }

    /* Mudança de tons de cinza para preto */
    body {
        color: #000 !important; /* Texto principal em preto */
    }

    .company-details {
        color: #000; /* Detalhes da empresa em preto */
    }

    .invoice-dates {
        color: #000; /* Datas em preto */
    }

    .customer-name {
        color: #000; /* Nome do cliente em preto */
    }

    .customer-info {
        color: #000; /* Informações do cliente em preto */
    }

    .total-label {
        color: #000; /* Labels dos totais em preto */
    }

    .total-amount {
        color: #000; /* Valores dos totais em preto */
    }

    .payment-info {
        color: #000; /* Informações de pagamento em preto */
    }

    .notes {
        color: #000; /* Notas em preto */
    }

    .footer {
        color: #666 !important; /* Footer em cinza (cor anterior) */
    }

    .items-table td {
        color: #000; /* Células da tabela em preto */
    }

    /* Ajuste do body para elementos fixos - sobrescreve CSS externo */
    body {
        padding-top: 48px !important; /* Espaço para o botão de impressão */
    }

    body.shared-view {
        padding-top: 90px !important; /* Espaço adicional para o banner quando compartilhado */
    }

    /* Regras específicas para impressão - garante padding consistente */
    @media print {
        body {
            padding: 0.2in !important; /* Padding padrão para impressão */
            padding-top: 0.2in !important; /* Remove padding extra do topo na impressão */
            margin: 0 !important; /* Remove margin que pode estar interferindo */
        }

        body.shared-view {
            padding: 0.2in !important; /* Mesmo padding para visualização compartilhada */
            padding-top: 0.2in !important;
            margin: 0 !important;
        }

        /* Garante que elementos fixos não apareçam na impressão */
        .print-button,
        .no-print {
            display: none !important;
        }
    }
    </style>
</head>
<body<?php echo $is_shared ? ' class="shared-view"' : ''; ?>>
    <!-- Print Button (always show) -->
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print" style="margin-right: 8px;"></i>Print
    </button>

    <!-- Shared View Header -->
    <?php if ($is_shared): ?>
    <div style="background: #f8f9fa; padding: 8px 15px; text-align: center; border-bottom: 1px solid #dee2e6; margin: 0; width: 100%; position: fixed; top: 48px; left: 0; right: 0; z-index: 999;" class="no-print">
        <p style="margin: 0; color: #6c757d; font-size: 12px; font-weight: normal;">
            <i class="fas fa-share-alt" style="color: #6c757d; margin-right: 6px; font-size: 11px;"></i>
            You are viewing a shared quote link
        </p>
    </div>
    <?php endif; ?>

    <!-- Print Page Wrapper -->
    <div class="print-page">
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Invoice Header -->
    <div class="header">
        <div class="company-info">
            <h1>
                <img src="assets/logo_Tonys.svg" width="32" height="32" alt="Tony's AC Repair Logo">
                Tony's AC Repair
            </h1>
            <div class="company-details">
                Professional Air Conditioning Services<br>
                (321) 419-2166<br>
                <EMAIL>
            </div>
        </div>
        <div class="invoice-info">
            <div class="quote-title">QUOTE</div>
            <div class="invoice-number"><?php
                // Extrair apenas a parte #YYMMDD-XXX do quote_number
                $quote_number = $quote['quote_number'];

                // Primeiro, procurar por qualquer padrão #YYMMDD-XXX no texto
                if (preg_match('/#\d{6}-\d{3}/', $quote_number, $matches)) {
                    // Encontrou o padrão #YYMMDD-XXX, usar ele
                    echo htmlspecialchars($matches[0]);
                } else if (strpos($quote_number, "\n") !== false) {
                    // Se tem quebra de linha, pegar a última parte
                    $parts = explode("\n", $quote_number);
                    $last_part = trim(end($parts));
                    echo htmlspecialchars($last_part);
                } else {
                    // Formato simples, usar como está
                    echo htmlspecialchars($quote_number);
                }
            ?></div>
            <div class="invoice-dates">
                Issued: <?php echo date('m/d/Y', strtotime($quote['quote_date'])); ?><br>
                Valid: <?php echo date('m/d/Y', strtotime(isset($quote['valid_until']) ? $quote['valid_until'] : $quote['quote_date'] . ' +30 days')); ?>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="two-columns">
        <div>
            <div class="section-title label-with-icon">
                <i class="fas fa-user label-icon"></i>
                Customer
            </div>
            <div class="customer-info">
                <div class="customer-name"><?php echo htmlspecialchars($quote['customer_name']); ?></div>
                <div>
                    <?php if ($quote['customer_phone']): ?>
                        Phone: <?php echo htmlspecialchars($quote['customer_phone']); ?><br>
                    <?php endif; ?>
                    <?php if ($quote['customer_email']): ?>
                        Email: <?php echo htmlspecialchars($quote['customer_email']); ?><br>
                    <?php endif; ?>
                    <?php if ($quote['customer_address']): ?>
                        Address: <?php echo htmlspecialchars($quote['customer_address']); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div>
            <div class="section-title label-with-icon">
                <i class="fas fa-map-marker-alt label-icon"></i>
                Service Address
            </div>
            <div class="customer-info">
                <?php if (isset($quote['service_address']) && $quote['service_address']): ?>
                    <div><?php echo htmlspecialchars($quote['service_address']); ?></div>
                <?php else: ?>
                    <div style="color: #999; font-style: italic;">Same as customer address</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Items & Services -->
    <div class="section-title label-with-icon">
        <i class="fas fa-list label-icon"></i>
        Items & Services
    </div>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 15%;" class="text-center">Qty</th>
                <th style="width: 17.5%;" class="text-right">Price</th>
                <th style="width: 17.5%;" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
            <tr>
                <td><?php echo htmlspecialchars($item['description']); ?></td>
                <td class="text-center"><?php echo number_format($item['quantity'], 0); ?></td>
                <td class="text-right">$<?php echo number_format($item['price'], 2); ?></td>
                <td class="text-right">$<?php echo number_format($item['quantity'] * $item['price'], 2); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Quote Totals -->
    <div class="totals">
        <table>
            <tr>
                <td class="total-label">Subtotal:</td>
                <td class="total-amount">$<?php echo number_format(isset($quote['subtotal']) ? $quote['subtotal'] : 0, 2); ?></td>
            </tr>
            <tr>
                <td class="total-label">Tax:</td>
                <td class="total-amount">$<?php echo number_format(isset($quote['tax_amount']) ? $quote['tax_amount'] : 0, 2); ?></td>
            </tr>
            <?php if (isset($quote['discount_amount']) && $quote['discount_amount'] > 0): ?>
            <tr>
                <td class="total-label">Discount:</td>
                <td class="total-amount">-$<?php echo number_format($quote['discount_amount'], 2); ?></td>
            </tr>
            <?php endif; ?>
            <tr class="grand-total">
                <td class="total-label">TOTAL:</td>
                <td class="total-amount">$<?php echo number_format(isset($quote['total']) ? $quote['total'] : 0, 2); ?></td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    <div class="section-title label-with-icon">
        <i class="fas fa-sticky-note label-icon"></i>
        Notes
    </div>
    <div class="notes">
        <?php if (isset($quote['notes']) && !empty($quote['notes'])): ?>
            <div><?php echo nl2br(htmlspecialchars($quote['notes'])); ?></div>
        <?php else: ?>
            <div style="color: #999; font-style: italic;">No additional notes</div>
        <?php endif; ?>
    </div>

    <!-- Payment Methods Information -->
    <div class="section-title">Payment Methods</div>
    <div class="payment-info">
        <strong>Available Payment Methods:</strong> Cash, Zelle, Transfer
    </div>
        </div> <!-- End Content Wrapper -->

    <!-- Footer -->
    <div class="footer">
        <p><strong>Thank you for considering our services!</strong></p>
        <p>Tony's AC Repair - Your comfort is our priority</p>
        <p>For questions about this quote, contact us: (321) 419-2166</p>
    </div>
    </div> <!-- End Print Page Wrapper -->
</body>
</html>
