<?php
// Configurar fuso horário para Florida USA (Eastern Time)
date_default_timezone_set('America/New_York');

// Include simple session fix for cPanel compatibility (if not already loaded)
if (!defined('SIMPLE_SESSION_FIX_LOADED') && file_exists(__DIR__ . '/simple_session_fix.php')) {
    require_once __DIR__ . '/simple_session_fix.php';
}

// Incluir verificação de segurança apenas se não estiver sendo ignorada
if (!defined('BYPASS_SECURITY') && file_exists('security_check.php')) {
    include_once 'security_check.php';
}

// Database connection parameters
$db_host = 'localhost';
$db_name = 'tonyoli_NOVO';
$db_user = 'tonyoli_NOVO';
$db_pass = 'Novos';

// Create connection using mysqli (compatible with PHP 8.2)
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set character set
$conn->set_charset("utf8mb4");

// Set MySQL timezone to match PHP timezone
$conn->query("SET time_zone = '-05:00'"); // Eastern Time (UTC-5, or UTC-4 during DST)

// Define global functions to make code more readable
function db_query($sql) {
    global $conn;
    return $conn->query($sql);
}

function db_fetch_assoc($result) {
    return $result->fetch_assoc();
}

function db_fetch_all($result) {
    return $result->fetch_all(MYSQLI_ASSOC);
}

function db_num_rows($result) {
    return $result->num_rows;
}

function db_insert_id() {
    global $conn;
    return $conn->insert_id;
}

function db_error() {
    global $conn;
    return $conn->error;
}

function db_escape($string) {
    global $conn;
    return $conn->real_escape_string($string);
}

function db_close() {
    global $conn;
    $conn->close();
}

function db_begin_transaction() {
    global $conn;
    $conn->begin_transaction();
}

function db_commit() {
    global $conn;
    $conn->commit();
}

function db_rollback() {
    global $conn;
    $conn->rollback();
}
?>
