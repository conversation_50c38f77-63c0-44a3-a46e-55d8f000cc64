<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$error = $success = '';
$service_calls = [];

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// Função para traduzir o status
function translate_status($status) {
    $translations = [
        'pending' => 'Pendente',
        'in_progress' => 'Em Andamento',
        'completed' => 'Concluído',
        'cancelled' => 'Cancelado'
    ];

    return isset($translations[$status]) ? $translations[$status] : $status;
}

// Função para obter a classe CSS do status
function get_status_class($status) {
    $classes = [
        'pending' => 'status-pending',
        'in_progress' => 'status-in-progress',
        'completed' => 'status-completed',
        'cancelled' => 'status-cancelled'
    ];

    return isset($classes[$status]) ? $classes[$status] : '';
}

// Construir a consulta SQL com base nos filtros
$query = "
    SELECT sc.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email,
           c.address as customer_address, c.city as customer_city, c.state as customer_state, c.zip as customer_zip
    FROM service_calls sc
    JOIN customers c ON sc.customer_id = c.id
    WHERE 1=1
";

// Adicionar filtro de status se fornecido
if (!empty($status_filter)) {
    $status_filter = db_escape($status_filter);
    $query .= " AND sc.status = '$status_filter'";
}

// Adicionar filtro de data se fornecido
if (!empty($date_from)) {
    $date_from = db_escape(date('Y-m-d 00:00:00', strtotime($date_from)));
    $query .= " AND sc.scheduled_date >= '$date_from'";
}

if (!empty($date_to)) {
    $date_to = db_escape(date('Y-m-d 23:59:59', strtotime($date_to)));
    $query .= " AND sc.scheduled_date <= '$date_to'";
}

// Ordenar por data agendada
$query .= " ORDER BY sc.scheduled_date DESC";

// Executar a consulta
$result = db_query($query);

if ($result && db_num_rows($result) > 0) {
    $service_calls = db_fetch_all($result);
}

// Obter contagem de chamados por status para o resumo
$status_counts = [
    'all' => 0,
    'pending' => 0,
    'in_progress' => 0,
    'completed' => 0,
    'cancelled' => 0
];

$count_query = "
    SELECT status, COUNT(*) as count
    FROM service_calls
    GROUP BY status
";

$count_result = db_query($count_query);

if ($count_result && db_num_rows($count_result) > 0) {
    while ($row = db_fetch_assoc($count_result)) {
        $status_counts[$row['status']] = $row['count'];
        $status_counts['all'] += $row['count'];
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Chamados - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Estilos gerais */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f5f7fa;
            font-size: 14px;
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .card-title {
            margin: 0;
            font-size: 1.2rem;
            color: #495057;
            font-weight: 600;
        }

        .card-body {
            padding: 12px 15px;
        }

        /* Estilos para o filtro */
        .filter-form {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .filter-form .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
        }

        .filter-form .form-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .filter-form select,
        .filter-form input[type="date"] {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .filter-form .form-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .filter-form .form-actions {
                flex-direction: column;
            }
        }

        /* Estilos para os botões de ação */
        .action-buttons {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
                margin-top: 10px;
            }

            .card-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .card-title {
                margin-bottom: 10px;
            }
        }

        /* Estilos para o resumo */
        .summary-section {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .summary-card {
            flex: 1;
            min-width: 150px;
            background-color: #fff;
            border-radius: 6px;
            padding: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }

        .summary-card.all {
            border-left: 4px solid #6c757d;
        }

        .summary-card.pending {
            border-left: 4px solid #ffc107;
        }

        .summary-card.in-progress {
            border-left: 4px solid #17a2b8;
        }

        .summary-card.completed {
            border-left: 4px solid #28a745;
        }

        .summary-card.cancelled {
            border-left: 4px solid #dc3545;
        }

        .summary-title {
            font-size: 13px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .summary-count {
            font-size: 24px;
            font-weight: 600;
            color: #343a40;
        }

        /* Estilos para a tabela */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* Estilos para os badges de status */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-align: center;
            min-width: 90px;
            letter-spacing: 0.3px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #b8daff;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Estilos para impressão */
        @media print {
            body {
                background-color: white;
                font-size: 12px;
            }

            .container {
                width: 100%;
                max-width: none;
                padding: 0;
            }

            .card {
                box-shadow: none;
                margin-bottom: 15px;
            }

            .card-header {
                background-color: white;
                border-bottom: 2px solid #333;
                padding: 10px 0;
            }

            .no-print {
                display: none !important;
            }

            .data-table {
                width: 100%;
                border-collapse: collapse;
            }

            .data-table th,
            .data-table td {
                padding: 8px;
                border: 1px solid #ddd;
            }

            .data-table th {
                background-color: #f2f2f2;
            }

            .print-header {
                text-align: center;
                margin-bottom: 20px;
            }

            .print-header h1 {
                font-size: 18px;
                margin-bottom: 5px;
            }

            .print-header p {
                font-size: 14px;
                color: #666;
            }

            .print-footer {
                text-align: center;
                margin-top: 20px;
                font-size: 12px;
                color: #666;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tony's AC Repair</div>

                <!-- Menu Normal -->
                <ul class="nav-links">
                    <li><a href="dashboard_simple.php">Painel</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                    <li><a href="service_calls.php" class="active">Chamados</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <!-- Cabeçalho para impressão -->
            <div class="print-header" style="display: none;">
                <h1>Relatório de Chamados de Serviço</h1>
                <p>Tony's AC Repair - <?php echo date('d/m/Y'); ?></p>
                <?php if (!empty($status_filter)): ?>
                    <p>Filtro: Status = <?php echo translate_status($status_filter); ?></p>
                <?php endif; ?>
                <?php if (!empty($date_from) || !empty($date_to)): ?>
                    <p>Período:
                        <?php echo !empty($date_from) ? date('d/m/Y', strtotime($date_from)) : 'Início'; ?>
                        até
                        <?php echo !empty($date_to) ? date('d/m/Y', strtotime($date_to)) : 'Hoje'; ?>
                    </p>
                <?php endif; ?>
            </div>

            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Relatório de Chamados</h1>
                    <div class="action-buttons no-print">
                        <a href="service_calls.php" class="btn-back"><i class="fas fa-arrow-left"></i> Voltar</a>
                        <button onclick="printReport()" class="btn-print"><i class="fas fa-print"></i> Imprimir</button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Formulário de filtro -->
                    <form method="GET" action="service_call_reports.php" class="filter-form no-print">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="">Todos</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pendente</option>
                                    <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>Em Andamento</option>
                                    <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Concluído</option>
                                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelado</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="date_from">Data Inicial</label>
                                <input type="date" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                            <div class="form-group">
                                <label for="date_to">Data Final</label>
                                <input type="date" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-report"><i class="fas fa-filter"></i> Filtrar</button>
                            <a href="service_call_reports.php" class="btn-back"><i class="fas fa-sync-alt"></i> Limpar Filtros</a>
                        </div>
                    </form>

                    <!-- Resumo -->
                    <div class="summary-section no-print">
                        <div class="summary-card all">
                            <div class="summary-title">Total</div>
                            <div class="summary-count"><?php echo $status_counts['all']; ?></div>
                        </div>
                        <div class="summary-card pending">
                            <div class="summary-title">Pendentes</div>
                            <div class="summary-count"><?php echo $status_counts['pending']; ?></div>
                        </div>
                        <div class="summary-card in-progress">
                            <div class="summary-title">Em Andamento</div>
                            <div class="summary-count"><?php echo $status_counts['in_progress']; ?></div>
                        </div>
                        <div class="summary-card completed">
                            <div class="summary-title">Concluídos</div>
                            <div class="summary-count"><?php echo $status_counts['completed']; ?></div>
                        </div>
                        <div class="summary-card cancelled">
                            <div class="summary-title">Cancelados</div>
                            <div class="summary-count"><?php echo $status_counts['cancelled']; ?></div>
                        </div>
                    </div>

                    <!-- Resultados -->
                    <?php if (empty($service_calls)): ?>
                        <p>Nenhum chamado encontrado com os filtros selecionados.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Cliente</th>
                                        <th>Endereço</th>
                                        <th>Título</th>
                                        <th>Data Agendada</th>
                                        <th>Status</th>
                                        <th class="no-print">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($service_calls as $call): ?>
                                        <tr>
                                            <td><?php echo $call['id']; ?></td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($call['customer_name']); ?></strong><br>
                                                <small><?php echo htmlspecialchars($call['customer_phone']); ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $address_parts = [];
                                                if (!empty($call['customer_address'])) $address_parts[] = $call['customer_address'];
                                                if (!empty($call['customer_city'])) $address_parts[] = $call['customer_city'];
                                                if (!empty($call['customer_state'])) $address_parts[] = $call['customer_state'];
                                                if (!empty($call['customer_zip'])) $address_parts[] = $call['customer_zip'];
                                                echo htmlspecialchars(implode(', ', $address_parts));
                                                ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($call['title']); ?></td>
                                            <td><?php echo format_date($call['scheduled_date']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo get_status_class($call['status']); ?>">
                                                    <?php echo translate_status($call['status']); ?>
                                                </span>
                                            </td>
                                            <td class="no-print">
                                                <div class="action-buttons">
                                                    <a href="service_call_view.php?id=<?php echo $call['id']; ?>" class="btn" title="Visualizar"><i class="fas fa-eye"></i></a>
                                                    <a href="service_call_edit.php?id=<?php echo $call['id']; ?>" class="btn btn-secondary" title="Editar"><i class="fas fa-edit"></i></a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Rodapé para impressão -->
            <div class="print-footer" style="display: none;">
                <p>Relatório gerado em <?php echo date('d/m/Y H:i:s'); ?></p>
                <p>Tony's AC Repair - Todos os direitos reservados</p>
            </div>
        </div>
    </main>

    <footer class="no-print">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Função para imprimir o relatório
        function printReport() {
            // Mostrar elementos específicos para impressão
            document.querySelector('.print-header').style.display = 'block';
            document.querySelector('.print-footer').style.display = 'block';

            // Imprimir
            window.print();

            // Ocultar elementos específicos para impressão após a impressão
            setTimeout(function() {
                document.querySelector('.print-header').style.display = 'none';
                document.querySelector('.print-footer').style.display = 'none';
            }, 1000);
        }
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
