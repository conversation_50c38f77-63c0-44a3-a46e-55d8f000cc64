<?php
// Include database connection
require_once 'db_connect.php';

// Get token from URL
$token = isset($_GET['token']) ? $_GET['token'] : '';

if (empty($token)) {
    die('Token não fornecido.');
}

// Check if shared_links table exists
$table_exists = false;
$tables_result = db_query("SHOW TABLES LIKE 'shared_links'");
if ($tables_result) {
    $table_exists = db_num_rows($tables_result) > 0;
}

if (!$table_exists) {
    die('Sistema de links compartilhados não está configurado.');
}

// Get shared link details
$query = "SELECT sl.*, 
          CASE 
            WHEN sl.type = 'invoice' THEN i.invoice_number 
            WHEN sl.type = 'quote' THEN q.quote_number 
          END as item_number,
          CASE 
            WHEN sl.type = 'invoice' THEN c_i.name 
            WHEN sl.type = 'quote' THEN c_q.name 
          END as customer_name
          FROM shared_links sl
          LEFT JOIN invoices i ON sl.type = 'invoice' AND sl.item_id = i.id
          LEFT JOIN quotes q ON sl.type = 'quote' AND sl.item_id = q.id
          LEFT JOIN customers c_i ON i.customer_id = c_i.id
          LEFT JOIN customers c_q ON q.customer_id = c_q.id
          WHERE sl.token = '" . db_escape($token) . "'";

$result = db_query($query);

if (!$result || db_num_rows($result) === 0) {
    die('Link não encontrado ou inválido.');
}

$link = db_fetch_assoc($result);

// Check if link has expired
if (strtotime($link['expires_at']) < time()) {
    die('Este link expirou em ' . date('d/m/Y', strtotime($link['expires_at'])) . '.');
}

// Update access count and last accessed
$update_query = "UPDATE shared_links SET 
                 access_count = access_count + 1, 
                 last_accessed = NOW() 
                 WHERE id = " . intval($link['id']);
db_query($update_query);

// Redirect to appropriate view
if ($link['type'] === 'invoice') {
    // Redirect to invoice view with shared parameter
    header("Location: generate_invoice_pdf.php?id=" . $link['item_id'] . "&shared=1");
} else {
    // Redirect to quote view with shared parameter
    header("Location: generate_quote_pdf.php?id=" . $link['item_id'] . "&shared=1");
}

// Close database connection
db_close();
exit;
?>
