<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Include strict permissions if available
if (file_exists('strict_permissions.php')) {
    require_once 'strict_permissions.php';

    // Check if user has permission to access reports
    if (function_exists('strict_has_role')) {
        if (strict_has_role('user')) {
            // Redirect user to dashboard
            header('Location: dashboard_simple.php');
            exit;
        }
    } else {
        // Fallback to session role check
        if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'user') {
            // Redirect user to dashboard
            header('Location: dashboard_simple.php');
            exit;
        }
    }
}

// Initialize variables
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01'); // First day of current month
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t'); // Last day of current month
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'all';

// Get quotes data
$quotes_query = "
    SELECT q.*, c.name as customer_name,
    (SELECT COUNT(*) FROM quote_items WHERE quote_id = q.id) as item_count
    FROM quotes q
    JOIN customers c ON q.customer_id = c.id
    WHERE 1=1
";

if (!empty($start_date)) {
    $quotes_query .= " AND q.quote_date >= '$start_date'";
}

if (!empty($end_date)) {
    $quotes_query .= " AND q.quote_date <= '$end_date'";
}

if ($report_type == 'pending') {
    $quotes_query .= " AND q.status = 'pending'";
} elseif ($report_type == 'approved') {
    $quotes_query .= " AND q.status = 'approved'";
} elseif ($report_type == 'rejected') {
    $quotes_query .= " AND q.status = 'rejected'";
}

$quotes_query .= " ORDER BY q.quote_date DESC";

$quotes_result = db_query($quotes_query);
$quotes = [];
if ($quotes_result && db_num_rows($quotes_result) > 0) {
    $quotes = db_fetch_all($quotes_result);
}

// Get invoices data
$invoices_query = "
    SELECT i.*, c.name as customer_name,
    (SELECT COUNT(*) FROM invoice_items WHERE invoice_id = i.id) as item_count
    FROM invoices i
    JOIN customers c ON i.customer_id = c.id
    WHERE 1=1
";

if (!empty($start_date)) {
    $invoices_query .= " AND i.invoice_date >= '$start_date'";
}

if (!empty($end_date)) {
    $invoices_query .= " AND i.invoice_date <= '$end_date'";
}

if ($report_type == 'paid') {
    $invoices_query .= " AND i.status = 'paid'";
} elseif ($report_type == 'unpaid') {
    $invoices_query .= " AND i.status = 'unpaid'";
} elseif ($report_type == 'partial') {
    $invoices_query .= " AND i.status = 'partial'";
}

$invoices_query .= " ORDER BY i.invoice_date DESC";

$invoices_result = db_query($invoices_query);
$invoices = [];
if ($invoices_result && db_num_rows($invoices_result) > 0) {
    $invoices = db_fetch_all($invoices_result);
}

// Calculate totals
$quotes_total = 0;
$quotes_count = count($quotes);
foreach ($quotes as $quote) {
    $quotes_total += $quote['total'];
}

$invoices_total = 0;
$invoices_paid_total = 0;
$invoices_unpaid_total = 0;
$invoices_count = count($invoices);

// Get payment data for partial payments
$invoice_payments = [];
foreach ($invoices as $invoice) {
    if ($invoice['status'] == 'partial') {
        $payment_query = "SELECT SUM(amount) as total_paid FROM invoice_payments WHERE invoice_id = {$invoice['id']}";
        $payment_result = db_query($payment_query);
        if ($payment_result && $payment_row = db_fetch_assoc($payment_result)) {
            $invoice_payments[$invoice['id']] = $payment_row['total_paid'] ?: 0;
        } else {
            $invoice_payments[$invoice['id']] = 0;
        }
    }
}

foreach ($invoices as $invoice) {
    $invoices_total += $invoice['total'];

    if ($invoice['status'] == 'paid') {
        // Fully paid invoices
        $invoices_paid_total += $invoice['total'];
    } elseif ($invoice['status'] == 'partial') {
        // For partial payments, add the paid amount to paid_total and the remainder to unpaid_total
        $paid_amount = $invoice_payments[$invoice['id']];
        $unpaid_amount = $invoice['total'] - $paid_amount;

        $invoices_paid_total += $paid_amount;
        $invoices_unpaid_total += $unpaid_amount;
    } elseif ($invoice['status'] == 'unpaid') {
        // Completely unpaid invoices
        $invoices_unpaid_total += $invoice['total'];
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Estilos para a página de relatórios */
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-body {
            padding: 20px;
        }

        .card-title {
            margin: 0;
            font-size: 1.5rem;
            color: #333;
        }

        .filter-form {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }

        .filter-form .form-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .filter-form input, .filter-form select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .filter-form button {
            padding: 8px 15px;
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 24px;
        }

        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            flex: 1;
            min-width: 200px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }

        .summary-card.quotes {
            border-top: 4px solid #4a6cf7;
        }

        .summary-card.invoices {
            border-top: 4px solid #28a745;
        }

        .summary-card.paid {
            border-top: 4px solid #28a745;
        }

        .summary-card.unpaid {
            border-top: 4px solid #dc3545;
        }

        .summary-card h3 {
            margin-top: 0;
            color: #333;
            font-size: 1.2rem;
        }

        .summary-card .amount {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }

        .summary-card .count {
            color: #6c757d;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .data-table th, .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .status-unpaid {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-partial {
            background-color: #fff3cd;
            color: #856404;
        }

        .tab-container {
            margin-top: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }

        .tab-button {
            padding: 10px 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }

        .tab-button.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }

        .tab-content.active {
            display: block;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8rem;
        }

        .print-button {
            margin-top: 20px;
            text-align: center;
        }

        @media print {
            .no-print {
                display: none;
            }

            .card {
                box-shadow: none;
                margin-bottom: 30px;
            }

            .data-table th, .data-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="report-container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title"><i class="fas fa-chart-bar"></i> Financial Report</h1>
                </div>
                <div class="card-body">
                    <!-- Filtros -->
                    <form method="GET" action="reports.php" class="filter-form no-print">
                        <div class="form-group">
                            <label for="start_date"><i class="fas fa-calendar"></i> Start Date:</label>
                            <input type="date" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                        </div>

                        <div class="form-group">
                            <label for="end_date"><i class="fas fa-calendar"></i> End Date:</label>
                            <input type="date" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                        </div>

                        <div class="form-group">
                            <label for="report_type"><i class="fas fa-filter"></i> Report Type:</label>
                            <select id="report_type" name="report_type">
                                <option value="all" <?php echo $report_type == 'all' ? 'selected' : ''; ?>>All</option>
                                <option value="pending" <?php echo $report_type == 'pending' ? 'selected' : ''; ?>>Pending Quotes</option>
                                <option value="approved" <?php echo $report_type == 'approved' ? 'selected' : ''; ?>>Approved Quotes</option>
                                <option value="rejected" <?php echo $report_type == 'rejected' ? 'selected' : ''; ?>>Rejected Quotes</option>
                                <option value="paid" <?php echo $report_type == 'paid' ? 'selected' : ''; ?>>Paid Invoices</option>
                                <option value="unpaid" <?php echo $report_type == 'unpaid' ? 'selected' : ''; ?>>Unpaid Invoices</option>
                                <option value="partial" <?php echo $report_type == 'partial' ? 'selected' : ''; ?>>Partially Paid Invoices</option>
                            </select>
                        </div>

                        <button type="submit"><i class="fas fa-search"></i> Filter</button>
                    </form>

                    <!-- Resumo -->
                    <div class="summary-cards">
                        <div class="summary-card quotes">
                            <h3><i class="fas fa-file-invoice"></i> Quotes</h3>
                            <div class="amount">$<?php echo number_format($quotes_total, 2); ?></div>
                            <div class="count"><?php echo $quotes_count; ?> quotes</div>
                        </div>

                        <div class="summary-card invoices">
                            <h3><i class="fas fa-file-invoice-dollar"></i> Invoices</h3>
                            <div class="amount">$<?php echo number_format($invoices_total, 2); ?></div>
                            <div class="count"><?php echo $invoices_count; ?> invoices</div>
                        </div>

                        <div class="summary-card paid">
                            <h3><i class="fas fa-check-circle"></i> Received</h3>
                            <div class="amount">$<?php echo number_format($invoices_paid_total, 2); ?></div>
                            <div class="count">Paid invoices</div>
                        </div>

                        <div class="summary-card unpaid">
                            <h3><i class="fas fa-exclamation-circle"></i> Receivable</h3>
                            <div class="amount">$<?php echo number_format($invoices_unpaid_total, 2); ?></div>
                            <div class="count">Pending invoices</div>
                        </div>
                    </div>

                    <!-- Tabs -->
                    <div class="tab-container">
                        <div class="tab-buttons">
                            <div class="tab-button active" data-tab="quotes"><i class="fas fa-file-invoice"></i> Quotes</div>
                            <div class="tab-button" data-tab="invoices"><i class="fas fa-file-invoice-dollar"></i> Invoices</div>
                        </div>

                        <!-- Quotes Tab -->
                        <div class="tab-content active" id="quotes-tab">
                            <?php if (empty($quotes)): ?>
                                <p>No quotes found for the selected period.</p>
                            <?php else: ?>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Number</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th class="no-print">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($quotes as $quote): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($quote['quote_number']); ?></td>
                                                <td><?php echo htmlspecialchars($quote['customer_name']); ?></td>
                                                <td><?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></td>
                                                <td>$<?php echo number_format($quote['total'], 2); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = 'status-' . $quote['status'];
                                                    $status_text = '';
                                                    switch ($quote['status']) {
                                                        case 'pending':
                                                            $status_text = 'Pending';
                                                            break;
                                                        case 'approved':
                                                            $status_text = 'Approved';
                                                            break;
                                                        case 'rejected':
                                                            $status_text = 'Rejected';
                                                            break;
                                                        case 'expired':
                                                            $status_text = 'Expired';
                                                            break;
                                                        default:
                                                            $status_text = ucfirst($quote['status']);
                                                    }
                                                    ?>
                                                    <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td class="no-print">
                                                    <div class="action-buttons">
                                                        <a href="quote_view.php?id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-info" title="View"><i class="fas fa-eye"></i></a>
                                                        <a href="quote_print.php?id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-primary" title="Print" target="_blank"><i class="fas fa-print"></i></a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3">Total</th>
                                            <th>$<?php echo number_format($quotes_total, 2); ?></th>
                                            <th colspan="2"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            <?php endif; ?>
                        </div>

                        <!-- Invoices Tab -->
                        <div class="tab-content" id="invoices-tab">
                            <?php if (empty($invoices)): ?>
                                <p>No invoices found for the selected period.</p>
                            <?php else: ?>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Number</th>
                                            <th>Customer</th>
                                            <th>Date</th>
                                            <th>Due Date</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th class="no-print">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($invoices as $invoice): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                                <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                                <td><?php echo date('d/m/Y', strtotime($invoice['invoice_date'])); ?></td>
                                                <td><?php echo !empty($invoice['due_date']) ? date('d/m/Y', strtotime($invoice['due_date'])) : '-'; ?></td>
                                                <td>$<?php echo number_format($invoice['total'], 2); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = 'status-' . $invoice['status'];
                                                    $status_text = '';
                                                    switch ($invoice['status']) {
                                                        case 'pending':
                                                            $status_text = 'Pending';
                                                            break;
                                                        case 'paid':
                                                            $status_text = 'Paid';
                                                            break;
                                                        case 'partial':
                                                            $status_text = 'Partial';
                                                            break;
                                                        case 'cancelled':
                                                            $status_text = 'Cancelled';
                                                            break;
                                                        case 'overdue':
                                                            $status_text = 'Overdue';
                                                            break;
                                                        default:
                                                            $status_text = ucfirst($invoice['status']);
                                                    }
                                                    ?>
                                                    <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td class="no-print">
                                                    <div class="action-buttons">
                                                        <a href="invoice_view.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-info" title="View"><i class="fas fa-eye"></i></a>
                                                        <a href="invoice_print.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-primary" title="Print" target="_blank"><i class="fas fa-print"></i></a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="4">Total</th>
                                            <th>$<?php echo number_format($invoices_total, 2); ?></th>
                                            <th colspan="2"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Print Button -->
                    <div class="print-button no-print">
                        <button onclick="window.print()" class="btn"><i class="fas fa-print"></i> Print Report</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. All rights reserved.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to current button and content
                    this.classList.add('active');
                    document.getElementById(tabId + '-tab').classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
