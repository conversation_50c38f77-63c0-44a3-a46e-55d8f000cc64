<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Determina qual página está ativa
$current_page = basename($_SERVER['PHP_SELF']);

// Get status filter if provided
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filter
$query = "
    SELECT i.*, c.name as customer_name
    FROM invoices i
    JOIN customers c ON i.customer_id = c.id
";

if (!empty($status_filter)) {
    $status_filter = db_escape($status_filter);
    $query .= " WHERE i.status = '$status_filter'";
}

$query .= " ORDER BY i.invoice_date DESC";

// Get all invoices
$invoices = [];
$result = db_query($query);

if ($result && db_num_rows($result) > 0) {
    $invoices = db_fetch_all($result);
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light">
    <title>Invoices - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/invoices-mobile-buttons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* CSS específico para menu hamburger - extraído do dashboard_simple-layout.css */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Header Mobile */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mobile-header h1 {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .menu-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 6px;
            transition: background-color 0.2s ease;
            z-index: 1001;
            position: relative;
            width: 44px;
            height: 44px;
            min-width: 44px;
            min-height: 44px;
            flex-shrink: 0;
        }

        .menu-toggle:hover {
            background-color: rgba(255,255,255,0.2);
        }

        .menu-toggle:focus {
            outline: none;
        }

        /* Menu Deslizante */
        .slide-menu {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            z-index: 999;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            max-height: calc(100vh - 60px);
            overflow-y: auto;
        }

        .slide-menu:focus {
            outline: none;
        }

        .slide-menu.active {
            transform: translateY(0);
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            padding: 1.5rem;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem 1rem;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            color: white;
            text-decoration: none;
            transition: background-color 0.2s ease;
            min-height: 100px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
        }

        .menu-item.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .menu-item i {
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .menu-item span {
            font-size: 0.9rem;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }

        /* Overlay */
        .menu-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 998;
        }

        .menu-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Layout Principal */
        .main-content {
            margin-top: 60px;
            padding: 1rem;
            min-height: calc(100vh - 60px);
        }

        /* Cabeçalho da página */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0 0.5rem;
        }

        .page-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .page-header .btn {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            border-radius: 6px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background-color: #007bff;
            color: white;
            border: none;
            transition: background-color 0.2s ease;
        }

        .page-header .btn:hover {
            background-color: #0056b3;
            text-decoration: none;
            color: white;
        }

        .page-header .btn i {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Mobile Header (igual ao dashboard) -->
    <header class="mobile-header">
        <h1>Tony's AC Repair</h1>
        <button class="menu-toggle" id="menuToggle" aria-expanded="false" aria-controls="slideMenu" aria-label="Toggle navigation menu">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Slide Menu (igual ao dashboard) -->
    <nav class="slide-menu" id="slideMenu" role="navigation" aria-label="Main navigation">
        <div class="menu-grid">
            <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">
                <i class="fa fa-home"></i>
                <span>Painel</span>
            </a>
            <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">
                <i class="fa fa-users"></i>
                <span>Clientes</span>
            </a>
            <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">
                <i class="fa fa-box"></i>
                <span>Estoque</span>
            </a>
            <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice"></i>
                <span>Orçamentos</span>
            </a>
            <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice-dollar"></i>
                <span>Faturas</span>
            </a>
            <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php') ? 'active' : ''; ?>">
                <i class="fa fa-headset"></i>
                <span>Chamados</span>
            </a>
            <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">
                <i class="fa fa-share-alt"></i>
                <span>Links</span>
            </a>
            <a href="logout.php" class="menu-item">
                <i class="fa fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </nav>

    <!-- Menu Overlay (igual ao dashboard) -->
    <div class="menu-overlay" id="menuOverlay" aria-hidden="true"></div>

    <!-- Menu lateral removido -->
    <div class="main-content">
        <div class="container-full">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <h1>Invoices</h1>
                <a href="invoice_create.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New
                </a>
            </div>
                    <!-- Mobile Filter Buttons -->
                    <div class="mobile-filter-buttons">
                        <a href="invoices.php" class="mobile-filter-btn btn-all <?php echo empty($status_filter) ? 'active' : ''; ?>">
                            <i class="fas fa-list"></i>
                            <span>All</span>
                        </a>
                        <a href="invoices.php?status=unpaid" class="mobile-filter-btn btn-unpaid <?php echo $status_filter === 'unpaid' ? 'active' : ''; ?>">
                            <i class="fas fa-times-circle"></i>
                            <span>Unpaid</span>
                        </a>
                        <a href="invoices.php?status=paid" class="mobile-filter-btn btn-paid <?php echo $status_filter === 'paid' ? 'active' : ''; ?>">
                            <i class="fas fa-check-circle"></i>
                            <span>Paid</span>
                        </a>
                        <a href="invoices.php?status=partial" class="mobile-filter-btn btn-partial <?php echo $status_filter === 'partial' ? 'active' : ''; ?>">
                            <i class="fas fa-adjust"></i>
                            <span>Partial</span>
                        </a>
                        <a href="invoices.php?status=cancelled" class="mobile-filter-btn btn-cancelled <?php echo $status_filter === 'cancelled' ? 'active' : ''; ?>">
                            <i class="fas fa-ban"></i>
                            <span>Cancelled</span>
                        </a>
                    </div>
                    <div class="filter-options">
                        <a href="invoices.php" class="btn <?php echo empty($status_filter) ? 'active' : ''; ?>"><i class="fas fa-list"></i> All</a>
                        <a href="invoices.php?status=unpaid" class="btn <?php echo $status_filter === 'unpaid' ? 'active' : ''; ?>"><i class="fas fa-times-circle"></i> Unpaid</a>
                        <a href="invoices.php?status=paid" class="btn <?php echo $status_filter === 'paid' ? 'active' : ''; ?>"><i class="fas fa-check-circle"></i> Paid</a>
                        <a href="invoices.php?status=partial" class="btn <?php echo $status_filter === 'partial' ? 'active' : ''; ?>"><i class="fas fa-adjust"></i> Partially Paid</a>
                        <a href="invoices.php?status=cancelled" class="btn <?php echo $status_filter === 'cancelled' ? 'active' : ''; ?>"><i class="fas fa-ban"></i> Cancelled</a>
                    </div>

                    <?php if (empty($invoices)): ?>
                        <p>No invoices found.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="invoice_number"><i class="fas fa-hashtag"></i> Number</th>
                                    <th data-sort="customer_name"><i class="fas fa-user"></i> Customer</th>
                                    <th data-sort="invoice_date"><i class="fas fa-calendar"></i> Date</th>
                                    <th data-sort="total"><i class="fas fa-dollar-sign"></i> Total</th>
                                    <th data-sort="status"><i class="fas fa-info-circle"></i> Status</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoices as $invoice): ?>
                                    <tr>
                                        <td data-column="invoice_number"><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                        <td data-column="customer_name"><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                        <td data-column="invoice_date"><?php echo date('m/d/Y', strtotime($invoice['invoice_date'])); ?></td>
                                        <td data-column="total">$<?php echo number_format($invoice['total'], 2, '.', ','); ?></td>
                                        <td data-column="status">
                                            <?php
                                            $status = '';
                                            switch ($invoice['status']) {
                                                case 'paid':
                                                    $status = '<span class="status-paid">Paid</span>';
                                                    break;
                                                case 'unpaid':
                                                    $status = '<span class="status-unpaid">Unpaid</span>';
                                                    break;
                                                case 'partial':
                                                    $status = '<span class="status-partial">Partial</span>';
                                                    break;
                                                case 'cancelled':
                                                    $status = '<span class="status-cancelled">Cancelled</span>';
                                                    break;
                                                default:
                                                    $status = $invoice['status'];
                                            }
                                            echo $status;
                                            ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons" style="position: relative;">
                                                <a href="invoice_view.php?id=<?php echo $invoice['id']; ?>" class="btn" title="View"><i class="fas fa-eye"></i></a>
                                                <a href="invoice_edit.php?id=<?php echo $invoice['id']; ?>" class="btn btn-secondary" title="Edit"><i class="fas fa-edit"></i></a>
                                                <a href="invoice_payment.php?id=<?php echo $invoice['id']; ?>" class="btn btn-success" title="Payment"><i class="fas fa-dollar-sign"></i></a>
                                                <a href="invoice_print.php?id=<?php echo $invoice['id']; ?>" class="btn btn-info" target="_blank" title="Print"><i class="fas fa-print"></i></a>
                                                <a href="shared_links.php?type=invoice&item_id=<?php echo $invoice['id']; ?>" class="btn btn-primary" title="Share"><i class="fas fa-share-alt"></i></a>
                                                <a href="invoice_delete.php?id=<?php echo $invoice['id']; ?>" class="btn btn-danger delete-btn" title="Delete"><i class="fas fa-trash-alt"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    <?php endif; ?>
        </div> <!-- Fecha container-full -->
    </div> <!-- Fecha main-content -->

    <script src="js/main.js"></script>
    <script src="js/mobile-menu-fix-new.js"></script>
    <script src="js/invoices-mobile-buttons.js"></script>
    <script>
        // JavaScript simplificado para menu hamburger - sem animações problemáticas
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            const slideMenu = document.getElementById('slideMenu');
            const menuOverlay = document.getElementById('menuOverlay');

            if (!menuToggle || !slideMenu || !menuOverlay) {
                return;
            }

            function toggleMenu() {
                const isActive = slideMenu.classList.contains('active');

                if (isActive) {
                    closeMenu();
                } else {
                    openMenu();
                }
            }

            function openMenu() {
                slideMenu.classList.add('active');
                menuOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                menuToggle.setAttribute('aria-expanded', 'true');
            }

            function closeMenu() {
                slideMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.style.overflow = '';
                menuToggle.setAttribute('aria-expanded', 'false');
            }

            // Event listeners
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleMenu();
            });

            menuOverlay.addEventListener('click', closeMenu);

            // Fechar menu ao clicar em um item
            const menuItems = slideMenu.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    setTimeout(closeMenu, 100);
                });
            });

            // Fechar com ESC
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && slideMenu.classList.contains('active')) {
                    closeMenu();
                }
            });

            // Fechar ao redimensionar
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768 && slideMenu.classList.contains('active')) {
                    closeMenu();
                }
            });
        });
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
