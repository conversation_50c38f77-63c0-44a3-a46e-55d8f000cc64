<?php
// Include simple session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Set timezone to Florida
date_default_timezone_set('America/New_York');

echo "<h1>Add Service Address Field to Invoices Table</h1>";

// Check if the service_address column already exists
$check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'service_address'");

if (db_num_rows($check_column) > 0) {
    echo "<p style='color: green;'>✅ Service address column already exists in invoices table.</p>";
} else {
    echo "<p>Adding service_address column to invoices table...</p>";
    
    // Add the service_address column
    $alter_query = "ALTER TABLE invoices ADD COLUMN service_address TEXT AFTER notes";
    
    if (db_query($alter_query)) {
        echo "<p style='color: green;'>✅ Successfully added service_address column to invoices table.</p>";
    } else {
        echo "<p style='color: red;'>❌ Error adding service_address column: " . db_error() . "</p>";
    }
}

// Verify the column was added
echo "<h2>Current Invoices Table Structure:</h2>";
$columns_result = db_query("SHOW COLUMNS FROM invoices");

if ($columns_result) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = db_fetch_assoc($columns_result)) {
        $highlight = ($column['Field'] === 'service_address') ? 'style="background-color: #d4edda;"' : '';
        echo "<tr $highlight>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ Error retrieving table structure: " . db_error() . "</p>";
}

echo "<h2>Test Query:</h2>";
$test_query = "SELECT id, invoice_number, service_address FROM invoices LIMIT 5";
$test_result = db_query($test_query);

if ($test_result) {
    echo "<p style='color: green;'>✅ Test query successful. Service address field is working.</p>";
    
    if (db_num_rows($test_result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin-top: 10px;'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Service Address</th></tr>";
        
        while ($row = db_fetch_assoc($test_result)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['invoice_number']) . "</td>";
            echo "<td>" . (empty($row['service_address']) ? '<em>Not set</em>' : htmlspecialchars($row['service_address'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No invoices found in the database.</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Test query failed: " . db_error() . "</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>✅ Database migration completed</li>";
echo "<li>🔄 Update invoice creation form to include service address field</li>";
echo "<li>🔄 Update invoice editing form to include service address field</li>";
echo "<li>🔄 Update invoice view and print pages to display service address</li>";
echo "<li>🔄 Test the complete functionality</li>";
echo "</ol>";

echo "<p><a href='invoice_create.php'>Go to Invoice Creation</a> | <a href='invoices_list.php'>View Invoices List</a></p>";

// Close database connection
db_close();
?>
