<?php
// Habilitar exibição de erros para depuração
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Iniciar sessão se ainda não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Incluir conexão com o banco de dados
require_once 'db_connect.php';

// Incluir funções de permissão
if (!function_exists('user_has_role') && file_exists('includes/permissions.php')) {
    include_once 'includes/permissions.php';
}

// Verificar se o usuário é administrador
$is_admin = false;
if (function_exists('user_has_role')) {
    $is_admin = user_has_role('administrator');
}

// Se não for administrador, verificar se existe algum administrador no sistema
$admin_exists = false;
$show_admin_link = $is_admin;

if (!$show_admin_link && isset($conn)) {
    // Verificar se a tabela user_roles existe e tem a coluna 'name'
    $result = $conn->query("SHOW COLUMNS FROM user_roles LIKE 'name'");
    if ($result && $result->num_rows > 0) {
        $query = "SELECT 1 FROM users u
                  JOIN user_roles ur ON u.role_id = ur.id
                  WHERE ur.name = 'administrator'
                  LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    } else {
        // Verificar se há algum usuário com role_id = 1 (assumindo que 1 é o ID do administrador)
        $query = "SELECT 1 FROM users WHERE role_id = 1 LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    }

    // Se não existir administrador, mostrar os links para qualquer usuário
    if (!$admin_exists) {
        $show_admin_link = true;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu de Administração</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .admin-menu {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .menu-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .menu-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .menu-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .menu-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }
        .menu-card-body {
            padding: 20px;
        }
        .menu-card-description {
            color: #6c757d;
            margin-bottom: 20px;
        }
        .menu-card-link {
            display: inline-block;
            background-color: #0d6efd;
            color: #fff;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        .menu-card-link:hover {
            background-color: #0b5ed7;
            color: #fff;
        }
        .menu-card-icon {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .not-available {
            opacity: 0.5;
            pointer-events: none;
        }
        .not-available .menu-card-link {
            background-color: #6c757d;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-admin {
            background-color: #cfe2ff;
            color: #0d6efd;
        }
        .status-no-admin {
            background-color: #f8d7da;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Menu de Administração</h1>
            <div>
                <a href="dashboard_simple_fixed.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar para o Dashboard
                </a>
                <a href="check_admin_status.php" class="btn btn-info">
                    <i class="fas fa-info-circle"></i> Verificar Status
                </a>
            </div>
        </div>

        <div class="status-info">
            <?php if ($is_admin): ?>
                <p>
                    <span class="status-badge status-admin">Administrador</span>
                    Você tem acesso completo às funcionalidades de administração.
                </p>
            <?php elseif (!$admin_exists): ?>
                <p>
                    <span class="status-badge status-no-admin">Sem Administrador</span>
                    Não há administradores no sistema. Você tem acesso temporário às funcionalidades de administração.
                </p>
            <?php else: ?>
                <p>
                    <span class="status-badge">Usuário Regular</span>
                    Você não tem permissões de administrador. Algumas funcionalidades podem estar restritas.
                </p>
            <?php endif; ?>
        </div>

        <div class="admin-menu">
            <div class="menu-card <?php echo $show_admin_link ? '' : 'not-available'; ?>">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-users-cog menu-card-icon"></i>
                        Gerenciar Usuários
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Adicione, edite ou remova usuários do sistema. Gerencie informações de conta e credenciais.
                    </p>
                    <a href="admin_users.php" class="menu-card-link">
                        <i class="fas fa-users"></i> Acessar
                    </a>
                </div>
            </div>

            <div class="menu-card <?php echo $show_admin_link ? '' : 'not-available'; ?>">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-user-tag menu-card-icon"></i>
                        Gerenciar Permissões
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Configure as permissões para cada role no sistema. Defina quais ações cada tipo de usuário pode realizar.
                    </p>
                    <a href="admin_roles.php" class="menu-card-link">
                        <i class="fas fa-shield-alt"></i> Acessar
                    </a>
                </div>
            </div>

            <div class="menu-card <?php echo $show_admin_link ? '' : 'not-available'; ?>">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-user-shield menu-card-icon"></i>
                        Atribuir Roles
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Atribua roles (funções) aos usuários do sistema. Defina quem é administrador, gerente ou usuário regular.
                    </p>
                    <a href="admin_manage_roles.php" class="menu-card-link">
                        <i class="fas fa-user-edit"></i> Acessar
                    </a>
                </div>
            </div>

            <div class="menu-card <?php echo $show_admin_link ? '' : 'not-available'; ?>">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-key menu-card-icon"></i>
                        Visualizar Permissões
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Veja todas as permissões atribuídas a cada role no sistema. Visualize quais ações cada tipo de usuário pode realizar.
                    </p>
                    <a href="admin_view_permissions.php" class="menu-card-link">
                        <i class="fas fa-eye"></i> Acessar
                    </a>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-tools menu-card-icon"></i>
                        Ferramentas de Diagnóstico
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Ferramentas para diagnosticar e corrigir problemas no sistema de usuários e permissões.
                    </p>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <a href="fix_admin_user.php" class="menu-card-link">
                            <i class="fas fa-user-cog"></i> Corrigir Usuário Admin
                        </a>
                        <a href="create_custom_admin.php" class="menu-card-link" style="background-color: #2ecc71;">
                            <i class="fas fa-user-plus"></i> Criar Admin Personalizado
                        </a>
                    </div>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-database menu-card-icon"></i>
                        Banco de Dados
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Ferramentas para verificar e corrigir problemas nas tabelas do banco de dados.
                    </p>
                    <a href="fix_database_tables.php" class="menu-card-link">
                        <i class="fas fa-table"></i> Corrigir Tabelas
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php
// Fechar conexão
if (isset($conn) && $conn instanceof mysqli) {
    $conn->close();
}
?>
