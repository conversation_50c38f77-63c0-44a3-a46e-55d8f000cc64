<?php
// Start output buffering to catch any unwanted output
ob_start();

// Set content type to JSON
header('Content-Type: application/json');

// Include database connection
require_once 'db_connect.php';

// Check if this is an AJAX request
if (!isset($_POST['ajax']) || $_POST['ajax'] != '1') {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Invalid request method'
    ]);
    exit;
}

// Get form data
$name = trim($_POST['item_name'] ?? '');
$sku = trim($_POST['item_sku'] ?? '');
$description = trim($_POST['item_description'] ?? '');
$price = trim($_POST['item_price'] ?? '');
$cost = trim($_POST['item_cost'] ?? '0');
$category = trim($_POST['item_category'] ?? '');

// Validate required fields
if (empty($name)) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Item name is required'
    ]);
    exit;
}

if (empty($price) || !is_numeric($price) || floatval($price) < 0) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Valid price is required'
    ]);
    exit;
}

// Validate cost if provided
if (!empty($cost) && (!is_numeric($cost) || floatval($cost) < 0)) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Cost must be a valid number'
    ]);
    exit;
}

try {
    // Connect to database
    if (!db_connect()) {
        ob_clean();
        echo json_encode([
            'success' => false,
            'error' => 'Database connection failed'
        ]);
        exit;
    }

    // Escape data to prevent SQL injection
    $name = db_escape($name);
    $sku = db_escape($sku);
    $description = db_escape($description);
    $price = floatval($price);
    $cost = floatval($cost);
    $category = db_escape($category);

    // Simple insert with basic fields
    $query = "INSERT INTO inventory (name, price, description, quantity)
              VALUES ('$name', $price, '$description', 0)";

    $result = db_query($query);
    
    if ($result) {
        $item_id = db_insert_id();

        // Return the created item data
        $item = [
            'id' => $item_id,
            'name' => $name,
            'price' => $price,
            'description' => $description
        ];

        ob_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Item created successfully',
            'item' => $item
        ]);
    } else {
        ob_clean();
        echo json_encode([
            'success' => false,
            'error' => 'Failed to create item: ' . db_error()
        ]);
    }
    
} catch (Exception $e) {
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} finally {
    // Close database connection if function exists
    if (function_exists('db_close')) {
        db_close();
    }
}

// End output buffering
ob_end_flush();
?>
