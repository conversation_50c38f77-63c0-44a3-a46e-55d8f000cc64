<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = 0;
$name = $description = $sku = $category = '';
$price = $cost = $quantity = 0;
$error = $success = '';

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get inventory data
    $result = db_query("SELECT * FROM inventory WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $item = db_fetch_assoc($result);

        $name = $item['name'];
        $description = $item['description'];
        $sku = $item['sku'];
        $price = $item['price'];
        $cost = $item['cost'];
        $quantity = $item['quantity'];
        $category = $item['category'];
    } else {
        $error = 'Produto não encontrado.';
    }
} else {
    $error = 'ID do produto não fornecido.';
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $id = intval($_POST['id']);
    $name = isset($_POST['name']) ? $_POST['name'] : '';
    $description = isset($_POST['description']) ? $_POST['description'] : '';
    $sku = isset($_POST['sku']) ? $_POST['sku'] : '';
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    $cost = isset($_POST['cost']) ? floatval($_POST['cost']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 0;
    $category = isset($_POST['category']) ? $_POST['category'] : '';

    // Validate form data
    if (empty($name)) {
        $error = 'O nome do produto é obrigatório.';
    } elseif ($price <= 0) {
        $error = 'O preço deve ser maior que zero.';
    } else {
        // Escape all input values to prevent SQL injection
        $name = db_escape($name);
        $description = db_escape($description);
        $sku = db_escape($sku);
        $category = db_escape($category);

        // Create and execute query
        $query = "UPDATE inventory SET
                  name = '$name',
                  description = '$description',
                  sku = '$sku',
                  price = $price,
                  cost = $cost,
                  quantity = $quantity,
                  category = '$category'
                  WHERE id = $id";

        if (db_query($query)) {
            $success = 'Produto atualizado com sucesso!';
        } else {
            $error = 'Erro ao atualizar produto: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Produto - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Editar Produto</h1>
                    <a href="inventory.php" class="btn">Voltar para Lista</a>
                </div>
                <div class="card-body">
                    <?php if ($id > 0 && empty($error)): ?>
                        <form method="POST" action="inventory_edit.php" class="needs-validation">
                            <input type="hidden" name="id" value="<?php echo $id; ?>">

                            <div class="form-group">
                                <label for="name">Nome *</label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="description">Descrição</label>
                                <textarea id="description" name="description"><?php echo htmlspecialchars($description); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="sku">SKU / Código</label>
                                <input type="text" id="sku" name="sku" value="<?php echo htmlspecialchars($sku); ?>">
                            </div>

                            <div class="form-group">
                                <label for="price">Preço de Venda *</label>
                                <input type="number" id="price" name="price" step="0.01" min="0" value="<?php echo $price; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="cost">Custo</label>
                                <input type="number" id="cost" name="cost" step="0.01" min="0" value="<?php echo $cost; ?>">
                            </div>

                            <div class="form-group">
                                <label for="quantity">Quantidade em Estoque</label>
                                <input type="number" id="quantity" name="quantity" step="1" min="0" value="<?php echo $quantity; ?>">
                            </div>

                            <div class="form-group">
                                <label for="category">Categoria</label>
                                <input type="text" id="category" name="category" value="<?php echo htmlspecialchars($category); ?>">
                            </div>

                            <button type="submit" class="btn">Atualizar Produto</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
