<?php
// Start session
session_start();

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = 0;
$error = $success = '';
$customer = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    
    // Get customer data
    $result = db_query("SELECT * FROM customers WHERE id = $id");
    
    if ($result && db_num_rows($result) > 0) {
        $customer = db_fetch_assoc($result);
    } else {
        $error = 'Cliente não encontrado.';
    }
} else {
    $error = 'ID do cliente não fornecido.';
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['confirm_delete'])) {
    $id = intval($_POST['id']);
    
    // Check if customer has related quotes or invoices
    $result = db_query("SELECT COUNT(*) as count FROM quotes WHERE customer_id = $id");
    $row = db_fetch_assoc($result);
    $quotes_count = $row['count'];
    
    $result = db_query("SELECT COUNT(*) as count FROM invoices WHERE customer_id = $id");
    $row = db_fetch_assoc($result);
    $invoices_count = $row['count'];
    
    if ($quotes_count > 0 || $invoices_count > 0) {
        $error = 'Não é possível excluir este cliente pois existem orçamentos ou faturas associados a ele.';
    } else {
        // Delete customer
        if (db_query("DELETE FROM customers WHERE id = $id")) {
            $success = 'Cliente excluído com sucesso!';
            $customer = null; // Clear customer data
        } else {
            $error = 'Erro ao excluir cliente: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excluir Cliente - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tonys AC Repair</div>
                <ul class="nav-links">
                    <li><a href="index.php">Início</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                    <p><a href="customers.php" class="btn">Voltar para Lista de Clientes</a></p>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">Excluir Cliente</h1>
                        <a href="customers.php" class="btn">Voltar para Lista</a>
                    </div>
                    <div class="card-body">
                        <?php if ($customer): ?>
                            <div class="alert alert-danger">
                                <p><strong>Atenção!</strong> Você está prestes a excluir o cliente abaixo. Esta ação não pode ser desfeita.</p>
                            </div>
                            
                            <div class="customer-details">
                                <p><strong>Nome:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                                <p><strong>Telefone:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                                <p><strong>Endereço:</strong> <?php echo htmlspecialchars($customer['address']); ?></p>
                                <p><strong>Cidade:</strong> <?php echo htmlspecialchars($customer['city']); ?></p>
                                <p><strong>Estado:</strong> <?php echo htmlspecialchars($customer['state']); ?></p>
                                <p><strong>CEP:</strong> <?php echo htmlspecialchars($customer['zip']); ?></p>
                            </div>
                            
                            <form method="POST" action="customer_delete.php">
                                <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <button type="submit" name="confirm_delete" class="btn btn-danger">Confirmar Exclusão</button>
                                <a href="customers.php" class="btn">Cancelar</a>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
