<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Habilitar exibição de erros para debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Função para registrar ações no log
function log_action($message, $level = 'info') {
    try {
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'];

        // Verificar se o diretório de logs existe
        $log_dir = __DIR__ . '/logs';
        if (!is_dir($log_dir)) {
            if (!mkdir($log_dir, 0755, true)) {
                error_log("Não foi possível criar o diretório de logs: $log_dir");
            }
        }

        // Salvar em arquivo para backup
        if (is_dir($log_dir) && is_writable($log_dir)) {
            $log_file = $log_dir . '/system.log';
            $log_entry = json_encode([
                'timestamp' => $timestamp,
                'user_id' => $user_id,
                'ip' => $ip,
                'level' => $level,
                'message' => $message
            ]);
            $log_text = $log_entry . "\n";
            file_put_contents($log_file, $log_text, FILE_APPEND);
        }
    } catch (Exception $e) {
        // Falha silenciosa, apenas registra o erro no log do PHP
        error_log("Erro ao registrar log: " . $e->getMessage());
    }
}

// Função para formatar valor em reais
function format_money($value) {
    return number_format($value, 2, ',', '.');
}

// Função para formatar data
function format_date($date) {
    return date('d/m/Y', strtotime($date));
}

// Função para obter o status da fatura traduzido
function get_invoice_status($status) {
    // Padronizar o status para valores em inglês
    $normalized_status = strtolower(trim($status));

    // Mapeamento de valores antigos para novos (padronizados)
    $status_mapping = [
        'pending' => 'unpaid',
        'pago' => 'paid',
        'pendente' => 'unpaid',
        'parcialmente pago' => 'partial',
        'cancelado' => 'cancelled',
        'vencido' => 'overdue'
    ];

    // Normalizar o status se for um dos valores antigos
    if (isset($status_mapping[$normalized_status])) {
        $normalized_status = $status_mapping[$normalized_status];
    }

    // Traduções para exibição
    $status_labels = [
        'unpaid' => 'Unpaid',
        'partial' => 'Partially Paid',
        'paid' => 'Paid',
        'cancelled' => 'Cancelled',
        'overdue' => 'Overdue'
    ];

    return isset($status_labels[$normalized_status]) ? $status_labels[$normalized_status] : ucfirst($normalized_status);
}

// Verificar se a tabela invoice_payments existe
try {
    $result = db_query("SHOW TABLES LIKE 'invoice_payments'");
    if (db_num_rows($result) == 0) {
        // Criar tabela se não existir
        $create_table = "CREATE TABLE IF NOT EXISTS invoice_payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_id INT NOT NULL,
            payment_date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
        )";
        if (!db_query($create_table)) {
            throw new Exception('Erro ao criar tabela invoice_payments: ' . db_error());
        }
    }
} catch (Exception $e) {
    // Registrar erro, mas continuar a execução
    error_log('Erro na inicialização: ' . $e->getMessage());
}

$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = $success = '';
$invoice = null;

// Buscar dados da fatura
$payments = [];
$total_paid = 0;
$remaining = 0;

if ($id > 0) {
    try {
        $result = db_query("SELECT i.*, c.name as customer_name
                            FROM invoices i
                            LEFT JOIN customers c ON i.customer_id = c.id
                            WHERE i.id = $id");

        if (!$result) {
            throw new Exception('Erro ao buscar fatura: ' . db_error());
        }

        if (db_num_rows($result) > 0) {
            $invoice = db_fetch_assoc($result);

            // Verificar se a tabela invoice_payments existe antes de consultar
            $check_table = db_query("SHOW TABLES LIKE 'invoice_payments'");
            if (db_num_rows($check_table) > 0) {
                // Buscar pagamentos
                $payments_result = db_query("SELECT * FROM invoice_payments WHERE invoice_id = $id ORDER BY payment_date DESC");

                if (!$payments_result) {
                    throw new Exception('Erro ao buscar pagamentos: ' . db_error());
                }

                while ($payment = db_fetch_assoc($payments_result)) {
                    $payments[] = $payment;
                    $total_paid += $payment['amount'];
                }
            }

            $remaining = $invoice['total'] - $total_paid;
        } else {
            $error = 'Fatura não encontrada.';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        error_log('Erro ao buscar fatura: ' . $e->getMessage());
    }
}

// Processar pagamento
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $invoice) {
    $payment_date = isset($_POST['payment_date']) ? $_POST['payment_date'] : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $payment_method = isset($_POST['payment_method']) ? $_POST['payment_method'] : '';
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';

    // Validações
    if (empty($payment_date)) {
        $error = 'A data do pagamento é obrigatória.';
    } elseif ($amount <= 0) {
        $error = 'O valor do pagamento deve ser maior que zero.';
    } elseif ($amount > $remaining) {
        $error = 'O valor do pagamento não pode ser maior que o valor restante.';
    } elseif (empty($payment_method)) {
        $error = 'O método de pagamento é obrigatório.';
    } else {
        // Iniciar transação
        try {
            db_begin_transaction();

            // Verificar novamente se a tabela existe
            $check_table = db_query("SHOW TABLES LIKE 'invoice_payments'");
            if (db_num_rows($check_table) == 0) {
                throw new Exception('A tabela invoice_payments não existe. Por favor, atualize a página.');
            }

            // Log antes da operação
            log_action("Iniciando registro de pagamento - Fatura #$id - Valor: $amount");

            // Inserir pagamento
            $payment_date = db_escape($payment_date);
            $payment_method = db_escape($payment_method);
            $notes = db_escape($notes);

            $query = "INSERT INTO invoice_payments (invoice_id, payment_date, amount, payment_method, notes)
                     VALUES ($id, '$payment_date', $amount, '$payment_method', '$notes')";

            if (!db_query($query)) {
                throw new Exception('Erro ao registrar pagamento: ' . db_error());
            }

            // Verificar se o pagamento foi inserido corretamente
            $payment_id = db_insert_id();
            if (!$payment_id) {
                throw new Exception('Erro ao obter ID do pagamento inserido');
            }

            // Log após inserção do pagamento
            log_action("Pagamento #$payment_id registrado com sucesso");

            // Atualizar status e valores da fatura
            $new_total_paid = $total_paid + $amount;
            $new_remaining = $invoice['total'] - $new_total_paid;

            // Determinar o status com base no saldo restante
            if (abs($new_remaining) < 0.01) { // Se o saldo for praticamente zero
                $status = 'paid';
            } elseif (abs($new_remaining - $invoice['total']) < 0.01) { // Se o saldo for praticamente igual ao total
                $status = 'unpaid';
            } else { // Se o saldo for diferente do total e maior que zero
                $status = 'partial';
            }

            // Adicionar campo paid_amount se não existir
            $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'paid_amount'");
            if (db_num_rows($check_column) == 0) {
                db_query("ALTER TABLE invoices ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0");
            }

            // Adicionar campo remaining_amount se não existir
            $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'remaining_amount'");
            if (db_num_rows($check_column) == 0) {
                db_query("ALTER TABLE invoices ADD COLUMN remaining_amount DECIMAL(10,2) DEFAULT 0");
            }

            // Atualizar a fatura com os novos valores
            $query = "UPDATE invoices SET
                      status = '$status',
                      paid_amount = $new_total_paid,
                      remaining_amount = $new_remaining
                      WHERE id = $id";

            if (!db_query($query)) {
                throw new Exception('Erro ao atualizar status e valores da fatura: ' . db_error());
            }

            // Log após atualização do status e valores
            log_action("Fatura #$id atualizada: Status=$status, Pago=$new_total_paid, Restante=$new_remaining");

            // Commit da transação
            db_commit();

            // Log após commit
            log_action("Transação completada com sucesso");

            $success = 'Pagamento registrado com sucesso!';

            // Adicionar detalhes do sucesso à sessão
            $_SESSION['last_operation'] = [
                'type' => 'payment',
                'invoice_id' => $id,
                'payment_id' => $payment_id,
                'amount' => $amount,
                'timestamp' => time()
            ];

            // Redirecionar para evitar reenvio do formulário
            header("Location: invoice_payment.php?id=$id&success=1");
            exit;

        } catch (Exception $e) {
            db_rollback();
            log_action("ERRO: " . $e->getMessage(), 'error');
            $error = $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento de Fatura - Tony's AC Repair</title>
    <style>
        /* Estilos para a página de pagamento de faturas */
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }

        .invoice-payment-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }

        .invoice-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .invoice-header h1 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 24px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .invoice-details {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .detail-card {
            flex: 1;
            min-width: 250px;
            padding: 15px;
            margin: 0 10px 10px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 3px solid #3498db;
        }

        .detail-card h3 {
            margin-top: 0;
            color: #3498db;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-weight: bold;
        }

        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }

        .status-unpaid {
            background-color: #f8d7da;
            color: #721c24;
        }

        .status-partial {
            background-color: #fff3cd;
            color: #856404;
        }

        .payment-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .payment-form h2 {
            margin-top: 0;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-grid {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }

        .form-grid .form-group {
            flex: 1;
            min-width: 200px;
            padding: 0 10px;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        .payments-history {
            margin-top: 30px;
        }

        .payments-history h2 {
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .payments-table {
            width: 100%;
            border-collapse: collapse;
        }

        .payments-table th {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #ddd;
        }

        .payments-table td {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .payments-table tr:hover {
            background-color: #f9f9f9;
        }

        .table-responsive {
            overflow-x: auto;
        }
    </style>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <!-- Menu lateral removido -->
    <div class="container-fluid">
        <!-- Content -->
            <div class="container">
        <?php if ($invoice): ?>
            <div class="invoice-header" style="display: flex; justify-content: space-between; align-items: flex-start;">
                <div>
                    <h1>Fatura #<?php echo htmlspecialchars($invoice['invoice_number']); ?></h1>
                    <p>Cliente: <?php echo htmlspecialchars($invoice['customer_name']); ?></p>
                </div>
                <div>
                    <a href="generate_invoice_pdf.php?id=<?php echo $id; ?>" class="btn btn-secondary" style="margin-top: 5px;" target="_blank">Voltar para Fatura</a>
                </div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($success || isset($_GET['success'])): ?>
                <div class="alert alert-success">
                    <?php echo $success ?: 'Pagamento registrado com sucesso!'; ?>
                </div>
            <?php endif; ?>

            <div class="invoice-details">
                <div class="detail-card">
                    <h3>Detalhes da Fatura</h3>
                    <p>Data: <?php echo format_date($invoice['invoice_date']); ?></p>
                    <p>Valor Total: R$ <?php echo format_money($invoice['total']); ?></p>
                    <p>Status: <span class="status-badge status-<?php echo $invoice['status']; ?>">
                        <?php echo get_invoice_status($invoice['status']); ?>
                    </span></p>
                </div>

                <div class="detail-card">
                    <h3>Resumo de Pagamentos</h3>
                    <p>Valor Pago: R$ <?php echo format_money($total_paid); ?></p>
                    <p>Valor Restante: R$ <?php echo format_money($remaining); ?></p>
                </div>
            </div>

            <?php if ($remaining > 0): ?>
                <div class="payment-form">
                    <h2>Registrar Novo Pagamento</h2>
                    <form method="POST" action="">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label" for="payment_date">Data do Pagamento</label>
                                <input type="date" id="payment_date" name="payment_date" class="form-control"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="amount">Valor</label>
                                <input type="number" id="amount" name="amount" class="form-control"
                                       step="0.01" max="<?php echo $remaining; ?>" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="payment_method">Método de Pagamento</label>
                                <select id="payment_method" name="payment_method" class="form-control" required>
                                    <option value="">Selecione...</option>
                                    <option value="zelle">Zelle</option>
                                    <option value="card">Card</option>
                                    <option value="cash">Cash</option>
                                    <option value="check">Check</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="notes">Observações</label>
                            <textarea id="notes" name="notes" class="form-control" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">Registrar Pagamento</button>
                        <a href="invoice_view.php?id=<?php echo $id; ?>" class="btn btn-secondary">Voltar para Fatura</a>
                    </form>
                </div>
            <?php endif; ?>

            <div class="payments-history">
                <h2>Histórico de Pagamentos</h2>
                <?php if (!empty($payments)): ?>
                    <div class="table-responsive">
                        <table class="payments-table">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Valor</th>
                                    <th>Método</th>
                                    <th>Observações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payments as $payment): ?>
                                    <tr>
                                        <td><?php echo format_date($payment['payment_date']); ?></td>
                                        <td>R$ <?php echo format_money($payment['amount']); ?></td>
                                        <td><?php echo htmlspecialchars(ucfirst($payment['payment_method'])); ?></td>
                                        <td><?php echo htmlspecialchars($payment['notes']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p>Nenhum pagamento registrado.</p>
                <?php endif; ?>
            </div>

        <?php else: ?>
            <div class="alert alert-danger">Fatura não encontrada.</div>
            <div style="text-align: center; margin-top: 20px;">
                <a href="invoices_list.php" class="btn btn-secondary">Voltar para Lista de Faturas</a>
            </div>
        <?php endif; ?>
    </div>

    <?php if ($invoice): ?>
    <div style="text-align: center; margin-top: 30px;">
        <a href="invoices_list.php" class="btn btn-secondary">Voltar para Lista de Faturas</a>
        </div>
    <?php endif; ?>
            </div>
    </div>

    <footer style="background-color: #f8f9fa; padding: 20px 0; text-align: center; margin-top: 30px; border-top: 1px solid #eee;">
        <div style="max-width: 1000px; margin: 0 auto;">
            <p style="margin: 0; color: #777;">&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>