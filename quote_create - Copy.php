<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$customer_id = 0;
$quote_date = date('Y-m-d');
$valid_until = date('Y-m-d', strtotime('+30 days'));
$notes = '';
$subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
$error = $success = '';
$items = [];

// Get all customers for dropdown
$customers = [];
$result = db_query("SELECT id, name FROM customers ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}

// Get all inventory items for dropdown
$inventory = [];
$result = db_query("SELECT id, name, price, sku FROM inventory ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $inventory = db_fetch_all($result);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $customer_id = intval(isset($_POST['customer_id']) ? $_POST['customer_id'] : 0);
    $quote_date = isset($_POST['quote_date']) ? $_POST['quote_date'] : date('Y-m-d');
    $valid_until = isset($_POST['valid_until']) ? $_POST['valid_until'] : '';
    $subtotal = floatval(isset($_POST['subtotal']) ? $_POST['subtotal'] : 0);
    $tax_rate = floatval(isset($_POST['tax_rate']) ? $_POST['tax_rate'] : 0);
    $tax_amount = floatval(isset($_POST['tax_amount']) ? $_POST['tax_amount'] : 0);
    $discount_amount = floatval(isset($_POST['discount_amount']) ? $_POST['discount_amount'] : 0);
    $total = floatval(isset($_POST['total']) ? $_POST['total'] : 0);
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';

    // Get items
    $item_ids = isset($_POST['item_id']) ? $_POST['item_id'] : [];
    $item_descriptions = isset($_POST['item_description']) ? $_POST['item_description'] : [];
    $item_quantities = isset($_POST['item_quantity']) ? $_POST['item_quantity'] : [];
    $item_prices = isset($_POST['item_price']) ? $_POST['item_price'] : [];
    $item_subtotals = isset($_POST['item_subtotal']) ? $_POST['item_subtotal'] : [];

    // Build items array
    for ($i = 0; $i < count($item_ids); $i++) {
        if (!empty($item_ids[$i])) {
            $items[] = [
                'id' => intval($item_ids[$i]),
                'description' => $item_descriptions[$i],
                'quantity' => floatval($item_quantities[$i]),
                'price' => floatval($item_prices[$i]),
                'subtotal' => floatval($item_subtotals[$i])
            ];
        }
    }

    // Validate form data
    if ($customer_id <= 0) {
        $error = 'Selecione um cliente.';
    } elseif (empty($quote_date)) {
        $error = 'A data do orçamento é obrigatória.';
    } elseif (empty($items)) {
        $error = 'Adicione pelo menos um item ao orçamento.';
    } else {
        // We'll use manual transaction management
        db_begin_transaction();
        $transaction_success = true;

        // Generate quote number
        $year = date('Y');
        $month = date('m');
        $result = db_query("SELECT MAX(CAST(SUBSTRING_INDEX(quote_number, '-', -1) AS UNSIGNED)) as max_number FROM quotes WHERE quote_number LIKE 'ORC-$year$month-%'");
        $row = db_fetch_assoc($result);
        $next_number = 1;

        if ($row && isset($row['max_number']) && $row['max_number']) {
            $next_number = $row['max_number'] + 1;
        }

        $quote_number = sprintf("ORC-%s%s-%03d", $year, $month, $next_number);

        // Escape strings to prevent SQL injection
        $quote_number = db_escape($quote_number);
        $quote_date = db_escape($quote_date);
        $valid_until = db_escape($valid_until);
        $notes = db_escape($notes);

        // Insert quote
        $query = "INSERT INTO quotes (customer_id, quote_number, quote_date, valid_until, subtotal, tax_rate, tax_amount, discount_amount, total, notes, status) "
               . "VALUES ($customer_id, '$quote_number', '$quote_date', " . (!empty($valid_until) ? "'$valid_until'" : "NULL") . ", $subtotal, $tax_rate, $tax_amount, $discount_amount, $total, '$notes', 'pending')";

        if (db_query($query)) {
            $quote_id = db_insert_id();

            // Insert quote items
            foreach ($items as $item) {
                $inventory_id = $item['id'];
                $description = db_escape($item['description']);
                $quantity = $item['quantity'];
                $price = $item['price'];
                $subtotal = $item['subtotal'];

                $query = "INSERT INTO quote_items (quote_id, inventory_id, description, quantity, price, subtotal) "
                       . "VALUES ($quote_id, $inventory_id, '$description', $quantity, $price, $subtotal)";

                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao inserir item do orçamento: ' . db_error();
                    break;
                }
            }

            if ($transaction_success) {
                db_commit();
                $success = 'Orçamento criado com sucesso! Número: ' . $quote_number;

                // Clear form data
                $customer_id = 0;
                $quote_date = date('Y-m-d');
                $valid_until = date('Y-m-d', strtotime('+30 days'));
                $notes = '';
                $subtotal = $tax_rate = $tax_amount = $discount_amount = $total = 0;
                $items = [];
            } else {
                db_rollback();
            }
        } else {
            db_rollback();
            $error = 'Erro ao criar orçamento: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Orçamento - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Novo Orçamento</h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="quote_create.php" class="needs-validation" id="quote-form">
                        <div class="form-group">
                            <label for="customer_id">Cliente *</label>
                            <select id="customer_id" name="customer_id" required>
                                <option value="">Selecione um cliente</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo ($customer_id == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quote_date">Data do Orçamento *</label>
                            <input type="date" id="quote_date" name="quote_date" value="<?php echo $quote_date; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="valid_until">Válido Até</label>
                            <input type="date" id="valid_until" name="valid_until" value="<?php echo $valid_until; ?>">
                        </div>

                        <h3>Itens do Orçamento</h3>

                        <div class="items-container" id="items-container">
                            <!-- Items will be added here -->
                        </div>

                        <button type="button" class="btn btn-secondary" onclick="addItemRow()">Adicionar Item</button>

                        <div class="totals-section">
                            <div class="totals-row">
                                <span class="totals-label">Subtotal:</span>
                                <input type="number" id="subtotal" name="subtotal" step="0.01" min="0" value="<?php echo $subtotal; ?>" readonly>
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Taxa (%):</span>
                                <input type="number" id="tax_rate" name="tax_rate" step="0.01" min="0" value="<?php echo $tax_rate; ?>" onchange="updateTaxAmount()">
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Valor da Taxa:</span>
                                <input type="number" id="tax_amount" name="tax_amount" step="0.01" min="0" value="<?php echo $tax_amount; ?>" readonly>
                            </div>

                            <div class="totals-row">
                                <span class="totals-label">Desconto:</span>
                                <input type="number" id="discount_amount" name="discount_amount" step="0.01" min="0" value="<?php echo $discount_amount; ?>" onchange="calculateTotals()">
                            </div>

                            <div class="totals-row grand-total">
                                <span class="totals-label">Total:</span>
                                <input type="number" id="total" name="total" step="0.01" min="0" value="<?php echo $total; ?>" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Observações</label>
                            <textarea id="notes" name="notes"><?php echo htmlspecialchars($notes); ?></textarea>
                        </div>

                        <button type="submit" class="btn">Criar Orçamento</button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <!-- Item template -->
    <template id="item-template">
        <div class="item-row">
            <div class="form-group">
                <label>Produto</label>
                <select name="item_id[]" class="item-select" required onchange="updateItemDetails(this)">
                    <option value="">Selecione um produto</option>
                    <?php foreach ($inventory as $item): ?>
                        <option value="<?php echo $item['id']; ?>" data-price="<?php echo $item['price']; ?>">
                            <?php echo htmlspecialchars($item['name']); ?> <?php echo !empty($item['sku']) ? '(' . htmlspecialchars($item['sku']) . ')' : ''; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label>Descrição</label>
                <input type="text" name="item_description[]" class="item-description">
            </div>

            <div class="form-group">
                <label>Quantidade</label>
                <input type="number" name="item_quantity[]" class="item-quantity" step="0.001" min="0.001" value="1" required onchange="calculateItemTotal(this)" placeholder="Ex: 5.33">
            </div>

            <div class="form-group">
                <label>Preço Unitário</label>
                <input type="number" name="item_price[]" class="item-price" step="0.01" min="0" required onchange="calculateItemTotal(this)">
            </div>

            <div class="form-group">
                <label>Subtotal</label>
                <input type="number" name="item_subtotal[]" class="item-subtotal" step="0.01" min="0" readonly>
            </div>

            <button type="button" class="btn btn-danger remove-item" onclick="removeItem(this)">Remover</button>
        </div>
    </template>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
