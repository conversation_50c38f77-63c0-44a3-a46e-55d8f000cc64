<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Only POST requests are allowed.']);
    exit;
}

// Get POST data
$invoice_id = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;
$confirm = isset($_POST['confirm']) ? $_POST['confirm'] : '';

// Validate input
if ($invoice_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid invoice ID.']);
    exit;
}

if ($confirm !== 'yes') {
    echo json_encode(['success' => false, 'message' => 'Confirmation required.']);
    exit;
}

// Check if invoice exists
$result = db_query("SELECT id, invoice_number FROM invoices WHERE id = $invoice_id");
if (!$result || db_num_rows($result) === 0) {
    echo json_encode(['success' => false, 'message' => 'Invoice not found.']);
    exit;
}

$invoice = db_fetch_assoc($result);

try {
    // Start transaction
    db_begin_transaction();
    $transaction_success = true;
    $error_message = '';

    // Delete invoice payments first (if any)
    if (!db_query("DELETE FROM invoice_payments WHERE invoice_id = $invoice_id")) {
        $transaction_success = false;
        $error_message = 'Error deleting invoice payments: ' . db_error();
    }

    // Delete invoice items
    if ($transaction_success) {
        if (!db_query("DELETE FROM invoice_items WHERE invoice_id = $invoice_id")) {
            $transaction_success = false;
            $error_message = 'Error deleting invoice items: ' . db_error();
        }
    }

    // Delete invoice
    if ($transaction_success) {
        if (!db_query("DELETE FROM invoices WHERE id = $invoice_id")) {
            $transaction_success = false;
            $error_message = 'Error deleting invoice: ' . db_error();
        }
    }

    // Commit or rollback transaction
    if ($transaction_success) {
        db_commit();
        echo json_encode([
            'success' => true, 
            'message' => 'Invoice deleted successfully.',
            'invoice_number' => $invoice['invoice_number']
        ]);
    } else {
        db_rollback();
        echo json_encode(['success' => false, 'message' => $error_message]);
    }

} catch (Exception $e) {
    // Rollback transaction on exception
    db_rollback();
    echo json_encode(['success' => false, 'message' => 'Error processing deletion: ' . $e->getMessage()]);
}

// Close database connection
db_close();
?>
