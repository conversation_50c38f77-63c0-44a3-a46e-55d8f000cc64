<?php
// Incluir conexão com o banco de dados
require_once '../db_connect.php';

// Definir cabeçalho JSON
header('Content-Type: application/json');

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método não permitido']);
    exit;
}

// Obter dados do formulário
$name = trim($_POST['name'] ?? '');
$email = trim($_POST['email'] ?? '');
$phone = trim($_POST['phone'] ?? '');
$address = trim($_POST['address'] ?? '');
$city = trim($_POST['city'] ?? '');
$state = trim($_POST['state'] ?? '');
$zip = trim($_POST['zip'] ?? '');
$notes = trim($_POST['notes'] ?? '');

// Validar dados obrigatórios
if (empty($name)) {
    echo json_encode(['error' => 'Nome é obrigatório']);
    exit;
}

// Escapar dados para evitar SQL injection
$name_escaped = db_escape($name);
$email_escaped = db_escape($email);
$phone_escaped = db_escape($phone);
$address_escaped = db_escape($address);
$city_escaped = db_escape($city);
$state_escaped = db_escape($state);
$zip_escaped = db_escape($zip);
$notes_escaped = db_escape($notes);

// Inserir novo cliente no banco de dados
$query = "INSERT INTO customers (name, email, phone, address, city, state, zip, notes, created_at, updated_at) 
          VALUES ('$name_escaped', '$email_escaped', '$phone_escaped', '$address_escaped', 
                  '$city_escaped', '$state_escaped', '$zip_escaped', '$notes_escaped', NOW(), NOW())";

if (db_query($query)) {
    $customer_id = db_insert_id();
    
    // Retornar sucesso com dados do cliente criado
    echo json_encode([
        'success' => true,
        'customer' => [
            'id' => $customer_id,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'address' => $address
        ]
    ]);
} else {
    // Retornar erro
    echo json_encode(['error' => 'Erro ao criar cliente: ' . db_error()]);
}

// Fechar conexão
db_close();
?>
