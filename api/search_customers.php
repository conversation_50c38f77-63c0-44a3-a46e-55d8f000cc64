<?php
// Incluir conexão com o banco de dados
require_once '../db_connect.php';

// Definir cabeçalho JSON
header('Content-Type: application/json');

// Verificar se há termo de busca
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

if (empty($search)) {
    echo json_encode([]);
    exit;
}

// Escapar o termo de busca para evitar SQL injection
$search_escaped = db_escape($search);

// Buscar clientes que contenham o termo de busca no nome
$query = "SELECT id, name, phone, email, address FROM customers 
          WHERE name LIKE '%$search_escaped%' 
          ORDER BY name 
          LIMIT 10";

$result = db_query($query);
$customers = [];

if ($result && db_num_rows($result) > 0) {
    while ($row = db_fetch_assoc($result)) {
        $customers[] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'phone' => $row['phone'] ?? '',
            'email' => $row['email'] ?? '',
            'address' => $row['address'] ?? ''
        ];
    }
}

// Retornar resultados em JSON
echo json_encode($customers);

// Fechar conexão
db_close();
?>
