<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check if it exists
if (file_exists('check_session.php')) {
    require_once 'check_session.php';
}

// Include functions if they exist
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Initialize variables
$id = '';
$customer_id = '';
$invoice_number = '';
$customer_name = '';
$address = '';
$city = '';
$state = '';
$zip = '';
$error_message = '';
$success_message = '';

// Check if invoice ID is provided
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $id = $_GET['id'];

    // Get invoice details
    $query = "SELECT i.*, c.name AS customer_name, c.address, c.city, c.state, c.zip, c.id AS customer_id
              FROM invoices i
              LEFT JOIN customers c ON i.customer_id = c.id
              WHERE i.id = ?";

    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $invoice = $result->fetch_assoc();
        $invoice_number = $invoice['invoice_number'];
        $customer_name = $invoice['customer_name'];
        $customer_id = $invoice['customer_id'];
        $address = $invoice['address'];
        $city = $invoice['city'];
        $state = $invoice['state'];
        $zip = $invoice['zip'];
    } else {
        $error_message = 'Invoice not found.';
    }

    $stmt->close();
} else {
    $error_message = 'Invoice ID not provided.';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $customer_id = $_POST['customer_id'];
    $invoice_id = $_POST['invoice_id'];
    $address = trim($_POST['address']);
    $city = trim($_POST['city']);
    $state = trim($_POST['state']);
    $zip = trim($_POST['zip']);

    // Validate form data
    if (empty($address)) {
        $error_message = 'Address is required.';
    } else {
        // Update customer address in the database
        $query = "UPDATE customers SET address = ?, city = ?, state = ?, zip = ? WHERE id = ?";

        $stmt = $conn->prepare($query);
        $stmt->bind_param('ssssi', $address, $city, $state, $zip, $customer_id);

        if ($stmt->execute()) {
            $success_message = 'Customer address updated successfully.';

            // Redirect back to invoice view after successful update
            header("Location: invoice_view.php?id=$invoice_id&address_updated=1");
            exit;
        } else {
            $error_message = 'Error updating customer address: ' . $conn->error;
        }

        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Customer Address - Invoice #<?php echo htmlspecialchars($invoice_number); ?></title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"></head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">Edit Customer Address - Invoice #<?php echo htmlspecialchars($invoice_number); ?></h1>
                <div class="action-buttons">
                    <a href="invoice_view.php?id=<?php echo $id; ?>" class="btn btn-primary" title="Back to Invoice"><i class="fas fa-arrow-left"></i> Back to Invoice</a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <div class="address-form">
                    <h2>Customer: <?php echo htmlspecialchars($customer_name); ?></h2>

                    <form method="POST" action="invoice_edit_address.php?id=<?php echo $id; ?>">
                        <input type="hidden" name="customer_id" value="<?php echo $customer_id; ?>">
                        <input type="hidden" name="invoice_id" value="<?php echo $id; ?>">

                        <div class="form-group">
                            <label for="address">Address</label>
                            <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($address); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="city">City</label>
                            <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($city); ?>">
                        </div>

                        <div class="form-group">
                            <label for="state">State</label>
                            <input type="text" id="state" name="state" value="<?php echo htmlspecialchars($state); ?>">
                        </div>

                        <div class="form-group">
                            <label for="zip">ZIP Code</label>
                            <input type="text" id="zip" name="zip" value="<?php echo htmlspecialchars($zip); ?>">
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Save Changes</button>
                            <a href="invoice_view.php?id=<?php echo $id; ?>" class="btn btn-secondary"><i class="fas fa-times"></i> Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
$conn->close();
?>
