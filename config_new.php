<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Check if the users table exists
$table_exists = false;
$query = "SHOW TABLES LIKE 'users'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $table_exists = true;
}

// Check if the user_roles table exists
$roles_table_exists = false;
$query = "SHOW TABLES LIKE 'user_roles'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $roles_table_exists = true;
}

// Get list of users if table exists
$users = array();
if ($table_exists) {
    $query = "SELECT u.id, u.username, u.email, u.created_at, r.name as role_name 
              FROM users u 
              LEFT JOIN user_roles r ON u.role_id = r.id 
              ORDER BY u.username";
    $result = $conn->query($query);
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            array_push($users, $row);
        }
    }
}

// Get list of roles if table exists
$roles = array();
if ($roles_table_exists) {
    $query = "SELECT r.id, r.name, r.description, COUNT(u.id) as user_count 
              FROM user_roles r 
              LEFT JOIN users u ON r.id = u.role_id 
              GROUP BY r.id 
              ORDER BY r.name";
    $result = $conn->query($query);
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            array_push($roles, $row);
        }
    }
}

// Check if current user has permission to manage users
$can_manage_users = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['user_role'])) {
    if ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'administrator') {
        $can_manage_users = true;
    } else {
        // Check if user has the manage_users permission
        $query = "SELECT COUNT(*) as has_permission 
                  FROM role_permissions rp 
                  JOIN permissions p ON rp.permission_id = p.id 
                  JOIN user_roles r ON rp.role_id = r.id 
                  JOIN users u ON u.role_id = r.id 
                  WHERE u.id = ? AND p.name = 'manage_users'";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('i', $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result && $row = $result->fetch_assoc()) {
            $can_manage_users = ($row['has_permission'] > 0);
        }
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Configuration - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .config-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .config-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .config-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .config-header h2 {
            margin: 0;
            font-size: 1.25rem;
            color: #343a40;
        }

        .config-body {
            padding: 20px;
        }

        .config-section {
            margin-bottom: 30px;
        }

        .config-section:last-child {
            margin-bottom: 0;
        }

        .config-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #495057;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .user-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .user-table tr:last-child td {
            border-bottom: none;
        }

        .user-actions {
            display: flex;
            gap: 8px;
        }

        .user-actions a {
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.875rem;
            text-decoration: none;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .user-actions a i {
            margin-right: 5px;
        }

        .btn-edit {
            background-color: #6c757d;
        }

        .btn-delete {
            background-color: #dc3545;
        }

        .btn-add-user {
            background-color: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .btn-add-user i {
            margin-right: 8px;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .role-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-administrator {
            background-color: #dc3545;
            color: white;
        }

        .role-manager {
            background-color: #fd7e14;
            color: white;
        }

        .role-user {
            background-color: #6c757d;
            color: white;
        }

        .tab-container {
            margin-bottom: 20px;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 15px;
        }

        .tab-button {
            padding: 10px 15px;
            background: none;
            border: none;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-weight: 500;
            color: #6c757d;
            margin-right: 5px;
        }

        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .user-table {
                display: block;
                overflow-x: auto;
            }

            .config-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .config-header .btn-add-user {
                margin-top: 10px;
                align-self: flex-start;
            }

            .tab-buttons {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="config-container">
                <h1>System Configuration</h1>

                <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> User has been successfully deleted.
                    </div>
                <?php endif; ?>

                <?php if (!$table_exists || !$roles_table_exists): ?>
                    <div class="config-card">
                        <div class="config-header">
                            <h2>User Management</h2>
                        </div>
                        <div class="config-body">
                            <div class="config-section">
                                <div class="alert alert-danger">
                                    <strong>Warning:</strong> The required tables do not exist in the database. Please run the <a href="create_permissions_tables.php" style="color: #721c24; text-decoration: underline;">database setup script</a> to create them.
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="config-card">
                        <div class="config-header">
                            <h2>User Management</h2>
                            <?php if ($can_manage_users): ?>
                                <a href="user_create_new.php" class="btn-add-user"><i class="fas fa-user-plus"></i> Add New User</a>
                            <?php endif; ?>
                        </div>
                        <div class="config-body">
                            <div class="tab-container">
                                <div class="tab-buttons">
                                    <button class="tab-button active" data-tab="users-tab">Users</button>
                                    <button class="tab-button" data-tab="roles-tab">Roles</button>
                                </div>
                                
                                <div id="users-tab" class="tab-content active">
                                    <div class="config-section">
                                        <h3>Users</h3>

                                        <?php if (empty($users)): ?>
                                            <p>No users found.</p>
                                        <?php else: ?>
                                            <div class="table-responsive">
                                                <table class="user-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Username</th>
                                                            <th>Email</th>
                                                            <th>Role</th>
                                                            <th>Created</th>
                                                            <?php if ($can_manage_users): ?>
                                                                <th>Actions</th>
                                                            <?php endif; ?>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($users as $user): ?>
                                                            <tr>
                                                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                                <td>
                                                                    <?php if (!empty($user['role_name'])): ?>
                                                                        <span class="role-badge role-<?php echo strtolower($user['role_name']); ?>">
                                                                            <?php echo htmlspecialchars($user['role_name']); ?>
                                                                        </span>
                                                                    <?php else: ?>
                                                                        <span class="role-badge role-user">User</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td><?php echo isset($user['created_at']) ? date('m/d/Y', strtotime($user['created_at'])) : 'N/A'; ?></td>
                                                                <?php if ($can_manage_users): ?>
                                                                    <td>
                                                                        <div class="user-actions">
                                                                            <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn-edit"><i class="fas fa-edit"></i> Edit</a>
                                                                            <a href="user_delete.php?id=<?php echo $user['id']; ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this user?');"><i class="fas fa-trash-alt"></i> Delete</a>
                                                                        </div>
                                                                    </td>
                                                                <?php endif; ?>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div id="roles-tab" class="tab-content">
                                    <div class="config-section">
                                        <h3>User Roles</h3>

                                        <?php if (empty($roles)): ?>
                                            <p>No roles found.</p>
                                        <?php else: ?>
                                            <div class="table-responsive">
                                                <table class="user-table">
                                                    <thead>
                                                        <tr>
                                                            <th>Role Name</th>
                                                            <th>Description</th>
                                                            <th>Users</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($roles as $role): ?>
                                                            <tr>
                                                                <td>
                                                                    <span class="role-badge role-<?php echo strtolower($role['name']); ?>">
                                                                        <?php echo htmlspecialchars($role['name']); ?>
                                                                    </span>
                                                                </td>
                                                                <td><?php echo htmlspecialchars($role['description']); ?></td>
                                                                <td><?php echo $role['user_count']; ?></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding content
                    this.classList.add('active');
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });
    </script>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
