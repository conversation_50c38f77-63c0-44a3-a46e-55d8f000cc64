<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check if it exists
if (file_exists('check_session.php')) {
    require_once 'check_session.php';
}

// Include functions if they exist
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Check if user has admin privileges
// This is a simple check, you might want to implement a more robust role-based system
$is_admin = true; // For now, we'll assume all logged-in users are admins

// Check if the users table exists
$table_exists = false;
$query = "SHOW TABLES LIKE 'users'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $table_exists = true;
}

// Get list of users
$users = array();
if ($table_exists) {
    $query = "SELECT id, username, email, created_at FROM users ORDER BY username";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            array_push($users, $row);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .settings-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .settings-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .settings-header h2 {
            margin: 0;
            font-size: 1.25rem;
            color: #343a40;
        }

        .settings-body {
            padding: 20px;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .settings-section:last-child {
            margin-bottom: 0;
        }

        .settings-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #495057;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }

        .user-table {
            width: 100%;
            border-collapse: collapse;
        }

        .user-table th,
        .user-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .user-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .user-table tr:last-child td {
            border-bottom: none;
        }

        .user-actions {
            display: flex;
            gap: 8px;
        }

        .user-actions a {
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.875rem;
            text-decoration: none;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .user-actions a i {
            margin-right: 5px;
        }

        .btn-edit {
            background-color: #6c757d;
        }

        .btn-delete {
            background-color: #dc3545;
        }

        .btn-add-user {
            background-color: #28a745;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }

        .btn-add-user i {
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .user-table {
                display: block;
                overflow-x: auto;
            }

            .settings-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .settings-header .btn-add-user {
                margin-top: 10px;
                align-self: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="settings-container">
                <h1>System Settings</h1>

                <?php if (isset($_GET['deleted']) && $_GET['deleted'] == 1): ?>
                    <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #c3e6cb;">
                        <i class="fas fa-check-circle"></i> User has been successfully deleted.
                    </div>
                <?php endif; ?>

                <?php if (!$table_exists): ?>
                    <div class="settings-card">
                        <div class="settings-header">
                            <h2>User Management</h2>
                        </div>
                        <div class="settings-body">
                            <div class="settings-section">
                                <div style="padding: 15px; background-color: #f8d7da; color: #721c24; border-radius: 4px; margin-bottom: 20px;">
                                    <strong>Warning:</strong> The 'users' table does not exist in the database. Please run the <a href="create_users_table.php" style="color: #721c24; text-decoration: underline;">database setup script</a> to create it.
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="settings-card">
                        <div class="settings-header">
                            <h2>User Management</h2>
                            <a href="user_create.php" class="btn-add-user"><i class="fas fa-user-plus"></i> Add New User</a>
                        </div>
                        <div class="settings-body">
                            <div class="settings-section">
                                <h3>Users</h3>

                                <?php if (empty($users)): ?>
                                    <p>No users found.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="user-table">
                                            <thead>
                                                <tr>
                                                    <th>Username</th>
                                                    <th>Email</th>
                                                    <th>Created</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($users as $user): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                        <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                        <td><?php echo isset($user['created_at']) ? date('m/d/Y', strtotime($user['created_at'])) : 'N/A'; ?></td>
                                                        <td>
                                                            <div class="user-actions">
                                                                <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn-edit"><i class="fas fa-edit"></i> Edit</a>
                                                                <a href="user_delete.php?id=<?php echo $user['id']; ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this user?');"><i class="fas fa-trash-alt"></i> Delete</a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
$conn->close();
?>
