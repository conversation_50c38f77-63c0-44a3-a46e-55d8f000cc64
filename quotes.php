<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Get status filter if provided
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query based on filter
$query = "
    SELECT q.*, c.name as customer_name
    FROM quotes q
    JOIN customers c ON q.customer_id = c.id
";

if (!empty($status_filter)) {
    $status_filter = db_escape($status_filter);
    $query .= " WHERE q.status = '$status_filter'";
}

$query .= " ORDER BY q.quote_date DESC";

// Get all quotes
$quotes = [];
$result = db_query($query);

if ($result && db_num_rows($result) > 0) {
    $quotes = db_fetch_all($result);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quotes - <PERSON><PERSON> AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <style>
        .action-buttons {
            display: flex;
            gap: 5px;
            justify-content: flex-start;
            align-items: center;
            white-space: nowrap;
        }

        .action-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 13px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .action-btn i {
            margin-right: 4px;
        }

        .view-btn {
            background-color: #3498db;
            color: white;
        }

        .view-btn:hover {
            background-color: #2980b9;
        }

        .edit-btn {
            background-color: #f39c12;
            color: white;
        }

        .edit-btn:hover {
            background-color: #d35400;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }

        .invoice-btn {
            background-color: #27ae60;
            color: white;
        }

        .invoice-btn:hover {
            background-color: #2ecc71;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">
                        Orçamentos
                        <?php if (!empty($status_filter)): ?>
                            - <?php echo ucfirst($status_filter); ?>
                        <?php endif; ?>
                    </h1>
                    <a href="quote_create.php" class="btn" style="display: inline-flex; align-items: center;"><i class="fas fa-plus-circle" style="margin-right: 5px;"></i> New Quote</a>
                </div>
                <div class="card-body">
                    <div class="filter-options" style="display: flex; gap: 8px; margin-bottom: 15px;">
                        <a href="quotes.php" class="btn <?php echo empty($status_filter) ? 'active' : ''; ?>" style="display: inline-flex; align-items: center;"><i class="fas fa-list" style="margin-right: 5px;"></i> All</a>
                        <a href="quotes.php?status=pending" class="btn <?php echo $status_filter === 'pending' ? 'active' : ''; ?>" style="display: inline-flex; align-items: center;"><i class="fas fa-clock" style="margin-right: 5px;"></i> Pending</a>
                        <a href="quotes.php?status=approved" class="btn <?php echo $status_filter === 'approved' ? 'active' : ''; ?>" style="display: inline-flex; align-items: center;"><i class="fas fa-check-circle" style="margin-right: 5px;"></i> Approved</a>
                        <a href="quotes.php?status=rejected" class="btn <?php echo $status_filter === 'rejected' ? 'active' : ''; ?>" style="display: inline-flex; align-items: center;"><i class="fas fa-times-circle" style="margin-right: 5px;"></i> Rejected</a>
                        <a href="quotes.php?status=expired" class="btn <?php echo $status_filter === 'expired' ? 'active' : ''; ?>" style="display: inline-flex; align-items: center;"><i class="fas fa-calendar-times" style="margin-right: 5px;"></i> Expired</a>
                    </div>

                    <?php if (empty($quotes)): ?>
                        <p>No quotes found.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="quote_number">Number</th>
                                    <th data-sort="customer_name">Customer</th>
                                    <th data-sort="quote_date">Date</th>
                                    <th data-sort="total">Total</th>
                                    <th data-sort="status">Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($quotes as $quote): ?>
                                    <tr>
                                        <td data-column="quote_number"><?php echo htmlspecialchars($quote['quote_number']); ?></td>
                                        <td data-column="customer_name"><?php echo htmlspecialchars($quote['customer_name']); ?></td>
                                        <td data-column="quote_date"><?php echo date('d/m/Y', strtotime($quote['quote_date'])); ?></td>
                                        <td data-column="total">R$ <?php echo number_format($quote['total'], 2, ',', '.'); ?></td>
                                        <td data-column="status">
                                            <?php
                                            $status = '';
                                            switch ($quote['status']) {
                                                case 'pending':
                                                    $status = '<span class="status-pending">Pendente</span>';
                                                    break;
                                                case 'approved':
                                                    $status = '<span class="status-approved">Aprovado</span>';
                                                    break;
                                                case 'rejected':
                                                    $status = '<span class="status-rejected">Rejeitado</span>';
                                                    break;
                                                case 'expired':
                                                    $status = '<span class="status-expired">Expirado</span>';
                                                    break;
                                                default:
                                                    $status = $quote['status'];
                                            }
                                            echo $status;
                                            ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="quote_view.php?id=<?php echo $quote['id']; ?>" class="action-btn view-btn" title="View">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="quote_edit.php?id=<?php echo $quote['id']; ?>" class="action-btn edit-btn" title="Edit">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="shared_links.php?type=quote&item_id=<?php echo $quote['id']; ?>" class="action-btn share-btn" title="Share">
                                                    <i class="fas fa-share-alt"></i> Share
                                                </a>
                                                <a href="quote_delete.php?id=<?php echo $quote['id']; ?>" class="action-btn delete-btn" title="Delete">
                                                    <i class="fas fa-trash-alt"></i> Delete
                                                </a>
                                                <?php if ($quote['status'] === 'pending' || $quote['status'] === 'approved'): ?>
                                                    <a href="invoice_create.php?quote_id=<?php echo $quote['id']; ?>" class="action-btn invoice-btn" title="Create Invoice">
                                                        <i class="fas fa-file-invoice-dollar"></i> Create Invoice
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // Confirmação para excluir orçamento
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('.delete-btn');

            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm('Tem certeza que deseja excluir este orçamento?')) {
                        e.preventDefault();
                    }
                });
            });
        });
    </script>
    <script src="js/mobile-menu-fix-new.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
