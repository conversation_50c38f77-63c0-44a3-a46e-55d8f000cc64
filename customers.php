<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$name = $email = $phone = $address = $city = $state = $zip = $notes = '';
$error = $success = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $name = isset($_POST['name']) ? $_POST['name'] : '';
    $email = isset($_POST['email']) ? $_POST['email'] : '';
    $phone = isset($_POST['phone']) ? $_POST['phone'] : '';
    $address = isset($_POST['address']) ? $_POST['address'] : '';
    $city = isset($_POST['city']) ? $_POST['city'] : '';
    $state = isset($_POST['state']) ? $_POST['state'] : '';
    $zip = isset($_POST['zip']) ? $_POST['zip'] : '';
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';

    // Validate form data
    if (empty($name)) {
        $error = 'Customer name is required.';
    } else {
        // Escape all input values to prevent SQL injection
        $name = db_escape($name);
        $email = db_escape($email);
        $phone = db_escape($phone);
        $address = db_escape($address);
        $city = db_escape($city);
        $state = db_escape($state);
        $zip = db_escape($zip);
        $notes = db_escape($notes);

        // Create and execute query
        $query = "INSERT INTO customers (name, email, phone, address, city, state, zip, notes) "
               . "VALUES ('$name', '$email', '$phone', '$address', '$city', '$state', '$zip', '$notes')";

        if (db_query($query)) {
            $success = 'Customer registered successfully!';
            // Clear form data
            $name = $email = $phone = $address = $city = $state = $zip = $notes = '';
        } else {
            $error = 'Error registering customer: ' . db_error();
        }
    }
}

// Get all customers
$customers = [];
$result = db_query("SELECT * FROM customers ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .customer-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 24px;
            margin: 0;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }

        .form-col {
            flex: 1;
            padding: 0 10px;
            min-width: 200px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .form-actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #2980b9;
        }

        .required-field {
            color: #e74c3c;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Estilos específicos para esta página */
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="customer-form-container">
                <div class="form-header">
                    <h1><i class="fas fa-user-plus"></i> CUSTOMER REGISTRATION</h1>
                </div>

                <form method="POST" action="customers.php" class="needs-validation">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="name">Name <span class="required-field">*</span></label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>">
                            </div>

                            <div class="form-group">
                                <label for="phone">Phone</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>" placeholder="(*************">
                            </div>
                        </div>

                        <div class="form-col">
                            <div class="form-group">
                                <label for="address">Address</label>
                                <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($address); ?>">
                            </div>

                            <div class="form-group">
                                <label for="city">City</label>
                                <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($city); ?>">
                            </div>

                            <div class="form-row">
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="state">State</label>
                                        <input type="text" id="state" name="state" value="<?php echo htmlspecialchars($state); ?>">
                                    </div>
                                </div>
                                <div class="form-col">
                                    <div class="form-group">
                                        <label for="zip">ZIP Code</label>
                                        <input type="text" id="zip" name="zip" value="<?php echo htmlspecialchars($zip); ?>" placeholder="00000">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea id="notes" name="notes" placeholder="Additional information about the customer"><?php echo htmlspecialchars($notes); ?></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-register">
                            <i class="fas fa-user-plus"></i> Register Customer
                        </button>
                    </div>
                </form>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2 class="card-title"><i class="fas fa-users"></i> Customer List</h2>
                    <a href="#" class="btn btn-export" title="Export to Excel"><i class="fas fa-file-excel"></i> Export</a>
                </div>
                <div class="card-body">
                    <?php if (empty($customers)): ?>
                        <p>No customers registered yet.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="name"><i class="fas fa-user"></i> Name</th>
                                    <th data-sort="phone"><i class="fas fa-phone"></i> Phone</th>
                                    <th><i class="fas fa-cogs"></i> Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customers as $customer): ?>
                                    <tr>
                                        <td data-column="name"><?php echo htmlspecialchars($customer['name']); ?></td>
                                        <td data-column="phone"><?php echo htmlspecialchars($customer['phone']); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="customer_view.php?id=<?php echo $customer['id']; ?>" class="btn" title="View"><i class="fas fa-eye"></i></a>
                                                <a href="customer_edit.php?id=<?php echo $customer['id']; ?>" class="btn btn-secondary" title="Edit"><i class="fas fa-edit"></i></a>
                                                <a href="customer_delete.php?id=<?php echo $customer['id']; ?>" class="btn btn-danger delete-btn" title="Delete"><i class="fas fa-trash-alt"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <!-- <script src="js/mobile-menu-fix-new.js"></script> -->
</body>
</html>
<?php
// Close database connection
db_close();
?>
