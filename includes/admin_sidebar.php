<?php

// Determina qual página está ativa

$current_page = basename($_SERVER['PHP_SELF']);

?>

<!-- Admin Sidebar -->

<div class="admin-sidebar">

    <div class="admin-menu-items">

        <a href="dashboard_simple.php" class="admin-menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-home"></i>

            <span>Painel</span>

        </a>

        <a href="customers.php" class="admin-menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">

            <i class="fa fa-users"></i>

            <span>Clientes</span>

        </a>

        <a href="inventory.php" class="admin-menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">

            <i class="fa fa-box"></i>

            <span>Estoque</span>

        </a>

        <a href="quotes.php" class="admin-menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice"></i>

            <span>Orçamentos</span>

        </a>

        <a href="invoices_list.php" class="admin-menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice-dollar"></i>

            <span>Faturas</span>

        </a>

        <a href="service_calls.php" class="admin-menu-item <?php echo ($current_page == 'service_calls.php' || $current_page == 'service_call_create.php' || $current_page == 'service_call_view.php' || $current_page == 'service_call_edit.php' || $current_page == 'service_call_delete.php') ? 'active' : ''; ?>">

            <i class="fa fa-headset"></i>

            <span>Chamados</span>

        </a>

        <a href="shared_links.php" class="admin-menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">

            <i class="fa fa-share-alt"></i>

            <span>Links Compartilhados</span>

        </a>

        <a href="reports.php" class="admin-menu-item <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>">

            <i class="fa fa-chart-bar"></i>

            <span>Relatórios</span>

        </a>

        <a href="admin_users.php" class="admin-menu-item <?php echo ($current_page == 'admin_users.php') ? 'active' : ''; ?>">

            <i class="fa fa-users-cog"></i>

            <span>Gerenciar Usuários</span>

        </a>

        <a href="admin_roles.php" class="admin-menu-item <?php echo ($current_page == 'admin_roles.php') ? 'active' : ''; ?>">

            <i class="fa fa-user-tag"></i>

            <span>Permissões</span>

        </a>

        <a href="admin_manage_roles.php" class="admin-menu-item <?php echo ($current_page == 'admin_manage_roles.php') ? 'active' : ''; ?>">

            <i class="fa fa-user-shield"></i>

            <span>Atribuir Roles</span>

        </a>

        <a href="admin_view_permissions.php" class="admin-menu-item <?php echo ($current_page == 'admin_view_permissions.php') ? 'active' : ''; ?>">

            <i class="fa fa-key"></i>

            <span>Ver Permissões</span>

        </a>

        <a href="backup.php" class="admin-menu-item <?php echo ($current_page == 'backup.php') ? 'active' : ''; ?>">

            <i class="fa fa-database"></i>

            <span>Backup</span>

        </a>

        <a href="logout.php" class="admin-menu-item">

            <i class="fa fa-sign-out-alt"></i>

            <span>Sair</span>

        </a>

    </div>

</div>



<style>

    .admin-sidebar {

        width: 250px;

        background-color: #343a40;

        color: #fff;

        height: 100%;

        position: fixed;

        left: 0;

        top: 0;

        overflow-y: auto;

        z-index: 1000;

        padding-top: 60px; /* Espaço para o menu superior */

    }



    .admin-menu-items {

        display: flex;

        flex-direction: column;

        padding: 15px 0;

    }



    .admin-menu-item {

        display: flex;

        align-items: center;

        padding: 12px 20px;

        color: #fff;

        text-decoration: none;

        transition: background-color 0.3s;

    }



    .admin-menu-item:hover {

        background-color: #495057;

    }



    .admin-menu-item.active {

        background-color: #007bff;

    }



    .admin-menu-item i {

        margin-right: 10px;

        width: 20px;

        text-align: center;

    }



    /* Ajuste para o conteúdo principal */

    .admin-content {

        margin-left: 250px;

        padding: 20px;

        padding-top: 70px; /* Espaço para o menu superior */

    }



    /* Responsividade para dispositivos móveis */

    @media (max-width: 768px) {

        .admin-sidebar {

            width: 100%;

            height: auto;

            position: relative;

            padding-top: 0;

        }



        .admin-content {

            margin-left: 0;

            padding-top: 20px;

        }



        .admin-menu-item {

            padding: 10px 15px;

        }

    }

</style>

