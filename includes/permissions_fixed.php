<?php

/**

 * Check if the current user has a specific permission

 *

 * @param string $permission The permission name to check

 * @return bool True if the user has the permission, false otherwise

 */

function user_has_permission($permission) {

    // Administrator has all permissions

    if (isset($_SESSION['user_role']) && (strtolower($_SESSION['user_role']) === 'administrator' || strtolower($_SESSION['user_role']) === 'admin')) {

        return true;

    }

    

    // Manager has specific permissions

    if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'manager') {

        $manager_permissions = ['manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports'];

        return in_array($permission, $manager_permissions);

    }

    

    // Regular user has limited permissions

    if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'user') {

        $user_permissions = ['manage_customers', 'manage_quotes', 'manage_invoices'];

        return in_array($permission, $user_permissions);

    }

    

    // If we get here, the user doesn't have the permission

    return false;

}



/**

 * Check if the current user has a specific role

 *

 * @param string|array $roles The role(s) to check

 * @return bool True if the user has any of the roles, false otherwise

 */

function user_has_role($roles) {

    if (!isset($_SESSION['user_role'])) {

        return false;

    }



    $user_role = strtolower($_SESSION['user_role']);



    if (is_array($roles)) {

        // Converter todos os roles para minúsculas para comparação case-insensitive

        $roles_lower = array_map('strtolower', $roles);

        return in_array($user_role, $roles_lower);

    } else {

        return $user_role === strtolower($roles);

    }

}



/**

 * Get the current user's role

 *

 * @return string|null The user's role or null if not set

 */

function get_user_role() {

    return $_SESSION['user_role'] ?? null;

}



/**

 * Get the current user's permissions

 *

 * @return array The user's permissions or empty array if not set

 */

function get_user_permissions() {

    $role = strtolower($_SESSION['user_role'] ?? '');

    

    if ($role === 'administrator' || $role === 'admin') {

        return ['manage_users', 'manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports', 'system_settings'];

    } else if ($role === 'manager') {

        return ['manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports'];

    } else if ($role === 'user') {

        return ['manage_customers', 'manage_quotes', 'manage_invoices'];

    }

    

    return [];

}

?>

