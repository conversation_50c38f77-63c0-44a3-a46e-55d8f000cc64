<?php

// Determina qual página está ativa

$current_page = basename($_SERVER['PHP_SELF']);

?>

<!-- Sidebar -->

<div class="sidebar p-3">

    <h4 class="mb-2 sistema-titulo">Estimate/Invoice</h4>

    <div class="menu-items">

        <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-home"></i>

            <span>Painel</span>

        </a>

        <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php' || $current_page == 'customers_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-users"></i>

            <span>Clientes</span>

        </a>

        <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php' || $current_page == 'inventory_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-box"></i>

            <span>Estoque</span>

        </a>

        <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice"></i>

            <span>Orçamentos</span>

        </a>

        <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice-dollar"></i>

            <span>Faturas</span>

        </a>



        <a href="config_new.php" class="menu-item <?php echo ($current_page == 'config.php' || $current_page == 'config_new.php' || $current_page == 'settings.php' || $current_page == 'user_create.php' || $current_page == 'user_create_new.php' || $current_page == 'user_edit.php' || $current_page == 'user_delete.php') ? 'active' : ''; ?>">

            <i class="fa fa-cog"></i>

            <span>Config</span>

        </a>

        <a href="logout.php" class="menu-item">

            <i class="fa fa-sign-out-alt"></i>

            <span>Sair</span>

        </a>

    </div>

</div>

