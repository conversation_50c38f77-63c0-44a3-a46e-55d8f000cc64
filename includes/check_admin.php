<?php

/**

 * Check if an administrator user exists and create one if not

 * This file should be included in the main entry points of the application

 */



// Only run this check if the database connection is available

if (isset($conn)) {

    // Check if the users and user_roles tables exist

    $tables_exist = true;



    $query = "SHOW TABLES LIKE 'users'";

    $result = $conn->query($query);

    if (!$result || $result->num_rows === 0) {

        $tables_exist = false;

    }



    $query = "SHOW TABLES LIKE 'user_roles'";

    $result = $conn->query($query);

    if (!$result || $result->num_rows === 0) {

        $tables_exist = false;

    }



    // If tables exist, check for admin user

    if ($tables_exist) {

        // Check if admin user exists

        $admin_exists = false;



        // Primeiro, verificar se a coluna 'name' existe na tabela user_roles

        $check_column = $conn->query("SHOW COLUMNS FROM user_roles LIKE 'name'");

        if ($check_column && $check_column->num_rows > 0) {

            // Usar a consulta original se a coluna existir

            $query = "SELECT u.id FROM users u

                      JOIN user_roles ur ON u.role_id = ur.id

                      WHERE ur.name = 'administrator'

                      LIMIT 1";

            $result = $conn->query($query);

            if ($result && $result->num_rows > 0) {

                $admin_exists = true;

            }

        } else {

            // Usar uma consulta alternativa se a coluna não existir

            // Verificar se há algum usuário com role_id = 1 (assumindo que 1 é o ID do administrador)

            $query = "SELECT id FROM users WHERE role_id = 1 LIMIT 1";

            $result = $conn->query($query);

            if ($result && $result->num_rows > 0) {

                $admin_exists = true;

            }

        }



        // If no admin exists, create one

        if (!$admin_exists) {

            // Get the administrator role ID

            $admin_role_id = null;

            $query = "SELECT id FROM user_roles WHERE name = 'administrator'";

            $result = $conn->query($query);

            if ($result && $result->num_rows > 0) {

                $row = $result->fetch_assoc();

                $admin_role_id = $row['id'];



                // Check if Admin user already exists

                $username = 'Admin';

                $email = '<EMAIL>';

                $password = password_hash('1961', PASSWORD_DEFAULT);



                // First check if the username exists

                $check_query = "SELECT id, role_id FROM users WHERE username = ? OR email = ? LIMIT 1";

                $check_stmt = $conn->prepare($check_query);

                $check_stmt->bind_param('ss', $username, $email);

                $check_stmt->execute();

                $check_result = $check_stmt->get_result();



                if ($check_result && $check_result->num_rows > 0) {

                    // User exists, check if they have admin role

                    $user_row = $check_result->fetch_assoc();



                    // If user doesn't have admin role, update it

                    if ($user_row['role_id'] != $admin_role_id) {

                        $update_query = "UPDATE users SET role_id = ? WHERE id = ?";

                        $update_stmt = $conn->prepare($update_query);

                        $update_stmt->bind_param('ii', $admin_role_id, $user_row['id']);

                        $update_stmt->execute();



                        // Log the update (optional)

                        error_log("Existing user updated to administrator role.");

                    }

                } else {

                    // User doesn't exist, create new admin user

                    $query = "INSERT INTO users (username, email, password, role_id, created_at) VALUES (?, ?, ?, ?, NOW())";

                    $stmt = $conn->prepare($query);

                    $stmt->bind_param('sssi', $username, $email, $password, $admin_role_id);



                    try {

                        $stmt->execute();

                        // Log the creation (optional)

                        error_log("Default administrator user created automatically.");

                    } catch (Exception $e) {

                        // Log the error but continue

                        error_log("Error creating admin user: " . $e->getMessage());

                    }

                }

            }

        }

    }

}

?>

