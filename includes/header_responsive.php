<header>

    <div class="container">

        <nav class="navbar">

            <div class="logo"><PERSON>'s AC Repair</div>

            <div class="menu-toggle" id="mobile-menu">

                <span></span>

                <span></span>

                <span></span>

            </div>

            <ul class="nav-links" id="nav-menu">

                <li><a href="index.php">Home</a></li>

                <li><a href="customers.php">Customers</a></li>

                <li><a href="inventory.php">Inventory</a></li>

                <li><a href="quotes.php">Quotes</a></li>

                <li><a href="invoices.php">Invoices</a></li>

            </ul>

        </nav>

    </div>

</header>



<script>

    document.addEventListener('DOMContentLoaded', function() {

        const mobileMenu = document.getElementById('mobile-menu');

        const navMenu = document.getElementById('nav-menu');



        if (mobileMenu && navMenu) {

            // Função para alternar o menu

            function toggleMenu(e) {

                if (e) {

                    e.preventDefault();

                }

                navMenu.classList.toggle('active');

                mobileMenu.classList.toggle('active');

            }



            // Adicionar eventos para clique e toque

            mobileMenu.addEventListener('click', toggleMenu);

            mobileMenu.addEventListener('touchstart', toggleMenu, {passive: false});



            // Fechar o menu ao clicar em um link

            const navLinks = document.querySelectorAll('.nav-links a');

            navLinks.forEach(link => {

                link.addEventListener('click', function() {

                    // Apenas fecha o menu se estiver em modo mobile

                    if (window.innerWidth <= 768) {

                        toggleMenu();

                    }

                });

            });

        }

    });

</script>

