<?php

// Configurações do Banco de Dados

define('DB_HOST', 'localhost');

define('DB_USER', 'tonyoli_NOVO');

define('DB_PASS', 'Novos');

define('DB_NAME', 'tonyoli_NOVO');



// Configurações gerais

define('SITE_URL', 'http://seu-dominio.com');

define('SITE_NAME', 'Sistema de Faturas');



// Configuração de fuso horário

date_default_timezone_set('America/Sao_Paulo');



// Configuração de sessão

ini_set('session.cookie_httponly', 1);

session_start();



// Conexão com o banco de dados

function db_connect() {

    $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);



    if (!$conn) {

        die("Erro na conexão: " . mysqli_connect_error());

    }



    mysqli_set_charset($conn, "utf8");

    return $conn;

}



// Funções de banco de dados

function db_query($query) {

    global $conn;

    if (!isset($conn)) {

        $conn = db_connect();

    }

    return mysqli_query($conn, $query);

}



function db_fetch_assoc($result) {

    return mysqli_fetch_assoc($result);

}



function db_num_rows($result) {

    return mysqli_num_rows($result);

}



function db_error() {

    global $conn;

    return mysqli_error($conn);

}



function db_escape($value) {

    global $conn;

    return mysqli_real_escape_string($conn, $value);

}



function db_begin_transaction() {

    global $conn;

    return mysqli_begin_transaction($conn);

}



function db_commit() {

    global $conn;

    return mysqli_commit($conn);

}



function db_rollback() {

    global $conn;

    return mysqli_rollback($conn);

}



function db_insert_id() {

    global $conn;

    return mysqli_insert_id($conn);

}



// Conexão inicial com o banco

$conn = db_connect();