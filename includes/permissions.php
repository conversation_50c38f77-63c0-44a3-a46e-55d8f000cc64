<?php

/**

 * Check if the current user has a specific permission

 *

 * @param string $permission The permission name to check

 * @return bool True if the user has the permission, false otherwise

 */

function user_has_permission($permission) {

    // Administrator has all permissions

    if (isset($_SESSION['user_role']) && (strtolower($_SESSION['user_role']) === 'administrator' || strtolower($_SESSION['user_role']) === 'admin')) {

        return true;

    }



    // Manager has specific permissions

    if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'manager') {

        $manager_permissions = ['manage_customers', 'manage_inventory', 'manage_quotes', 'manage_invoices', 'view_reports'];

        if (in_array($permission, $manager_permissions)) {

            return true;

        }

    }



    // Regular user has limited permissions

    if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'user') {

        $user_permissions = ['manage_customers', 'manage_quotes', 'manage_invoices'];

        if (in_array($permission, $user_permissions)) {

            return true;

        }

    }



    // Não verificamos mais as permissões na sessão como fallback

    // Isso garante que as permissões sejam estritamente baseadas no papel do usuário

    // e evita que usuários tenham permissões que não deveriam ter



    return false;

}



/**

 * Check if the current user has a specific role

 *

 * @param string|array $roles The role(s) to check

 * @return bool True if the user has any of the roles, false otherwise

 */

function user_has_role($roles) {

    if (!isset($_SESSION['user_role'])) {

        return false;

    }



    $user_role = strtolower($_SESSION['user_role']);



    if (is_array($roles)) {

        // Converter todos os roles para minúsculas para comparação case-insensitive

        $roles_lower = array_map('strtolower', $roles);

        return in_array($user_role, $roles_lower);

    } else {

        return $user_role === strtolower($roles);

    }

}



/**

 * Check if a user has a specific role by user ID

 *

 * @param int $user_id The user ID to check

 * @param string $role_name The role name to check

 * @return bool True if the user has the role, false otherwise

 */

function user_has_role_by_id($user_id, $role_name) {

    global $conn;



    // Se não houver conexão, retornar falso

    if (!isset($conn)) {

        return false;

    }



    // Verificar se as tabelas necessárias existem

    $tables_exist = true;



    $query = "SHOW TABLES LIKE 'users'";

    $result = $conn->query($query);

    if (!$result || $result->num_rows === 0) {

        $tables_exist = false;

    }



    $query = "SHOW TABLES LIKE 'user_roles'";

    $result = $conn->query($query);

    if (!$result || $result->num_rows === 0) {

        $tables_exist = false;

    }



    if (!$tables_exist) {

        return false;

    }



    try {

        // Consulta para verificar se o usuário tem a role

        $query = "SELECT 1 FROM users u

                  JOIN user_roles ur ON u.role_id = ur.id

                  WHERE u.id = ? AND LOWER(ur.name) = LOWER(?)

                  LIMIT 1";



        $stmt = $conn->prepare($query);

        if (!$stmt) {

            return false;

        }



        $stmt->bind_param('is', $user_id, $role_name);

        $stmt->execute();

        $result = $stmt->get_result();



        return ($result && $result->num_rows > 0);

    } catch (Exception $e) {

        error_log("Erro ao verificar role do usuário: " . $e->getMessage());

        return false;

    }

}



/**

 * Get the current user's role

 *

 * @return string|null The user's role or null if not set

 */

function get_user_role() {

    return $_SESSION['user_role'] ?? null;

}



/**

 * Get the current user's permissions

 *

 * @return array The user's permissions or empty array if not set

 */

function get_user_permissions() {

    return $_SESSION['user_permissions'] ?? [];

}

?>

