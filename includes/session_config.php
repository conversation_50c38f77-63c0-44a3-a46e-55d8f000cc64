<?php

// Configuração de sessão personalizada

function setup_session() {

    // Criar diretório de sessões se não existir

    $session_dir = __DIR__ . '/../sessions';

    if (!file_exists($session_dir)) {

        mkdir($session_dir, 0755, true);

    }

    

    // Definir o caminho da sessão

    ini_set('session.save_path', $session_dir);

    

    // Iniciar a sessão

    if (session_status() == PHP_SESSION_NONE) {

        session_start();

    }

}

?>

