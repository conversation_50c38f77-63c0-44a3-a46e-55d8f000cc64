<?php

// Determina qual página está ativa

$current_page = basename($_SERVER['PHP_SELF']);

?>

<!-- Sidebar -->

<div class="sidebar p-3">

    <h4 class="mb-2 sistema-titulo">Estimate/Invoice</h4>

    <div class="menu-items">

        <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-home"></i>

            <span>Painel</span>

        </a>

        <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php' || $current_page == 'customers_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-users"></i>

            <span>Clientes</span>

        </a>

        <?php if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) !== 'user'): ?>

        <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php' || $current_page == 'inventory_simple.php') ? 'active' : ''; ?>">

            <i class="fa fa-box"></i>

            <span>Estoque</span>

        </a>

        <?php endif; ?>

        <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice"></i>

            <span>Orçamentos</span>

        </a>

        <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">

            <i class="fa fa-file-invoice-dollar"></i>

            <span>Faturas</span>

        </a>



        <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php' || $current_page == 'service_call_create.php' || $current_page == 'service_call_view.php' || $current_page == 'service_call_edit.php' || $current_page == 'service_call_delete.php') ? 'active' : ''; ?>">

            <i class="fa fa-headset"></i>

            <span>Chamados</span>

        </a>



        <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">

            <i class="fa fa-share-alt"></i>

            <span>Links Compartilhados</span>

        </a>



        <?php if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) !== 'user'): ?>

        <a href="reports.php" class="menu-item <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>">

            <i class="fa fa-chart-bar"></i>

            <span>Relatórios</span>

        </a>

        <?php endif; ?>





        <?php if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'administrator'): ?>

        <a href="admin_users.php" class="menu-item <?php echo ($current_page == 'admin_users.php') ? 'active' : ''; ?>">

            <i class="fa fa-users-cog"></i>

            <span>Gerenciar Usuários</span>

        </a>

        <?php endif; ?>





        <?php

        // Check if user is admin or if no admin exists yet

        $show_admin_link = false;



        // Include permissions helper if not already included

        if (!function_exists('user_has_role') && file_exists('includes/permissions.php')) {

            include_once 'includes/permissions.php';

        }



        // Check if user is admin

        if (function_exists('user_has_role') && isset($_SESSION['user_role'])) {

            $is_admin = user_has_role('administrator');

            if ($is_admin) {

                $show_admin_link = true;

            }

        }



        // If not admin, check if admin exists in the system

        if (!$show_admin_link) {

            // Include database connection if not already included

            if (!isset($conn) && file_exists('db_connect.php')) {

                include_once 'db_connect.php';

            }



            if (isset($conn)) {

                $admin_exists = false;



                // Primeiro, verificar se a coluna 'name' existe na tabela user_roles

                $check_column = $conn->query("SHOW COLUMNS FROM user_roles LIKE 'name'");

                if ($check_column && $check_column->num_rows > 0) {

                    // Usar a consulta original se a coluna existir

                    $query = "SELECT 1 FROM users u

                              JOIN user_roles ur ON u.role_id = ur.id

                              WHERE ur.name = 'administrator'

                              LIMIT 1";

                    $result = $conn->query($query);

                    if ($result && $result->num_rows > 0) {

                        $admin_exists = true;

                    }

                } else {

                    // Usar uma consulta alternativa se a coluna não existir

                    // Verificar se há algum usuário com role_id = 1 (assumindo que 1 é o ID do administrador)

                    $query = "SELECT 1 FROM users WHERE role_id = 1 LIMIT 1";

                    $result = $conn->query($query);

                    if ($result && $result->num_rows > 0) {

                        $admin_exists = true;

                    }

                }



                // If no admin exists, show the link to anyone

                if (!$admin_exists) {

                    $show_admin_link = true;

                }

            }

        }



        // Show admin links if appropriate

        if ($show_admin_link):

        ?>

            <a href="admin_users.php" class="menu-item <?php echo ($current_page == 'admin_users.php' || $current_page == 'admin_user_add.php' || $current_page == 'admin_user_edit.php') ? 'active' : ''; ?>">

                <i class="fa fa-users-cog"></i>

                <span>Usuários</span>

            </a>

            <a href="admin_roles.php" class="menu-item <?php echo ($current_page == 'admin_roles.php' || $current_page == 'admin_role_edit.php') ? 'active' : ''; ?>">

                <i class="fa fa-user-tag"></i>

                <span>Permissões</span>

            </a>

            <a href="admin_manage_roles.php" class="menu-item <?php echo ($current_page == 'admin_manage_roles.php') ? 'active' : ''; ?>">

                <i class="fa fa-user-shield"></i>

                <span>Atribuir Roles</span>

            </a>

            <a href="admin_view_permissions.php" class="menu-item <?php echo ($current_page == 'admin_view_permissions.php') ? 'active' : ''; ?>">

                <i class="fa fa-key"></i>

                <span>Ver Permissões</span>

            </a>



            <a href="backup.php" class="menu-item <?php echo ($current_page == 'backup.php') ? 'active' : ''; ?>">

                <i class="fa fa-database"></i>

                <span>Backup</span>

            </a>

        <?php endif; ?>



        <!-- Botão Sair sempre no final do menu -->

        <a href="logout.php" class="menu-item">

            <i class="fa fa-sign-out-alt"></i>

            <span>Sair</span>

        </a>

    </div>

</div>

