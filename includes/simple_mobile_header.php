<?php
// Mobile Header - Versão Funcional Garantida
// Baseado no dashboard_simple.php que funciona
$current_page = basename($_SERVER['PHP_SELF']);
$header_title = isset($header_title) ? $header_title : '<PERSON>\'s AC Repair';
?>

<style>
/* ===== MOBILE HEADER - VERSÃO FUNCIONAL ===== */
.mobile-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 60px !important;
    min-height: 60px !important;
    max-height: 60px !important;
    background: linear-gradient(135deg, #2c3e50, #3498db) !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 0 1rem !important;
    z-index: 99998 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    box-sizing: border-box !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.mobile-header h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.menu-toggle {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(255,255,255,0.1) !important;
    border: 2px solid rgba(255,255,255,0.2) !important;
    color: white !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    width: 44px !important;
    height: 44px !important;
    min-width: 44px !important;
    min-height: 44px !important;
    max-width: 44px !important;
    max-height: 44px !important;
    z-index: 99999 !important;
    position: relative !important;
    pointer-events: auto !important;
    touch-action: manipulation !important;
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    flex-shrink: 0 !important;
    box-sizing: border-box !important;
}

.menu-toggle:hover,
.menu-toggle:focus,
.menu-toggle:active {
    background: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.4) !important;
    transform: scale(1.05) !important;
    outline: none !important;
}

.menu-toggle:active {
    transform: scale(0.95) !important;
    background: rgba(255,255,255,0.3) !important;
}

.menu-toggle i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    transform-origin: center !important;
    display: block !important;
    font-size: 1.2rem !important;
    line-height: 1 !important;
    width: auto !important;
    height: auto !important;
    color: white !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: none !important;
}

/* Garantir que o ícone seja sempre visível */
.menu-toggle .fas,
.menu-toggle .fa {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

.slide-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    transform: translateY(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-height: calc(100vh - 60px);
    overflow-y: auto;
}

.slide-menu.active {
    transform: translateY(0);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    min-height: 100px;
    border: 1px solid rgba(255,255,255,0.1);
}

.menu-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.menu-item.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.menu-item i {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.menu-item span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

.menu-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 998;
    backdrop-filter: blur(2px);
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Força bruta para garantir visibilidade do botão */
button#menuToggle,
.menu-toggle,
.mobile-header .menu-toggle {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
    position: relative !important;
    z-index: 99999 !important;
    pointer-events: auto !important;
}

/* Ajustar body para o header fixo */
body {
    padding-top: 60px !important;
}

/* Prevenir scroll quando menu está aberto */
body.menu-open {
    overflow: hidden !important;
}

/* Garantir que o header seja sempre visível */
.mobile-header {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Responsividade */
@media (max-width: 768px) {
    .menu-grid {
        gap: 0.75rem;
        padding: 1rem;
    }

    .menu-item {
        min-height: 80px;
        padding: 1rem 0.5rem;
    }

    .menu-item i {
        font-size: 1.5rem;
    }

    .menu-item span {
        font-size: 0.8rem;
    }
}
</style>

<!-- Mobile Header -->
<header class="mobile-header">
    <h1><?php echo htmlspecialchars($header_title); ?></h1>
    <button class="menu-toggle" id="menuToggle">
        <i class="fas fa-bars"></i>
    </button>
</header>

<!-- Slide Menu -->
<nav class="slide-menu" id="slideMenu">
    <div class="menu-grid">
        <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">
            <i class="fa fa-home"></i>
            <span>Painel</span>
        </a>
        <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">
            <i class="fa fa-users"></i>
            <span>Clientes</span>
        </a>
        <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">
            <i class="fa fa-box"></i>
            <span>Estoque</span>
        </a>
        <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">
            <i class="fa fa-file-invoice"></i>
            <span>Orçamentos</span>
        </a>
        <a href="invoices.php" class="menu-item <?php echo ($current_page == 'invoices.php') ? 'active' : ''; ?>">
            <i class="fa fa-file-invoice-dollar"></i>
            <span>Faturas</span>
        </a>
        <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php') ? 'active' : ''; ?>">
            <i class="fa fa-headset"></i>
            <span>Chamados</span>
        </a>
        <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">
            <i class="fa fa-share-alt"></i>
            <span>Links</span>
        </a>
        <a href="logout.php" class="menu-item">
            <i class="fa fa-sign-out-alt"></i>
            <span>Sair</span>
        </a>
    </div>
</nav>

<!-- Menu Overlay -->
<div class="menu-overlay" id="menuOverlay"></div>

<script>
// Menu Mobile com Vibração e Animação Robusta
(function() {
    'use strict';

    let menuInitialized = false;

    function initMobileMenu() {
        if (menuInitialized) return;

        const menuToggle = document.getElementById('menuToggle');
        const slideMenu = document.getElementById('slideMenu');
        const menuOverlay = document.getElementById('menuOverlay');

        if (!menuToggle || !slideMenu || !menuOverlay) {
            console.log('🍔 Aguardando elementos do menu...');
            return;
        }

        console.log('🍔 Inicializando menu mobile com vibração...');
        menuInitialized = true;

        function vibrate() {
            // Vibração para dispositivos móveis
            if (navigator.vibrate) {
                navigator.vibrate(50); // 50ms de vibração
            }
        }

        function toggleMenu(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            vibrate(); // Vibrar ao tocar

            const isActive = slideMenu.classList.contains('active');
            const icon = menuToggle.querySelector('i');

            if (isActive) {
                // Fechar menu
                slideMenu.classList.remove('active');
                menuOverlay.classList.remove('active');
                document.body.classList.remove('menu-open');

                if (icon) {
                    icon.style.transform = 'rotate(0deg)';
                    icon.className = 'fas fa-bars';
                }

                console.log('✅ Menu fechado');
            } else {
                // Abrir menu
                slideMenu.classList.add('active');
                menuOverlay.classList.add('active');
                document.body.classList.add('menu-open');

                if (icon) {
                    icon.style.transform = 'rotate(90deg)';
                    icon.className = 'fas fa-times';
                }

                console.log('✅ Menu aberto');
            }
        }

        // Event listeners múltiplos para garantir funcionamento
        menuToggle.addEventListener('click', toggleMenu, { passive: false });
        menuToggle.addEventListener('touchstart', toggleMenu, { passive: false });
        menuToggle.addEventListener('touchend', function(e) {
            e.preventDefault();
        }, { passive: false });

        menuOverlay.addEventListener('click', toggleMenu);
        menuOverlay.addEventListener('touchstart', toggleMenu, { passive: false });

        // Fechar com ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && slideMenu.classList.contains('active')) {
                toggleMenu();
            }
        });

        // Fechar ao clicar em item do menu
        const menuItems = slideMenu.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', function() {
                vibrate(); // Vibrar ao clicar em item
                setTimeout(toggleMenu, 100);
            });
        });

        // Forçar visibilidade do botão
        function ensureButtonVisibility() {
            if (menuToggle) {
                menuToggle.style.display = 'flex';
                menuToggle.style.opacity = '1';
                menuToggle.style.visibility = 'visible';
                menuToggle.style.pointerEvents = 'auto';
                menuToggle.style.touchAction = 'manipulation';
                menuToggle.style.zIndex = '99999';
                menuToggle.style.position = 'relative';

                // Garantir que o ícone seja visível
                const icon = menuToggle.querySelector('i');
                if (icon) {
                    icon.style.display = 'block';
                    icon.style.opacity = '1';
                    icon.style.visibility = 'visible';
                    icon.style.color = 'white';
                }
            }
        }

        // Aplicar visibilidade imediatamente
        ensureButtonVisibility();

        // Verificar visibilidade periodicamente
        setInterval(ensureButtonVisibility, 1000);

        console.log('✅ Menu mobile inicializado com sucesso!');
    }

    // Múltiplas tentativas de inicialização
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileMenu);
    } else {
        initMobileMenu();
    }

    // Backup: tentar novamente após delays
    setTimeout(initMobileMenu, 500);
    setTimeout(initMobileMenu, 1000);
    setTimeout(initMobileMenu, 2000);

    // Observar mudanças no DOM para elementos adicionados dinamicamente
    if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    initMobileMenu();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
})();
</script>
