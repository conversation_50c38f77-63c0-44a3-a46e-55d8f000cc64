<?php

// Função para verificar se usuário está logado

function check_login() {

    if (!isset($_SESSION['user_id'])) {

        header("Location: login.php");

        exit;

    }

}



// Função para formatar valor em reais

function format_money($value) {

    return number_format($value, 2, ',', '.');

}



// Função para formatar data

function format_date($date) {

    return date('d/m/Y', strtotime($date));

}



// Função para validar data

function validate_date($date) {

    $d = DateTime::createFromFormat('Y-m-d', $date);

    return $d && $d->format('Y-m-d') === $date;

}



// Função para obter o status da fatura traduzido

function get_invoice_status($status) {

    // Padronizar o status para valores em inglês

    $normalized_status = strtolower(trim($status));



    // Mapeamento de valores antigos para novos (padronizados)

    $status_mapping = [

        'pending' => 'unpaid',

        'pago' => 'paid',

        'pendente' => 'unpaid',

        'parcialmente pago' => 'partial',

        'cancelado' => 'cancelled',

        'vencido' => 'overdue'

    ];



    // Normalizar o status se for um dos valores antigos

    if (isset($status_mapping[$normalized_status])) {

        $normalized_status = $status_mapping[$normalized_status];

    }



    // Traduções para exibição

    $status_labels = [

        'unpaid' => 'Unpaid',

        'partial' => 'Partially Paid',

        'paid' => 'Paid',

        'cancelled' => 'Cancelled',

        'overdue' => 'Overdue'

    ];



    return isset($status_labels[$normalized_status]) ? $status_labels[$normalized_status] : ucfirst($normalized_status);

}



// Nota: As funções de banco de dados foram movidas para db_connect.php

// A função db_close() já está definida em db_connect.php



function log_action($message, $level = 'info') {

    try {

        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0;

        $timestamp = date('Y-m-d H:i:s');

        $ip = $_SERVER['REMOTE_ADDR'];



        $log_entry = [

            'timestamp' => $timestamp,

            'user_id' => $user_id,

            'ip' => $ip,

            'level' => $level,

            'message' => $message

        ];



        // Verificar se a tabela system_logs existe

        $result = db_query("SHOW TABLES LIKE 'system_logs'");

        if (db_num_rows($result) == 0) {

            // Criar tabela se não existir

            $create_table = "CREATE TABLE IF NOT EXISTS system_logs (

                id INT AUTO_INCREMENT PRIMARY KEY,

                timestamp DATETIME NOT NULL,

                user_id INT NOT NULL,

                ip_address VARCHAR(45) NOT NULL,

                level VARCHAR(20) NOT NULL,

                message TEXT NOT NULL,

                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

            )";

            db_query($create_table);

        }



        // Inserir no banco de dados

        $query = "INSERT INTO system_logs (timestamp, user_id, ip_address, level, message)

                  VALUES ('$timestamp', $user_id, '$ip', '$level', '" . db_escape($message) . "')";

        db_query($query);



        // Verificar se o diretório de logs existe

        $log_dir = dirname(__FILE__) . '/../logs';

        if (!is_dir($log_dir)) {

            if (!mkdir($log_dir, 0755, true)) {

                error_log("Não foi possível criar o diretório de logs: $log_dir");

            }

        }



        // Também salvar em arquivo para backup

        if (is_dir($log_dir) && is_writable($log_dir)) {

            $log_file = $log_dir . '/system.log';

            $log_text = json_encode($log_entry) . "\n";

            file_put_contents($log_file, $log_text, FILE_APPEND);

        }

    } catch (Exception $e) {

        // Falha silenciosa, apenas registra o erro no log do PHP

        error_log("Erro ao registrar log: " . $e->getMessage());

    }

}

