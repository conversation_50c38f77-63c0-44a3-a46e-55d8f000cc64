<?php
// Habilitar exibição de erros para depuração
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Verificar se estamos usando o modo bypass
$bypass_auth = isset($_GET['bypass']) && $_GET['bypass'] === 'true';

if (!$bypass_auth) {
    // Incluir verificação de sessão normal
    require_once 'check_session.php';

    // Incluir funções de permissão
    if (!function_exists('user_has_permission') && file_exists('includes/permissions.php')) {
        include_once 'includes/permissions.php';
    }

    // Verificar se o usuário é administrador
    $is_admin = user_has_role('administrator');

    // Se não for administrador, redirecionar para o dashboard
    if (!$is_admin) {
        // Log the access attempt
        error_log("Access attempt to admin_manage_roles.php by non-admin user: {$_SESSION['username']} with role {$_SESSION['user_role']}");
        header('Location: dashboard_simple.php');
        exit;
    }
} else {
    // Modo bypass - incluir apenas a conexão com o banco de dados
    require_once 'db_connect.php';
    $is_admin = true; // Fingir que é administrador no modo bypass

    // Exibir aviso de modo bypass
    echo '<div style="background-color: #fff3cd; color: #856404; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #ffeeba;">
            <strong>Atenção!</strong> Você está acessando esta página no modo bypass, ignorando as verificações de permissão.
          </div>';
}

// Processar atualização de role
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
    $role_id = isset($_POST['role_id']) ? (int)$_POST['role_id'] : 0;

    if ($user_id > 0 && $role_id > 0) {
        // Verificar se o usuário existe
        $query = "SELECT id, username FROM users WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $user = $result->fetch_assoc();

            // Verificar se a role existe
            $query = "SELECT id, name FROM user_roles WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('i', $role_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $result->num_rows > 0) {
                $role = $result->fetch_assoc();

                // Proteção especial para administradores
                $is_last_admin = false;

                // Verificar se este é o último administrador
                if ($role['name'] !== 'administrator') {
                    $query = "SELECT COUNT(*) as admin_count FROM users u
                              JOIN user_roles ur ON u.role_id = ur.id
                              WHERE ur.name = 'administrator' AND u.id != ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param('i', $user_id);
                    $stmt->execute();
                    $result = $stmt->get_result();

                    if ($result && $row = $result->fetch_assoc()) {
                        if ((int)$row['admin_count'] === 0) {
                            $is_last_admin = true;
                        }
                    }
                }

                if ($is_last_admin) {
                    $message = "Não é possível alterar a role do último administrador.";
                    $message_type = 'error';
                } else {
                    // Atualizar a role do usuário
                    $query = "UPDATE users SET role_id = ? WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param('ii', $role_id, $user_id);

                    if ($stmt->execute()) {
                        $message = "Role do usuário '{$user['username']}' atualizada para '{$role['name']}' com sucesso.";
                        $message_type = 'success';
                    } else {
                        $message = "Erro ao atualizar role: " . $conn->error;
                        $message_type = 'error';
                    }
                }
            } else {
                $message = "Role não encontrada.";
                $message_type = 'error';
            }
        } else {
            $message = "Usuário não encontrado.";
            $message_type = 'error';
        }
    } else {
        $message = "Dados inválidos.";
        $message_type = 'error';
    }
}

// Obter todas as roles
$roles = [];
$query = "SELECT id, name, description FROM user_roles ORDER BY name";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $roles[$row['id']] = $row;
    }
}

// Obter todos os usuários com suas roles
$users = [];
$query = "SELECT u.id, u.username, u.email, u.role_id, ur.name as role_name
          FROM users u
          LEFT JOIN user_roles ur ON u.role_id = ur.id
          ORDER BY u.username";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Roles de Usuários</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th, .admin-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .admin-table tr:hover {
            background-color: #f8f9fa;
        }

        .role-select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: #fff;
            width: 100%;
        }

        .role-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }

        .role-admin {
            background-color: #cfe2ff;
            color: #0d6efd;
        }

        .role-manager {
            background-color: #d1e7dd;
            color: #198754;
        }

        .role-user {
            background-color: #fff3cd;
            color: #ffc107;
        }

        .role-other {
            background-color: #e2e3e5;
            color: #212529;
        }

        .action-button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .action-save {
            background-color: #0d6efd;
            color: #fff;
        }

        .action-save:hover {
            background-color: #0b5ed7;
        }

        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            width: 100%;
            font-size: 16px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination-button {
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #fff;
            color: #0d6efd;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .pagination-button:hover {
            background-color: #e9ecef;
        }

        .pagination-button.active {
            background-color: #0d6efd;
            color: #fff;
            border-color: #0d6efd;
        }

        .pagination-button.disabled {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">Gerenciar Roles de Usuários</h1>
                    <a href="dashboard_simple_fixed.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar para o Dashboard
                    </a>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="message <?php echo $message_type === 'success' ? 'message-success' : 'message-error'; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Roles Disponíveis</h2>
                    </div>
                    <div class="admin-card-body">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nome</th>
                                        <th>Descrição</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($roles as $role): ?>
                                        <tr>
                                            <td><?php echo $role['id']; ?></td>
                                            <td>
                                                <span class="role-badge <?php echo 'role-' . strtolower($role['name']); ?>">
                                                    <?php echo htmlspecialchars($role['name']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($role['description']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Usuários</h2>
                    </div>
                    <div class="admin-card-body">
                        <div class="search-container">
                            <input type="text" id="userSearch" class="search-input" placeholder="Buscar usuários...">
                        </div>

                        <div class="table-responsive">
                            <table class="admin-table" id="usersTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role Atual</th>
                                        <th>Nova Role</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <?php if (!empty($user['role_name'])): ?>
                                                    <span class="role-badge <?php echo 'role-' . strtolower($user['role_name']); ?>">
                                                        <?php echo htmlspecialchars($user['role_name']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="role-badge role-other">Sem Role</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <form method="post" action="" class="role-form">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <select name="role_id" class="role-select">
                                                        <?php foreach ($roles as $role): ?>
                                                            <option value="<?php echo $role['id']; ?>" <?php echo ($user['role_id'] == $role['id']) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($role['name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                            </td>
                                            <td>
                                                    <button type="submit" name="update_role" class="action-button action-save">
                                                        <i class="fas fa-save"></i> Salvar
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="pagination" id="pagination">
                            <!-- Pagination will be added by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality
            const searchInput = document.getElementById('userSearch');
            const table = document.getElementById('usersTable');
            const rows = table.querySelectorAll('tbody tr');

            searchInput.addEventListener('keyup', function() {
                const searchTerm = searchInput.value.toLowerCase();

                rows.forEach(row => {
                    const username = row.cells[1].textContent.toLowerCase();
                    const email = row.cells[2].textContent.toLowerCase();

                    if (username.includes(searchTerm) || email.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                updatePagination();
            });

            // Pagination
            const rowsPerPage = 10;
            let currentPage = 1;

            function showPage(page) {
                const start = (page - 1) * rowsPerPage;
                const end = start + rowsPerPage;
                let visibleIndex = 0;

                rows.forEach(row => {
                    if (row.style.display !== 'none') {
                        if (visibleIndex >= start && visibleIndex < end) {
                            row.classList.add('visible');
                        } else {
                            row.classList.remove('visible');
                        }
                        visibleIndex++;
                    }
                });

                updateVisibility();
                updatePaginationButtons(page, Math.ceil(visibleIndex / rowsPerPage));
            }

            function updateVisibility() {
                rows.forEach(row => {
                    if (row.classList.contains('visible')) {
                        row.style.display = '';
                    } else if (row.style.display !== 'none') {
                        row.style.display = 'none';
                    }
                });
            }

            function updatePaginationButtons(currentPage, totalPages) {
                const pagination = document.getElementById('pagination');
                pagination.innerHTML = '';

                if (totalPages <= 1) {
                    return;
                }

                // Previous button
                const prevButton = document.createElement('button');
                prevButton.textContent = 'Anterior';
                prevButton.classList.add('pagination-button');
                if (currentPage === 1) {
                    prevButton.classList.add('disabled');
                } else {
                    prevButton.addEventListener('click', () => showPage(currentPage - 1));
                }
                pagination.appendChild(prevButton);

                // Page buttons
                for (let i = 1; i <= totalPages; i++) {
                    const pageButton = document.createElement('button');
                    pageButton.textContent = i;
                    pageButton.classList.add('pagination-button');
                    if (i === currentPage) {
                        pageButton.classList.add('active');
                    }
                    pageButton.addEventListener('click', () => showPage(i));
                    pagination.appendChild(pageButton);
                }

                // Next button
                const nextButton = document.createElement('button');
                nextButton.textContent = 'Próximo';
                nextButton.classList.add('pagination-button');
                if (currentPage === totalPages) {
                    nextButton.classList.add('disabled');
                } else {
                    nextButton.addEventListener('click', () => showPage(currentPage + 1));
                }
                pagination.appendChild(nextButton);
            }

            function updatePagination() {
                currentPage = 1;
                showPage(currentPage);
            }

            // Initialize pagination
            updatePagination();
        });
    </script>
</body>
</html>
<?php
// Close database connection
if (isset($conn) && $conn instanceof mysqli) {
    $conn->close();
}
?>
