/**
 * Dashboard Mobile Menu
 * JavaScript para controlar o menu deslizante mobile
 */

document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.getElementById('menuToggle');
    const slideMenu = document.getElementById('slideMenu');
    const menuOverlay = document.getElementById('menuOverlay');
    
    // Verificar se os elementos existem
    if (!menuToggle || !slideMenu || !menuOverlay) {
        console.warn('Elementos do menu mobile não encontrados');
        return;
    }
    
    /**
     * Alternar estado do menu
     */
    function toggleMenu() {
        const isActive = slideMenu.classList.contains('active');
        
        if (isActive) {
            closeMenu();
        } else {
            openMenu();
        }
    }
    
    /**
     * Abrir menu
     */
    function openMenu() {
        slideMenu.classList.add('active');
        menuOverlay.classList.add('active');

        // Prevenir scroll do body e adicionar classe para CSS
        document.body.classList.add('menu-open');
        document.body.style.overflow = 'hidden';

        // Alterar ícone do botão e adicionar atributo ARIA
        const icon = menuToggle.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-times';
        }
        menuToggle.setAttribute('aria-expanded', 'true');

        // Adicionar animação escalonada aos itens do menu
        const menuItems = slideMenu.querySelectorAll('.menu-item');
        menuItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            // Remover foco de todos os itens para evitar borda azul
            item.blur();
        });

        // Garantir que nenhum item tenha foco ao abrir
        setTimeout(() => {
            menuItems.forEach(item => item.blur());
            // Focar no menu container em vez de um item específico
            slideMenu.focus();
        }, 50);
    }
    
    /**
     * Fechar menu
     */
    function closeMenu() {
        slideMenu.classList.remove('active');
        menuOverlay.classList.remove('active');

        // Restaurar scroll do body e remover classe CSS
        document.body.classList.remove('menu-open');
        document.body.style.overflow = '';

        // Alterar ícone do botão e atributo ARIA
        const icon = menuToggle.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-bars';
        }
        menuToggle.setAttribute('aria-expanded', 'false');

        // Remover delays de animação
        const menuItems = slideMenu.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.style.animationDelay = '';
        });
    }
    
    /**
     * Event Listeners
     */
    
    // Toggle menu ao clicar no botão
    menuToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleMenu();
    });
    
    // Fechar menu ao clicar no overlay
    menuOverlay.addEventListener('click', closeMenu);
    
    // Fechar menu ao clicar em um item do menu
    const menuItems = slideMenu.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            // Pequeno delay para permitir navegação
            setTimeout(closeMenu, 100);
        });
    });
    
    // Fechar menu com tecla ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && slideMenu.classList.contains('active')) {
            closeMenu();
        }
    });
    
    // Fechar menu ao redimensionar tela (para desktop)
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && slideMenu.classList.contains('active')) {
            closeMenu();
        }
    });
    
    // Prevenir propagação de cliques dentro do menu
    slideMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    /**
     * Melhorias de acessibilidade
     */
    
    // Gerenciar foco quando menu abre/fecha
    let lastFocusedElement = null;
    
    menuToggle.addEventListener('click', function() {
        if (slideMenu.classList.contains('active')) {
            lastFocusedElement = document.activeElement;
            // Não focar automaticamente - deixar usuário navegar naturalmente
        } else {
            // Restaurar foco
            if (lastFocusedElement) {
                lastFocusedElement.focus();
            }
        }
    });
    
    // Navegação por teclado no menu
    slideMenu.addEventListener('keydown', function(e) {
        const menuItems = Array.from(slideMenu.querySelectorAll('.menu-item'));
        const currentIndex = menuItems.indexOf(document.activeElement);
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                const nextIndex = (currentIndex + 1) % menuItems.length;
                menuItems[nextIndex].focus();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                const prevIndex = currentIndex === 0 ? menuItems.length - 1 : currentIndex - 1;
                menuItems[prevIndex].focus();
                break;
                
            case 'Home':
                e.preventDefault();
                menuItems[0].focus();
                break;
                
            case 'End':
                e.preventDefault();
                menuItems[menuItems.length - 1].focus();
                break;
        }
    });
    
    /**
     * Animações de entrada para quick access buttons
     */
    function animateQuickButtons() {
        const quickButtons = document.querySelectorAll('.quick-btn');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1
        });
        
        quickButtons.forEach(button => {
            button.style.opacity = '0';
            button.style.transform = 'translateY(20px)';
            button.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            observer.observe(button);
        });
    }
    
    // Inicializar animações
    animateQuickButtons();
    
    /**
     * Feedback tátil e animações para dispositivos móveis
     */

    // Função para aplicar animações aos botões de acesso rápido
    function addQuickButtonAnimations() {
        const quickButtons = document.querySelectorAll('.quick-btn');

        quickButtons.forEach(button => {
            let isAnimating = false;

            // Evento único para clique (funciona em mobile e desktop)
            button.addEventListener('click', function(e) {
                // Previne múltiplas animações simultâneas
                if (isAnimating) return;

                isAnimating = true;

                // Vibração física no celular
                if ('vibrate' in navigator) {
                    navigator.vibrate(30); // Vibração física simples
                }

                // Remove seleção de outros botões
                quickButtons.forEach(otherButton => {
                    if (otherButton !== this) {
                        otherButton.classList.remove('clicked', 'tap-animation');
                    }
                });

                // 1. Primeiro aplica a borda (imediato)
                this.classList.add('clicked');
                console.log('Borda aplicada:', this.classList.contains('clicked'), 'Cor:', getComputedStyle(this).getPropertyValue('--btn-color'));

                // 2. Força reflow para garantir que a borda apareceu
                this.offsetHeight;

                // 3. Depois aplica a animação
                this.classList.add('tap-animation');

                // Remove a classe de animação após o scale on tap
                setTimeout(() => {
                    this.classList.remove('tap-animation');
                    isAnimating = false;
                }, 200); // Duração do tapScale

                // Remove a borda após 500ms
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 500);
            });

            // Remove eventos conflitantes - usando apenas o click
        });
    }

    // Inicializar animações dos botões
    addQuickButtonAnimations();

    // Feedback tátil para menu
    if ('vibrate' in navigator) {
        menuToggle.addEventListener('click', () => {
            navigator.vibrate(50); // Vibração curta
        });

        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                navigator.vibrate(30); // Vibração mais suave
            });
        });
    }
    
    /**
     * Swipe gesture para fechar menu (opcional)
     */
    let startY = 0;
    let startX = 0;
    
    slideMenu.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
    });
    
    slideMenu.addEventListener('touchmove', function(e) {
        if (!slideMenu.classList.contains('active')) return;
        
        const currentY = e.touches[0].clientY;
        const currentX = e.touches[0].clientX;
        const diffY = startY - currentY;
        const diffX = Math.abs(startX - currentX);
        
        // Swipe up para fechar (movimento vertical maior que horizontal)
        if (diffY > 50 && diffX < 100) {
            closeMenu();
        }
    });
});

/**
 * Utilitários globais
 */
window.DashboardMenu = {
    open: function() {
        const event = new CustomEvent('toggleMenu');
        document.dispatchEvent(event);
    },
    
    close: function() {
        const slideMenu = document.getElementById('slideMenu');
        if (slideMenu && slideMenu.classList.contains('active')) {
            const event = new CustomEvent('toggleMenu');
            document.dispatchEvent(event);
        }
    }
};
