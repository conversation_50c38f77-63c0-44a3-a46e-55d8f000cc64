/**
 * Specific mobile actions for customers.php
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth > 768) {
        console.log('Screen width > 768px, not initializing mobile actions for customers');
        return;
    }
    
    console.log('Initializing mobile actions for customers.php');
    
    // Get all action button containers
    const containers = document.querySelectorAll('.action-buttons');
    console.log('Found', containers.length, 'action containers');
    
    // Process each container
    containers.forEach(function(container, index) {
        // Skip if already initialized
        if (container.querySelector('.action-toggle')) {
            console.log('Container', index, 'already initialized');
            return;
        }
        
        // Create the toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'btn action-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-ellipsis-v"></i>';
        toggleBtn.setAttribute('type', 'button');
        toggleBtn.setAttribute('aria-label', 'Actions menu');
        
        // Create the dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'action-dropdown';
        
        // Get all the action buttons
        const buttons = container.querySelectorAll('a.btn');
        console.log('Container', index, 'has', buttons.length, 'buttons');
        
        if (buttons.length === 0) {
            console.log('No action buttons found in container', index);
            return;
        }
        
        // Clone each button and add to dropdown
        buttons.forEach(function(btn) {
            const clone = btn.cloneNode(true);
            dropdown.appendChild(clone);
        });
        
        // Add our new elements to the container
        container.appendChild(dropdown);
        container.prepend(toggleBtn);
        
        // Hide original buttons
        buttons.forEach(function(btn) {
            btn.style.display = 'none';
        });
        
        // Add click event to toggle button
        toggleBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.classList.toggle('show');
            console.log('Toggled dropdown', index, dropdown.classList.contains('show'));
            
            // Close all other dropdowns
            document.querySelectorAll('.action-dropdown.show').forEach(function(d) {
                if (d !== dropdown) {
                    d.classList.remove('show');
                }
            });
        });
    });
    
    // Close dropdowns when clicking elsewhere
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.action-toggle')) {
            document.querySelectorAll('.action-dropdown.show').forEach(function(dropdown) {
                dropdown.classList.remove('show');
            });
        }
    });
});
