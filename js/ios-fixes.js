/**
 * Correções JavaScript específicas para iOS/iPhone
 */

// Adiciona classe 'loaded' ao body após carregamento completo
document.addEventListener('DOMContentLoaded', function() {
    // Adiciona classe imediatamente
    document.body.classList.add('loaded');

    // Força aplicação de estilos após um pequeno delay
    setTimeout(function() {
        document.body.classList.add('loaded');

        // Força recálculo de layout para iOS
        if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
            // Força modo claro
            document.documentElement.style.colorScheme = 'light';
            document.body.style.colorScheme = 'light';

            // Força cores claras em tabelas
            forceTableLightColors();

            // Ajusta altura dos campos
            adjustInputHeights();

            document.body.style.display = 'none';
            document.body.offsetHeight; // Força reflow
            document.body.style.display = '';
        }
    }, 100);
});

// Previne zoom em inputs no iOS
document.addEventListener('DOMContentLoaded', function() {
    // Detecta se é iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    if (isIOS) {
        // Adiciona meta tag adicional se necessário
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport && !viewport.content.includes('user-scalable=no')) {
            viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
        }

        // Força font-size mínimo em todos os inputs
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(function(input) {
            const computedStyle = window.getComputedStyle(input);
            const fontSize = parseFloat(computedStyle.fontSize);

            if (fontSize < 16) {
                input.style.fontSize = '16px';
            }
        });

        // Observa novos inputs adicionados dinamicamente
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const newInputs = node.querySelectorAll ? node.querySelectorAll('input, select, textarea') : [];
                        newInputs.forEach(function(input) {
                            input.style.fontSize = '16px';
                        });

                        // Se o próprio node é um input
                        if (node.matches && node.matches('input, select, textarea')) {
                            node.style.fontSize = '16px';
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
});

// Safe defaults for focus helpers used below (prevent ReferenceError)
let keyboardTimeout = null;
let scrollTimeout = null;
let isProcessing = false;
// Minimal no-op to avoid side effects; keeps behavior unchanged
function positionField(el) { /* no-op */ }

// Ajusta layout após mudança de orientação no iOS
window.addEventListener('orientationchange', function() {
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        setTimeout(function() {
            // Força recálculo de viewport
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
            }

            // Força reflow
            document.body.style.display = 'none';
            document.body.offsetHeight;
            document.body.style.display = '';
        }, 500);
    }
});

// Previne zoom duplo em inputs no iOS (EXCETO textareas para não interferir com seleção)
document.addEventListener('touchend', function(e) {
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        const target = e.target;
        // Remover textarea para evitar interferência na seleção de texto
        if (target.matches('input, select')) {
            // Previne zoom duplo
            target.blur();
            setTimeout(function() {
                target.focus();
            }, 0);
        }
    }
});

// Ajusta tamanhos de fonte dinamicamente para totais
function adjustTotalsForIOS() {
    // No-op: keep CSS control to avoid font-size flicker on load
}

// Executa ajuste após carregamento e redimensionamento
window.addEventListener('load', function() {
    adjustTotalsForIOS();
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        forceTableLightColors();
        adjustInputHeights();
    }
});

window.addEventListener('resize', function() {
    adjustTotalsForIOS();
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        adjustInputHeights();
    }
});

// Executa ajuste quando novos elementos são adicionados
document.addEventListener('DOMContentLoaded', function() {
    const observer = new MutationObserver(function(mutations) {
        let shouldAdjust = false;
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1 &&
                    (node.classList.contains('totals-display-label') ||
                     node.classList.contains('totals-display-value') ||
                     node.querySelector('.totals-display-label, .totals-display-value'))) {
                    shouldAdjust = true;
                }
            });
        });

        if (shouldAdjust) {
            setTimeout(function() {
                adjustTotalsForIOS();
                if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
                    forceTableLightColors();
                    adjustInputHeights();
                }
            }, 100);
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// SOLUÇÃO SIMPLES: Enter navega, não submete
document.addEventListener('keydown', function(e) {
    // Para QUALQUER input do formulário (exceto botões)
    if (e.key === 'Enter' && e.target.matches('#invoice-form input:not([type="submit"]):not([type="button"])')) {

        // IMPEDE o submit
        e.preventDefault();

        // Encontra todos os campos editáveis
        const inputs = Array.from(document.querySelectorAll(
            '#invoice-form input:not([type="hidden"]):not([readonly]):not([disabled]), ' +
            '#invoice-form select:not([disabled]), ' +
            '#invoice-form textarea:not([readonly]):not([disabled])'
        ));

        // Encontra o próximo campo
        const currentIndex = inputs.indexOf(e.target);
        const nextInput = inputs[currentIndex + 1];

        // Move o foco
        if (nextInput) {
            nextInput.focus();
        } else {
            e.target.blur(); // Remove foco se for o último
        }
    }
});

    // Event listeners simplificados
    document.addEventListener('focusin', function(e) {
        if (e.target.matches('input, select, textarea')) {
            // Cancela processamentos anteriores
            if (keyboardTimeout) clearTimeout(keyboardTimeout);
            if (scrollTimeout) clearTimeout(scrollTimeout);
            isProcessing = false;

            // Destaque visual mínimo
            e.target.style.backgroundColor = '#f8f9ff';
            e.target.style.borderColor = '#007bff';

            // Posicionamento apenas para inputs e selects (não textareas)
            if (!e.target.matches('textarea')) {
                positionField(e.target);
            }
        }
    });

    document.addEventListener('focusout', function(e) {
        if (e.target.matches('input, select, textarea')) {
            // Limpa todos os timeouts
            if (keyboardTimeout) clearTimeout(keyboardTimeout);
            if (scrollTimeout) clearTimeout(scrollTimeout);
            isProcessing = false;

            // Remove destaque
            e.target.style.backgroundColor = '';
            e.target.style.borderColor = '';
        }
    });

    // Previne conflitos com touch events
    document.addEventListener('touchstart', function(e) {
        if (e.target.matches('input, select, textarea')) {
            // Garante que o campo seja focável
            e.target.style.pointerEvents = 'auto';
            e.target.style.touchAction = 'manipulation';
        }
    }, { passive: true });



// Força cores claras em tabelas no iPhone
function forceTableLightColors() {
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        const tables = document.querySelectorAll('.items-table, .data-table, .table, .dashboard-table');

        tables.forEach(function(table) {
            // Força cores na tabela - COPIA EXATA DO DESKTOP
            table.style.backgroundColor = '#ffffff';
            table.style.color = '#333333';
            table.style.border = '1px solid #e0e0e0';
            table.style.borderCollapse = 'separate';
            table.style.borderSpacing = '0';
            table.style.borderRadius = '8px';
            table.style.overflow = 'hidden';

            // Força cores no cabeçalho - COPIA EXATA DO ANDROID/PC
            const headers = table.querySelectorAll('thead, thead tr, thead th');
            headers.forEach(function(header) {
                header.style.backgroundColor = '#e9ecef';
                header.style.color = '#343a40';
                header.style.borderBottom = '2px solid #ced4da';
                header.style.fontWeight = '600';
                header.style.textTransform = 'uppercase';
                header.style.fontSize = '12px';
                header.style.letterSpacing = '0.5px';
                header.style.height = '35px';
            });

            // Força cores nas linhas - COPIA EXATA DO ANDROID/PC
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(function(row, index) {
                // Linhas alternadas como no Android/PC
                if (index % 2 === 0) {
                    row.style.backgroundColor = '#ffffff';
                } else {
                    row.style.backgroundColor = '#f8f9fa';
                }
                row.style.color = '#333333';
                row.style.transition = 'background-color 0.2s ease';
                row.style.minHeight = '40px';
                row.style.height = 'auto';

                // Adiciona evento de hover com cor mais suave
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f5f5f5';
                });

                row.addEventListener('mouseleave', function() {
                    if (index % 2 === 0) {
                        this.style.backgroundColor = '#ffffff';
                    } else {
                        this.style.backgroundColor = '#f8f9fa';
                    }
                });

                // Força cores nas células
                const cells = row.querySelectorAll('td');
                cells.forEach(function(cell, cellIndex) {
                    cell.style.color = '#333333';
                    cell.style.borderBottom = '1px solid #f0f0f0';
                    cell.style.borderLeft = 'none';
                    cell.style.borderRight = 'none';
                    cell.style.borderTop = 'none';
                    cell.style.padding = '8px 10px';
                    cell.style.textAlign = 'left';
                    cell.style.fontSize = '14px';
                    cell.style.lineHeight = '1.3';
                    cell.style.verticalAlign = 'middle';
                    cell.style.fontWeight = 'normal';
                });
            });
        });
    }

    // Força correções no painel de totais
    function fixTotalsPanel() {
        const totalsSection = document.querySelector('.totals-section');
        if (totalsSection) {
            totalsSection.style.backgroundColor = '#ffffff';
            totalsSection.style.border = '1px solid #e0e0e0';
            totalsSection.style.borderRadius = '8px';
        }

        // Força altura igual nos dropdowns
        const dropdowns = document.querySelectorAll('.input-with-dropdown');
        dropdowns.forEach(function(dropdown) {
            dropdown.style.height = '38px';
            dropdown.style.display = 'flex';
            dropdown.style.alignItems = 'center';
            dropdown.style.border = '2px solid #e9ecef';
            dropdown.style.borderRadius = '8px';
            dropdown.style.backgroundColor = 'white';
            dropdown.style.overflow = 'hidden';
            dropdown.style.boxSizing = 'border-box';

            const input = dropdown.querySelector('input');
            if (input) {
                input.style.flex = '1';
                input.style.border = 'none';
                input.style.outline = 'none';
                input.style.padding = '9px 12px';
                input.style.fontSize = '14px';
                input.style.background = 'transparent';
                input.style.height = '100%';
                input.style.minWidth = '0';
                input.style.boxSizing = 'border-box';
            }

            const select = dropdown.querySelector('select');
            if (select) {
                select.style.border = 'none';
                select.style.outline = 'none';
                select.style.backgroundColor = '#f8f9fa';
                select.style.padding = '9px 8px';
                select.style.fontSize = '14px';
                select.style.fontWeight = '600';
                select.style.height = '100%';
                select.style.width = '45px';
                select.style.minWidth = '45px';
                select.style.maxWidth = '45px';
                select.style.borderLeft = '2px solid #e9ecef';
                select.style.cursor = 'pointer';
                select.style.boxSizing = 'border-box';
            }
        });

        // Força altura igual em campos de totais
        const totalsInputs = document.querySelectorAll('.totals-field input');
        totalsInputs.forEach(function(input) {
            if (!input.closest('.input-with-dropdown')) {
                input.style.height = '38px';
                input.style.padding = '9px 12px';
                input.style.fontSize = '14px';
                input.style.border = '2px solid #e9ecef';
                input.style.borderRadius = '6px';
            }
        });
    }

    // Força z-index simplificado para dropdowns
    function fixDropdownZIndex() {


    }

    // Aplica correções no painel de totais e z-index
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        fixTotalsPanel();
        fixDropdownZIndex();

        // Observa mudanças no DOM para aplicar correções
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    fixTotalsPanel();
                    fixDropdownZIndex();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Aplica z-index quando campo ganha foco
        document.addEventListener('focusin', function(e) {
            if (e.target.matches('input, select, textarea')) {
                const formGroup = e.target.closest('.form-group');
                if (formGroup) {
                    formGroup.style.zIndex = 'auto'; // alinhar ao Android: n3o for87ar z-index
                }

                // Se é um campo de pesquisa, garante visibilidade do dropdown
                const searchContainer = e.target.closest('.customer-search-container');
                if (searchContainer) {
                    // N3o alterar z-index no iOS; deixe o CSS definir (como no Android)
                    const results = searchContainer.querySelector('.search-results, #customer_results, #suggestions');
                    if (results) {
                        results.style.zIndex = ''; // limpar inline, usar CSS (1000)
                    }
                }
            }
        });

        // Remove z-index quando campo perde foco
        document.addEventListener('focusout', function(e) {
            if (e.target.matches('input, select, textarea')) {
                const formGroup = e.target.closest('.form-group');
                if (formGroup) {
                    setTimeout(function() {
                        formGroup.style.zIndex = 'auto';
                    }, 200);
                }
            }
        });


    }
}

// Ajusta altura dos campos no iPhone
function adjustInputHeights() {
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        const inputs = document.querySelectorAll('input, select, textarea, .form-control');

        inputs.forEach(function(input) {
            // Pula campos dentro de .input-with-dropdown para não interferir no alinhamento
            if (input.closest('.input-with-dropdown')) {
                return;
            }

            const screenWidth = window.innerWidth;

            if (screenWidth <= 480) {
                input.style.minHeight = '34px';
                input.style.height = '34px';
                input.style.padding = '5px 8px';
            } else if (screenWidth <= 768) {
                input.style.minHeight = '36px';
                input.style.height = '36px';
                input.style.padding = '6px 10px';
            } else {
                input.style.minHeight = '38px';
                input.style.height = '38px';
                input.style.padding = '8px 12px';
            }

            input.style.fontSize = '16px'; // Previne zoom
            input.style.lineHeight = '1.4';
            input.style.boxSizing = 'border-box';
            input.style.backgroundColor = '#ffffff';
            input.style.color = '#333333';
            input.style.borderColor = '#ced4da';
        });
    }
}
