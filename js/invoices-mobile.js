/**
 * Specific mobile actions for invoices.php (legacy - now using invoices_list.php)
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth > 768) {
        return;
    }
    
    console.log('Initializing mobile actions for invoices.php (legacy)');
    
    // Get all action button containers
    const containers = document.querySelectorAll('.action-buttons');
    console.log('Found', containers.length, 'action containers');
    
    // Process each container
    containers.forEach(function(container, index) {
        // Create the toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'btn action-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-ellipsis-v"></i>';
        
        // Create the dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'action-dropdown';
        
        // Get all the action buttons
        const buttons = container.querySelectorAll('a.btn');
        console.log('Container', index, 'has', buttons.length, 'buttons');
        
        // Clone each button and add to dropdown
        buttons.forEach(function(btn) {
            const clone = btn.cloneNode(true);
            dropdown.appendChild(clone);
        });
        
        // Clear the container and add our new elements
        container.innerHTML = '';
        container.appendChild(toggleBtn);
        container.appendChild(dropdown);
        
        // Add click event to toggle button
        toggleBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.classList.toggle('show');
            console.log('Toggled dropdown', index);
        });
    });
    
    // Close dropdowns when clicking elsewhere
    document.addEventListener('click', function() {
        document.querySelectorAll('.action-dropdown.show').forEach(function(dropdown) {
            dropdown.classList.remove('show');
        });
    });
});
