/**
 * Mobile Actions Menu - JavaScript for handling dropdown action menus on mobile devices
 */
function initMobileActions() {
    console.log('Initializing mobile actions menu...');

    // Only apply on mobile devices
    if (window.innerWidth > 768) {
        console.log('Screen width > 768px, not initializing mobile actions');
        return;
    }

    // Convert action buttons to dropdown menus on mobile
    const actionButtonsContainers = document.querySelectorAll('.action-buttons');
    console.log('Found', actionButtonsContainers.length, 'action button containers');

    actionButtonsContainers.forEach((container, index) => {
        // Skip if already initialized
        if (container.querySelector('.action-toggle')) {
            console.log('Container', index, 'already initialized');
            return;
        }

        // Get all action buttons before we modify the container
        const actionButtons = Array.from(container.querySelectorAll('.btn:not(.action-toggle)'));
        console.log('Container', index, 'has', actionButtons.length, 'action buttons');

        if (actionButtons.length === 0) {
            console.log('No action buttons found in container', index);
            return;
        }

        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.className = 'btn action-toggle';
        toggleButton.innerHTML = '<i class="fas fa-ellipsis-v"></i>';
        toggleButton.setAttribute('type', 'button');
        toggleButton.setAttribute('aria-label', 'Actions menu');

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'action-dropdown';

        // Clone buttons and add to dropdown
        actionButtons.forEach(button => {
            const clone = button.cloneNode(true);
            dropdown.appendChild(clone);
        });

        // Add toggle button and dropdown to container
        container.prepend(toggleButton);
        container.appendChild(dropdown);

        // Add click event to toggle button
        toggleButton.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.classList.toggle('show');
            console.log('Toggle clicked, dropdown visibility:', dropdown.classList.contains('show'));

            // Close all other dropdowns
            document.querySelectorAll('.action-dropdown.show').forEach(openDropdown => {
                if (openDropdown !== dropdown) {
                    openDropdown.classList.remove('show');
                }
            });
        });

        console.log('Mobile actions initialized for container', index);
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.action-buttons')) {
            document.querySelectorAll('.action-dropdown.show').forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initMobileActions);

// Also initialize on window resize (in case of orientation change)
window.addEventListener('resize', function() {
    // Debounce the resize event
    clearTimeout(window.resizeTimer);
    window.resizeTimer = setTimeout(initMobileActions, 250);
});
