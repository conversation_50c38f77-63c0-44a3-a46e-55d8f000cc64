// Script para gerenciar o comportamento do menu flutuante em todas as páginas

document.addEventListener('DOMContentLoaded', function() {
    // Função para ajustar o padding do conteúdo com base na altura do sidebar em dispositivos móveis
    function adjustContentPadding() {
        const sidebar = document.querySelector('.sidebar');
        const content = document.querySelector('.content');
        
        if (!sidebar || !content) return; // Sai da função se os elementos não existirem
        
        if (window.innerWidth <= 768) {
            // Em dispositivos móveis, ajusta o padding-top do conteúdo
            const sidebarHeight = sidebar.offsetHeight;
            
            // Verifica se está em modo paisagem
            const isLandscape = window.matchMedia('(orientation: landscape)').matches;
            
            if (isLandscape) {
                // Em modo paisagem, usa um padding menor
                document.body.style.paddingTop = '60px';
                if (content) content.style.paddingTop = '10px';
            } else {
                // Em modo retrato, usa o padding baseado na altura real
                document.body.style.paddingTop = (sidebarHeight + 10) + 'px';
                if (content) content.style.paddingTop = '10px';
            }
            
            // Define a variável CSS personalizada para a altura do sidebar
            document.documentElement.style.setProperty('--sidebar-mobile-height', sidebarHeight + 'px');
        } else {
            // Em telas maiores, restaura os valores padrão
            document.body.style.paddingTop = '0';
            if (content) content.style.marginLeft = '220px';
            document.documentElement.style.setProperty('--sidebar-mobile-height', '0px');
        }
    }
    
    // Executa a função quando a página carrega
    adjustContentPadding();
    
    // Também executa quando a janela é redimensionada
    window.addEventListener('resize', adjustContentPadding);
    
    // E quando a orientação do dispositivo muda
    window.addEventListener('orientationchange', adjustContentPadding);
    
    // Executa novamente após um pequeno atraso para garantir que todos os elementos foram carregados
    setTimeout(adjustContentPadding, 500);
});
