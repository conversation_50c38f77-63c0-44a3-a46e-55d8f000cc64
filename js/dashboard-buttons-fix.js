/**
 * Fix for dashboard action buttons
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard buttons fix loaded');
    
    // Get all action buttons
    const actionButtons = document.querySelectorAll('.action-btn');
    
    // Remove any existing click events
    actionButtons.forEach(function(button) {
        const clone = button.cloneNode(true);
        button.parentNode.replaceChild(clone, button);
    });
    
    // Add click event to each button
    document.querySelectorAll('.action-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default action
            
            // Get the href attribute
            const href = this.getAttribute('href');
            
            // Log the click
            console.log('Button clicked:', href);
            
            // If href exists, navigate to it
            if (href && href !== '#' && href !== 'javascript:void(0)') {
                window.location.href = href;
            }
        });
    });
});
