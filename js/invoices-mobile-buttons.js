/**
 * JavaScript for mobile invoice filter buttons
 */
document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth > 768) {
        return;
    }
    
    console.log('Mobile device detected, initializing mobile invoice buttons');
    
    // Add active class to the current button
    const filterButtons = document.querySelectorAll('.mobile-filter-btn');
    
    filterButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            // Don't add active class to the "New" button
            if (!this.classList.contains('btn-new')) {
                // Remove active class from all buttons
                filterButtons.forEach(function(btn) {
                    if (!btn.classList.contains('btn-new')) {
                        btn.classList.remove('active');
                    }
                });
                
                // Add active class to clicked button
                this.classList.add('active');
            }
        });
    });
});
