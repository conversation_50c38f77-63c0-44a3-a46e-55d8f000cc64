/**
 * Timezone configuration for Florida USA (Eastern Time)
 * This file ensures all JavaScript date/time operations use Florida timezone
 */

// Set default timezone for flatpickr and other date pickers
const FLORIDA_TIMEZONE = 'America/New_York';

// Function to get current date/time in Florida timezone
function getFloridaDateTime() {
    return new Date().toLocaleString("en-US", {timeZone: FLORIDA_TIMEZONE});
}

// Function to format date for Florida timezone
function formatFloridaDate(date, format = 'Y-m-d H:i') {
    const floridaDate = new Date(date.toLocaleString("en-US", {timeZone: FLORIDA_TIMEZONE}));
    
    const year = floridaDate.getFullYear();
    const month = String(floridaDate.getMonth() + 1).padStart(2, '0');
    const day = String(floridaDate.getDate()).padStart(2, '0');
    const hours = String(floridaDate.getHours()).padStart(2, '0');
    const minutes = String(floridaDate.getMinutes()).padStart(2, '0');
    
    switch(format) {
        case 'Y-m-d H:i':
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        case 'Y-m-d':
            return `${year}-${month}-${day}`;
        case 'm/d/Y':
            return `${month}/${day}/${year}`;
        case 'm/d/y':
            return `${month}/${day}/${String(year).slice(-2)}`;
        default:
            return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
}

// Default flatpickr configuration for Florida timezone
const floridaFlatpickrConfig = {
    enableTime: true,
    dateFormat: "Y-m-d H:i",
    time_24hr: true,
    defaultDate: getFloridaDateTime(),
    locale: {
        firstDayOfWeek: 0 // Sunday
    }
};

// Default jQuery UI datepicker configuration for Florida timezone
const floridaDatepickerConfig = {
    dateFormat: "mm/dd/y",
    changeMonth: true,
    changeYear: true,
    yearRange: "c-10:c+10",
    firstDay: 0 // Sunday
};

// Function to initialize all date pickers with Florida timezone
function initFloridaDatePickers() {
    // Initialize flatpickr instances
    if (typeof flatpickr !== 'undefined') {
        document.querySelectorAll('.flatpickr-florida').forEach(function(element) {
            flatpickr(element, floridaFlatpickrConfig);
        });
    }
    
    // Initialize jQuery UI datepickers
    if (typeof $ !== 'undefined' && $.datepicker) {
        $('.datepicker-florida').datepicker(floridaDatepickerConfig);
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initFloridaDatePickers();
});

// jQuery version for compatibility
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        initFloridaDatePickers();
    });
}

// Export functions for use in other scripts
window.FloridaTimezone = {
    getDateTime: getFloridaDateTime,
    formatDate: formatFloridaDate,
    flatpickrConfig: floridaFlatpickrConfig,
    datepickerConfig: floridaDatepickerConfig,
    init: initFloridaDatePickers,
    timezone: FLORIDA_TIMEZONE
};
