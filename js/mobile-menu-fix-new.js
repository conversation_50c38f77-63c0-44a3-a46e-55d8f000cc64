/**
 * Mobile Actions Menu - Direct implementation for mobile devices
 * This script replaces action buttons with a dropdown menu on mobile devices
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth <= 768;
    
    if (!isMobile) {
        return;
    }

    // Find all action button containers
    const actionContainers = document.querySelectorAll('.action-buttons');
    
    // Process each container
    actionContainers.forEach(function(container, index) {
        // Get all buttons in this container
        const buttons = Array.from(container.querySelectorAll('a.btn'));

        if (buttons.length === 0) {
            return;
        }
        
        // Create the dropdown structure
        const dropdown = document.createElement('div');
        dropdown.className = 'mobile-dropdown';
        
        // Create the toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'mobile-dropdown-toggle';
        toggleBtn.innerHTML = '<i class="fas fa-ellipsis-v"></i>';
        toggleBtn.setAttribute('type', 'button');
        toggleBtn.setAttribute('aria-label', 'Actions menu');
        
        // Create the dropdown menu
        const menu = document.createElement('div');
        menu.className = 'mobile-dropdown-menu';
        
        // Add the toggle button and menu to the dropdown
        dropdown.appendChild(toggleBtn);
        dropdown.appendChild(menu);
        
        // Store original buttons for reference
        const originalButtons = [];
        
        // Process each button
        buttons.forEach(function(button, buttonIndex) {
            // Store original button
            originalButtons.push(button);
            
            // Get button attributes
            const href = button.getAttribute('href') || '#';
            const target = button.getAttribute('target') || '';
            const title = button.getAttribute('title') || '';
            
            // Get button text (if any)
            let buttonText = '';
            if (button.textContent) {
                buttonText = button.textContent.trim();
            }
            
            // Get button icon (if any)
            let iconHTML = '';
            const iconElement = button.querySelector('i');
            if (iconElement) {
                iconHTML = iconElement.outerHTML;
            }
            
            // Determine display text (prefer title, fall back to button text)
            const displayText = title || buttonText || 'Action';
            
            // Create menu item
            const menuItem = document.createElement('a');
            menuItem.href = href;
            menuItem.className = 'mobile-dropdown-item';
            
            // Add target attribute if present
            if (target) {
                menuItem.setAttribute('target', target);
            }
            
            // Add color class if present
            if (button.classList.contains('btn-primary')) {
                menuItem.classList.add('btn-primary');
            } else if (button.classList.contains('btn-secondary')) {
                menuItem.classList.add('btn-secondary');
            } else if (button.classList.contains('btn-success')) {
                menuItem.classList.add('btn-success');
            } else if (button.classList.contains('btn-danger')) {
                menuItem.classList.add('btn-danger');
            } else if (button.classList.contains('btn-warning')) {
                menuItem.classList.add('btn-warning');
            } else if (button.classList.contains('btn-info')) {
                menuItem.classList.add('btn-info');
            }
            
            // Create icon container
            const iconContainer = document.createElement('span');
            iconContainer.className = 'menu-icon';
            if (iconHTML) {
                iconContainer.innerHTML = iconHTML;
            } else {
                iconContainer.innerHTML = '<i class="fas fa-circle" style="opacity: 0;"></i>';
            }
            
            // Create text container
            const textContainer = document.createElement('span');
            textContainer.className = 'menu-text';
            textContainer.textContent = displayText;
            
            // Add icon and text to menu item
            menuItem.appendChild(iconContainer);
            menuItem.appendChild(textContainer);
            
            // Add menu item to menu
            menu.appendChild(menuItem);
        });
        
        // Clear the container and add our dropdown
        container.innerHTML = '';
        container.appendChild(dropdown);
        
        // Add click event to toggle button
        toggleBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            
            // Check if menu is already shown
            const isShown = menu.classList.contains('show');
            
            // Close all menus first
            document.querySelectorAll('.mobile-dropdown-menu.show').forEach(function(openMenu) {
                openMenu.classList.remove('show');
            });
            
            // If menu wasn't shown before, show it now
            if (!isShown) {
                menu.classList.add('show');
                
                // First, reset any previously set positioning
                menu.style.left = '-138px'; // Match the CSS value
                menu.style.right = 'auto';
                menu.style.transform = ''; // Reset any transform
                
                // Wait a tiny bit for the menu to render with the new position
                setTimeout(function() {
                    // Position the menu based on available space
                    const rect = menu.getBoundingClientRect();
                    const viewportWidth = window.innerWidth;

                    // If menu would go off the right edge of the screen
                    if (rect.right > viewportWidth) {
                        menu.style.left = 'auto';
                        menu.style.right = '0';
                    }

                    // If menu would go off the left edge of the screen
                    if (rect.left < 0) {
                        menu.style.left = '0';
                        menu.style.right = 'auto';
                    }
                    
                    // For very small screens, position the menu appropriately
                    if (viewportWidth < 400) {
                        // If the menu is too wide for the screen or goes off the left edge
                        if (rect.width > viewportWidth * 0.9 || rect.left < 0) {
                            // Center the menu
                            menu.style.left = '50%';
                            menu.style.right = 'auto';
                            menu.style.transform = 'translateX(-50%)';
                        } else if (rect.right > viewportWidth) {
                            // If the menu goes off the right edge, align it to the right
                            menu.style.left = 'auto';
                            menu.style.right = '0';
                            menu.style.transform = '';
                        }
                    }
                }, 10);
            }
        });
    });
    
    // Close all menus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.mobile-dropdown')) {
            document.querySelectorAll('.mobile-dropdown-menu.show').forEach(function(dropdown) {
                dropdown.classList.remove('show');
            });
        }
    });
});
