// Main JavaScript for NOVO Sistema

document.addEventListener('DOMContentLoaded', function() {
    // Initialize delete confirmation
    initDeleteConfirmation();
    
    // Initialize table sorting
    initTableSorting();
    
    // Initialize form validation
    initFormValidation();
    
    // Initialize any item rows that exist on page load
    if (document.querySelector('.items-container')) {
        // Add at least one item row if none exist
        if (document.querySelectorAll('.item-row').length === 0) {
            addItemRow();
        }
    }
});

// Delete confirmation
function initDeleteConfirmation() {
    const deleteButtons = document.querySelectorAll('.delete-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Tem certeza que deseja excluir este item? Esta ação não pode ser desfeita.')) {
                e.preventDefault();
            }
        });
    });
}

// Table sorting
function initTableSorting() {
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const order = this.classList.contains('asc') ? 'desc' : 'asc';
                
                // Remove sort classes from all headers
                headers.forEach(h => {
                    h.classList.remove('asc', 'desc');
                });
                
                // Add sort class to clicked header
                this.classList.add(order);
                
                // Sort the table
                sortTable(table, column, order);
            });
        });
    });
}

function sortTable(table, column, order) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Sort rows
    rows.sort((a, b) => {
        const aValue = a.querySelector(`td[data-column="${column}"]`).textContent.trim();
        const bValue = b.querySelector(`td[data-column="${column}"]`).textContent.trim();
        
        // Check if values are numbers
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return order === 'asc' ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return order === 'asc' 
            ? aValue.localeCompare(bValue, 'pt-BR') 
            : bValue.localeCompare(aValue, 'pt-BR');
    });
    
    // Reorder rows in the DOM
    rows.forEach(row => {
        tbody.appendChild(row);
    });
}

// Form validation
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Highlight invalid fields
                const invalidFields = form.querySelectorAll(':invalid');
                invalidFields.forEach(field => {
                    field.classList.add('is-invalid');
                    
                    field.addEventListener('input', function() {
                        if (this.checkValidity()) {
                            this.classList.remove('is-invalid');
                        }
                    });
                });
                
                // Show alert
                alert('Por favor, preencha todos os campos obrigatórios corretamente.');
            }
        });
    });
}

// Quote and Invoice Item Functions
function addItemRow() {
    const itemsContainer = document.getElementById('items-container');
    const template = document.getElementById('item-template');
    
    if (itemsContainer && template) {
        const clone = document.importNode(template.content, true);
        itemsContainer.appendChild(clone);
        
        // Initialize the new row
        const newRow = itemsContainer.lastElementChild;
        const quantityInput = newRow.querySelector('.item-quantity');
        const priceInput = newRow.querySelector('.item-price');
        
        if (quantityInput && priceInput) {
            quantityInput.addEventListener('input', function() {
                calculateItemTotal(this);
            });
            
            priceInput.addEventListener('input', function() {
                calculateItemTotal(this);
            });
        }
    }
}

function removeItem(button) {
    const itemRow = button.closest('.item-row');
    itemRow.remove();
    calculateTotals();
}

function updateItemDetails(select) {
    const itemRow = select.closest('.item-row');
    const priceInput = itemRow.querySelector('.item-price');
    const option = select.options[select.selectedIndex];
    
    if (option && option.dataset.price) {
        priceInput.value = option.dataset.price;
    } else {
        priceInput.value = '';
    }
    
    calculateItemTotal(priceInput);
}

function calculateItemTotal(input) {
    const itemRow = input.closest('.item-row');
    const quantityInput = itemRow.querySelector('.item-quantity');
    const priceInput = itemRow.querySelector('.item-price');
    const subtotalInput = itemRow.querySelector('.item-subtotal');
    
    if (quantityInput && priceInput && subtotalInput) {
        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const subtotal = quantity * price;
        
        subtotalInput.value = subtotal.toFixed(2);
        
        calculateTotals();
    }
}

function calculateTotals() {
    const subtotalInput = document.getElementById('subtotal');
    const taxRateInput = document.getElementById('tax_rate');
    const taxAmountInput = document.getElementById('tax_amount');
    const discountAmountInput = document.getElementById('discount_amount');
    const totalInput = document.getElementById('total');
    
    if (subtotalInput && taxRateInput && taxAmountInput && discountAmountInput && totalInput) {
        // Calculate subtotal from all items
        const itemSubtotals = document.querySelectorAll('.item-subtotal');
        let subtotal = 0;
        
        itemSubtotals.forEach(input => {
            subtotal += parseFloat(input.value) || 0;
        });
        
        // Calculate tax and total
        const taxRate = parseFloat(taxRateInput.value) || 0;
        const taxAmount = subtotal * (taxRate / 100);
        const discountAmount = parseFloat(discountAmountInput.value) || 0;
        const total = subtotal + taxAmount - discountAmount;
        
        // Update form fields
        subtotalInput.value = subtotal.toFixed(2);
        taxAmountInput.value = taxAmount.toFixed(2);
        totalInput.value = total.toFixed(2);
    }
}

// Update tax amount when tax rate changes
function updateTaxAmount() {
    const subtotalInput = document.getElementById('subtotal');
    const taxRateInput = document.getElementById('tax_rate');
    const taxAmountInput = document.getElementById('tax_amount');
    
    if (subtotalInput && taxRateInput && taxAmountInput) {
        const subtotal = parseFloat(subtotalInput.value) || 0;
        const taxRate = parseFloat(taxRateInput.value) || 0;
        const taxAmount = subtotal * (taxRate / 100);
        
        taxAmountInput.value = taxAmount.toFixed(2);
        
        calculateTotals();
    }
}

// Print function
function printDocument() {
    window.print();
}
