/**
 * JavaScript functionality for invoices_list.php
 * Handles search, filtering, sorting, row selection, and delete confirmation
 */

// Global variables
let selectedRow = null;
let deleteInvoiceId = null;
let searchTimeout = null;

// Export functions for global access IMMEDIATELY
console.log('Exporting functions to global scope...');

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing...');
    initializeMenuFunctionality();
    initializeSearchFunctionality();
    initializeModalFunctionality();
    initializeKeyboardNavigation();
});

/**
 * Initialize mobile menu functionality
 */
function initializeMenuFunctionality() {
    const menuToggle = document.getElementById('menuToggle');
    const slideMenu = document.getElementById('slideMenu');
    const menuOverlay = document.getElementById('menuOverlay');

    if (!menuToggle || !slideMenu || !menuOverlay) {
        console.warn('Menu elements not found');
        return;
    }

    function toggleMenu() {
        const isActive = slideMenu.classList.contains('active');
        if (isActive) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        slideMenu.classList.add('active');
        menuOverlay.classList.add('active');
        document.body.style.overflow = 'hidden';
        menuToggle.setAttribute('aria-expanded', 'true');
    }

    function closeMenu() {
        slideMenu.classList.remove('active');
        menuOverlay.classList.remove('active');
        document.body.style.overflow = '';
        menuToggle.setAttribute('aria-expanded', 'false');
    }

    // Event listeners
    menuToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleMenu();
    });

    menuOverlay.addEventListener('click', closeMenu);

    // Close menu when clicking on menu items
    const menuItems = slideMenu.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            setTimeout(closeMenu, 100);
        });
    });

    // Close with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && slideMenu.classList.contains('active')) {
            closeMenu();
        }
    });

    // Close when resizing to larger screen
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && slideMenu.classList.contains('active')) {
            closeMenu();
        }
    });
}

/**
 * Initialize search and filter functionality
 */
function initializeSearchFunctionality() {
    console.log('Initializing search functionality...');
    const searchInput = document.getElementById('searchInput');
    const searchForm = document.getElementById('searchForm');

    if (!searchInput || !searchForm) {
        console.warn('Search elements not found');
        return;
    }

    console.log('Search elements found, adding event listeners...');

    // Real-time search with debouncing - keep focus
    searchInput.addEventListener('input', function() {
        console.log('Search input detected:', this.value);
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            console.log('Performing AJAX search...');
            performAjaxSearch();
        }, 300);
    });
}

/**
 * Submit search form (fallback)
 */
function submitSearchForm() {
    const searchForm = document.getElementById('searchForm');
    if (!searchForm) return;

    // Add loading state
    const table = document.getElementById('invoicesTable');
    if (table) {
        table.classList.add('loading');
    }

    searchForm.submit();
}

/**
 * AJAX search functionality
 */
function performAjaxSearch() {
    console.log('performAjaxSearch called');
    const searchForm = document.getElementById('searchForm');
    if (!searchForm) {
        console.error('Search form not found');
        return Promise.reject('Search form not found');
    }

    const formData = new FormData(searchForm);
    const params = new URLSearchParams(formData);
    console.log('Search params:', params.toString());

    // Add loading state
    const table = document.getElementById('invoicesTable');
    if (table) {
        table.classList.add('loading');
    }

    // Update URL without reloading page
    const newUrl = window.location.pathname + '?' + params.toString();
    console.log('New URL:', newUrl);
    window.history.replaceState({}, '', newUrl);

    // Fetch new content
    return fetch(newUrl, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('AJAX response received');
        return response.text();
    })
    .then(html => {
        console.log('Parsing HTML response...');
        console.log('Response length:', html.length);

        // Parse the response and update only the table content
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newTableContainer = doc.querySelector('.invoices-table');

        if (newTableContainer) {
            const currentTableContainer = document.querySelector('.invoices-table');
            if (currentTableContainer) {
                console.log('Updating table content...');
                console.log('New table HTML:', newTableContainer.innerHTML.substring(0, 200) + '...');
                currentTableContainer.innerHTML = newTableContainer.innerHTML;

                // Re-export functions after DOM update
                window.selectRow = selectRow;
                window.sortTable = sortTable;
                window.filterByStatus = filterByStatus;
                window.confirmDelete = confirmDelete;
                window.shareInvoice = shareInvoice;

                console.log('Functions re-exported after table update');
            }
        } else {
            console.warn('New table container not found in response');
            console.log('Available elements in response:', doc.querySelectorAll('*').length);
            // Fallback to full page reload
            console.log('Falling back to form submission');
            submitSearchForm();
            return;
        }

        // Reset selected row
        selectedRow = null;

        // Remove loading state
        if (table) {
            table.classList.remove('loading');
        }

        console.log('AJAX search completed');
    })
    .catch(error => {
        console.error('Search error:', error);
        // Fallback to form submission
        console.log('Falling back to form submission');
        submitSearchForm();
    });
}

/**
 * Row selection functionality
 */
function selectRow(index) {
    // Remove previous selection
    if (selectedRow !== null) {
        const prevRow = document.querySelector(`tr[onclick="selectRow(${selectedRow})"]`);
        const prevActionRow = document.getElementById(`actionRow${selectedRow}`);
        if (prevRow) {
            prevRow.classList.remove('selected');
            prevRow.setAttribute('aria-selected', 'false');
        }
        if (prevActionRow) {
            prevActionRow.classList.remove('show');
            prevActionRow.setAttribute('aria-hidden', 'true');
        }
    }

    // Select new row
    const currentRow = document.querySelector(`tr[onclick="selectRow(${index})"]`);
    const currentActionRow = document.getElementById(`actionRow${index}`);
    
    if (selectedRow === index) {
        // Deselect if clicking the same row
        selectedRow = null;
        if (currentRow) {
            currentRow.classList.remove('selected');
            currentRow.setAttribute('aria-selected', 'false');
        }
        if (currentActionRow) {
            currentActionRow.classList.remove('show');
            currentActionRow.setAttribute('aria-hidden', 'true');
        }
    } else {
        // Select new row
        selectedRow = index;
        if (currentRow) {
            currentRow.classList.add('selected');
            currentRow.setAttribute('aria-selected', 'true');
        }
        if (currentActionRow) {
            currentActionRow.classList.add('show');
            currentActionRow.setAttribute('aria-hidden', 'false');
            
            // Scroll action row into view if needed
            setTimeout(() => {
                currentActionRow.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'nearest' 
                });
            }, 100);
        }
    }

    // Announce selection to screen readers
    announceSelection(index, currentRow);
}

/**
 * Announce row selection for accessibility
 */
function announceSelection(index, row) {
    if (!row) return;
    
    const invoiceNumber = row.getAttribute('data-invoice-number');
    const customerName = row.getAttribute('data-customer-name');
    
    const announcement = selectedRow === index 
        ? `Selected invoice ${invoiceNumber} for ${customerName}. Actions are now available.`
        : `Deselected invoice ${invoiceNumber}.`;
    
    // Create temporary announcement element
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    
    document.body.appendChild(announcer);
    setTimeout(() => {
        document.body.removeChild(announcer);
    }, 1000);
}

/**
 * Sorting functionality with three states (ASC, DESC, none)
 */
function sortTable(field) {
    const urlParams = new URLSearchParams(window.location.search);
    const currentSort = urlParams.get('sort');
    const currentOrder = urlParams.get('order');

    let newSort = field;
    let newOrder = 'ASC';

    if (currentSort === field) {
        if (currentOrder === 'ASC') {
            newOrder = 'DESC';
        } else if (currentOrder === 'DESC') {
            // Third state: deactivate sorting (go back to default) - only for non-date fields
            if (field === 'invoice_date') {
                // For date field, keep cycling between ASC and DESC
                newOrder = 'ASC';
            } else {
                // For other fields, go back to default (date DESC)
                newSort = 'invoice_date';
                newOrder = 'DESC';
            }
        }
    }

    const form = document.getElementById('searchForm');
    if (form) {
        form.querySelector('input[name="sort"]').value = newSort;
        form.querySelector('input[name="order"]').value = newOrder;

        // Add loading state
        const table = document.getElementById('invoicesTable');
        if (table) {
            table.classList.add('loading');
        }

        form.submit();
    }
}

/**
 * Filter by status functionality (cycles through status values)
 */
function filterByStatus() {
    console.log('filterByStatus called');
    const urlParams = new URLSearchParams(window.location.search);
    const currentStatus = urlParams.get('filter_status') || 'all';
    console.log('Current status:', currentStatus);

    // Status cycle: all -> paid -> unpaid -> partial -> cancelled -> all
    const statusCycle = ['all', 'paid', 'unpaid', 'partial', 'cancelled'];
    let currentIndex = statusCycle.indexOf(currentStatus);
    if (currentIndex === -1) currentIndex = 0;

    // Move to next in cycle
    const nextIndex = (currentIndex + 1) % statusCycle.length;
    const newStatus = statusCycle[nextIndex];
    console.log('New status:', newStatus);

    const form = document.getElementById('searchForm');
    if (form) {
        form.querySelector('input[name="filter_status"]').value = newStatus;

        // Add loading state
        const table = document.getElementById('invoicesTable');
        if (table) {
            table.classList.add('loading');
        }

        // Try AJAX first, fallback to form submission
        console.log('Trying AJAX first...');
        performAjaxSearch().catch(() => {
            console.log('AJAX failed, falling back to form submission...');
            form.submit();
        });
    } else {
        console.error('Search form not found');
    }
}

/**
 * Delete confirmation functionality
 */
function confirmDelete(invoiceId, invoiceNumber, customerName) {
    deleteInvoiceId = invoiceId;
    
    const modal = document.getElementById('deleteModal');
    const invoiceNumberSpan = document.getElementById('deleteInvoiceNumber');
    const customerNameSpan = document.getElementById('deleteCustomerName');
    
    if (invoiceNumberSpan) invoiceNumberSpan.textContent = invoiceNumber;
    if (customerNameSpan) customerNameSpan.textContent = customerName;
    
    if (modal) {
        modal.style.display = 'block';
        modal.setAttribute('aria-hidden', 'false');
        
        // Focus the cancel button for accessibility
        const cancelButton = modal.querySelector('.btn-cancel');
        if (cancelButton) {
            setTimeout(() => cancelButton.focus(), 100);
        }
    }
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
    }
    deleteInvoiceId = null;
}

function executeDelete() {
    if (deleteInvoiceId) {
        // Show loading state
        const modal = document.getElementById('deleteModal');
        const confirmBtn = modal.querySelector('.btn-confirm');
        const originalText = confirmBtn.innerHTML;

        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Excluindo...';

        // Create form data for deletion
        const formData = new FormData();
        formData.append('invoice_id', deleteInvoiceId);
        formData.append('confirm', 'yes');

        // Send delete request
        fetch('delete_invoice_api.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                showSuccessMessage('Fatura excluída com sucesso!');
                closeDeleteModal();

                // Reload page to update list
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                alert('Erro ao excluir fatura: ' + (data.message || 'Erro desconhecido'));
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Error deleting invoice:', error);
            alert('Erro ao excluir fatura. Tente novamente.');
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = originalText;
        });
    }
}

/**
 * Initialize modal functionality
 */
function initializeModalFunctionality() {
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('deleteModal');
        if (event.target === modal) {
            closeDeleteModal();
        }
    });

    // Close modal with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('deleteModal');
            if (modal && modal.style.display === 'block') {
                closeDeleteModal();
            }
        }
    });
}

/**
 * Initialize keyboard navigation
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Only handle keyboard navigation when not in input fields
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT') {
            return;
        }

        const rows = document.querySelectorAll('tr[onclick^="selectRow"]');
        if (rows.length === 0) return;

        let currentIndex = selectedRow;
        
        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex === null) {
                    selectRow(0);
                } else if (currentIndex < rows.length - 1) {
                    selectRow(currentIndex + 1);
                }
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex === null) {
                    selectRow(rows.length - 1);
                } else if (currentIndex > 0) {
                    selectRow(currentIndex - 1);
                }
                break;
                
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (currentIndex !== null) {
                    // Toggle selection
                    selectRow(currentIndex);
                }
                break;
        }
    });
}

/**
 * Utility function to show loading state
 */
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
    }
}

/**
 * Utility function to hide loading state
 */
function hideLoading(element) {
    if (element) {
        element.classList.remove('loading');
    }
}

/**
 * Handle form submission with loading state
 */
function handleFormSubmission(form) {
    if (form) {
        const table = document.getElementById('invoicesTable');
        showLoading(table);
        
        // Add a small delay to show loading state
        setTimeout(() => {
            form.submit();
        }, 100);
    }
}

/**
 * Share invoice functionality (similar to invoice_create.php success modal)
 */
function shareInvoice(invoiceId, invoiceNumber, customerName) {
    // Show loading
    const shareButtons = document.querySelectorAll('.btn-share');
    const currentButton = event.target.closest('.btn-share');
    const originalText = currentButton.innerHTML;

    currentButton.disabled = true;
    currentButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

    // Create shared link via API
    const formData = new FormData();
    formData.append('type', 'invoice');
    formData.append('item_id', invoiceId);
    formData.append('expires_days', 30);

    fetch('create_shared_link_api.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            shareInvoiceWithDeviceOptions(data.link_url, invoiceNumber, customerName);
        } else {
            alert('Error creating link: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error creating link:', error);
        alert('Error creating link. Please try again.');
    })
    .finally(() => {
        // Restore button
        currentButton.disabled = false;
        currentButton.innerHTML = originalText;
    });
}

/**
 * Share using device native sharing options
 */
function shareInvoiceWithDeviceOptions(shareUrl, invoiceNumber, customerName) {
    const webShareMessage = `Hello! Here is the link to view invoice ${invoiceNumber} for ${customerName}. This link expires in 30 days. Thank you!`;

    // Check if Web Share API is supported (mobile devices)
    if (navigator.share) {
        navigator.share({
            title: `Invoice ${invoiceNumber} - Tony's AC Repair`,
            text: webShareMessage,
            url: shareUrl
        }).catch(err => {
            console.log('Error sharing:', err);
            // Fallback to manual sharing options with full message including URL
            const fullMessage = `Hello! Here is the link to view invoice ${invoiceNumber} for ${customerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
            showManualShareOptions(shareUrl, fullMessage);
        });
    } else {
        // Fallback for desktop or unsupported browsers - include URL in message
        const fullMessage = `Hello! Here is the link to view invoice ${invoiceNumber} for ${customerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
        showManualShareOptions(shareUrl, fullMessage);
    }
}

/**
 * Show manual sharing options
 */
function showManualShareOptions(shareUrl, message) {
    // Create a temporary textarea to copy the message
    const textarea = document.createElement('textarea');
    textarea.value = message;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);

    showSuccessMessage('Link and message copied to clipboard! You can now paste it in your preferred messaging app.');
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'success-message';
    messageDiv.innerHTML = `
        <div class="alert alert-success" style="
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            position: fixed;
            top: 80px;
            left: 1rem;
            right: 1rem;
            z-index: 2001;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        ">
            <strong>✅ ${message}</strong>
            <button type="button" style="
                position: absolute;
                top: 8px;
                right: 12px;
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #155724;
            " onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    document.body.appendChild(messageDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 5000);
}

/**
 * Update clear button visibility based on active filters
 */
function updateClearButtonVisibility() {
    const searchInput = document.getElementById('searchInput');
    const clearBtn = document.getElementById('clearBtn');
    const urlParams = new URLSearchParams(window.location.search);
    const currentStatus = urlParams.get('filter_status') || 'all';

    if (!clearBtn) return;

    const hasSearch = searchInput && searchInput.value.trim() !== '';
    const hasStatusFilter = currentStatus !== 'all';

    if (hasSearch || hasStatusFilter) {
        clearBtn.style.display = 'flex';
    } else {
        clearBtn.style.display = 'none';
    }
}

/**
 * Clear all filters and return to initial state
 */
function clearAllFilters() {
    console.log('Clearing all filters...');

    // Reset to initial state: no search, no status filter, default sort
    const newUrl = window.location.pathname + '?search=&filter_status=all&sort=invoice_date&order=DESC';

    // Navigate to clean state
    window.location.href = newUrl;
}

// Export functions for global access IMMEDIATELY
console.log('Exporting functions to global scope...');
window.performAjaxSearch = performAjaxSearch;
window.selectRow = selectRow;
window.sortTable = sortTable;
window.filterByStatus = filterByStatus;
window.confirmDelete = confirmDelete;
window.closeDeleteModal = closeDeleteModal;
window.executeDelete = executeDelete;
window.shareInvoice = shareInvoice;
window.clearAllFilters = clearAllFilters;
window.updateClearButtonVisibility = updateClearButtonVisibility;

// Debug: verify functions are exported
console.log('Functions exported:', {
    performAjaxSearch: typeof window.performAjaxSearch,
    selectRow: typeof window.selectRow,
    sortTable: typeof window.sortTable,
    filterByStatus: typeof window.filterByStatus,
    confirmDelete: typeof window.confirmDelete,
    closeDeleteModal: typeof window.closeDeleteModal,
    executeDelete: typeof window.executeDelete,
    shareInvoice: typeof window.shareInvoice
});

// Test function availability immediately
console.log('Testing filterByStatus availability:', typeof filterByStatus);
console.log('Testing window.filterByStatus availability:', typeof window.filterByStatus);
