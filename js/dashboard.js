// Dashboard JavaScript for NOVO Sistema

document.addEventListener('DOMContentLoaded', function() {
    // Responsividade para tabelas
    const tables = document.querySelectorAll('.data-table');
    tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive';
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });

    // Ajuste da altura do sidebar em mobile e ajuste do padding do body
    function adjustSidebarHeight() {
        const sidebar = document.querySelector('.sidebar');
        if (window.innerWidth <= 576) {
            // Garante que o sidebar tenha altura automática
            sidebar.style.height = 'auto';

            // Calcula a altura real do sidebar
            const sidebarHeight = sidebar.offsetHeight;

            // Define a variável CSS personalizada para a altura do sidebar
            document.documentElement.style.setProperty('--sidebar-mobile-height', sidebarHeight + 'px');

            // Ajusta o padding-top do body para evitar que o conteúdo fique escondido
            document.body.style.paddingTop = sidebarHeight + 'px';
        } else {
            // Em telas maiores, restaura os valores padrão
            sidebar.style.height = '100vh';
            document.body.style.paddingTop = '0';
            document.documentElement.style.setProperty('--sidebar-mobile-height', '0px');
        }
    }

    // Executar ajustes no carregamento e no redimensionamento
    adjustSidebarHeight();
    window.addEventListener('resize', adjustSidebarHeight);
    window.addEventListener('orientationchange', adjustSidebarHeight);

    // Também ajustar após um pequeno atraso para garantir que todos os elementos foram carregados
    setTimeout(adjustSidebarHeight, 500);

    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initializeCharts();
    }

    // Initialize data tables if available
    initializeDataTables();
});

function initializeCharts() {
    // Sales Chart
    const salesChartCanvas = document.getElementById('salesChart');
    if (salesChartCanvas) {
        const salesChart = new Chart(salesChartCanvas, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
                datasets: [{
                    label: 'Vendas',
                    data: salesData || [12000, 19000, 15000, 25000, 22000, 30000, 28000, 26000, 29000, 32000, 35000, 38000],
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderColor: '#3498db',
                    borderWidth: 2,
                    tension: 0.4,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#3498db',
                    pointBorderWidth: 2,
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'R$ ' + context.parsed.y.toLocaleString('pt-BR');
                            }
                        }
                    }
                }
            }
        });
    }

    // Quotes vs Invoices Chart
    const quotesInvoicesCanvas = document.getElementById('quotesInvoicesChart');
    if (quotesInvoicesCanvas) {
        const quotesInvoicesChart = new Chart(quotesInvoicesCanvas, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
                datasets: [
                    {
                        label: 'Orçamentos',
                        data: quotesData || [15, 20, 18, 25, 22, 30],
                        backgroundColor: '#f39c12',
                        borderRadius: 5
                    },
                    {
                        label: 'Faturas',
                        data: invoicesData || [12, 17, 15, 22, 20, 25],
                        backgroundColor: '#3498db',
                        borderRadius: 5
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    // Top Products Chart
    const topProductsCanvas = document.getElementById('topProductsChart');
    if (topProductsCanvas) {
        const topProductsChart = new Chart(topProductsCanvas, {
            type: 'doughnut',
            data: {
                labels: topProductsLabels || ['Produto A', 'Produto B', 'Produto C', 'Produto D', 'Outros'],
                datasets: [{
                    data: topProductsData || [35, 25, 15, 10, 15],
                    backgroundColor: [
                        '#3498db',
                        '#2ecc71',
                        '#f39c12',
                        '#e74c3c',
                        '#95a5a6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    }
}

function initializeDataTables() {
    // Add DataTables functionality if available
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.data-table').DataTable({
            responsive: true,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Portuguese-Brasil.json'
            },
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50],
            dom: '<"top"fl>rt<"bottom"ip>',
            ordering: true
        });
    }
}

// Filter dashboard data by date range
function filterDashboardData(range) {
    // This function would update the charts based on the selected date range
    // For demonstration purposes, we'll just log the selected range
    console.log('Filtering dashboard data by range:', range);

    // In a real implementation, you would make an AJAX request to get new data
    // and then update the charts

    // Example:
    // fetch(`/api/dashboard-data?range=${range}`)
    //     .then(response => response.json())
    //     .then(data => {
    //         // Update charts with new data
    //         updateCharts(data);
    //     });
}

// Update charts with new data
function updateCharts(data) {
    // This function would update all charts with new data
    // For demonstration purposes, we'll just log the data
    console.log('Updating charts with new data:', data);

    // In a real implementation, you would update each chart
    // Example:
    // salesChart.data.datasets[0].data = data.salesData;
    // salesChart.update();
}
