<?php
// Habilitar exibição de erros para depuração
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir conexão com o banco de dados
require_once 'db_connect.php';

// Não tentamos iniciar a sessão aqui para evitar os erros

// Verificar se o usuário Tony existe e tem a role de administrador
$tony_is_admin = false;
$query = "SELECT u.id, u.username, u.email, u.role_id, ur.name as role_name
          FROM users u
          LEFT JOIN user_roles ur ON u.role_id = ur.id
          WHERE (u.username = 'Tony' OR u.email = '<EMAIL>')";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    $tony_user = $result->fetch_assoc();
    if (isset($tony_user['role_name']) && strtolower($tony_user['role_name']) === 'administrator') {
        $tony_is_admin = true;
    }
}

// Verificar se existe algum administrador no sistema
$admin_exists = false;
$query = "SELECT 1 FROM users u
          JOIN user_roles ur ON u.role_id = ur.id
          WHERE ur.name = 'administrator'
          LIMIT 1";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $admin_exists = true;
}

// Determinar se devemos mostrar os links de administração
$show_admin_links = $tony_is_admin || !$admin_exists;
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu de Administração Direto</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .admin-menu {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .menu-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .menu-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .menu-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .menu-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }
        .menu-card-body {
            padding: 20px;
        }
        .menu-card-description {
            color: #6c757d;
            margin-bottom: 20px;
        }
        .menu-card-link {
            display: inline-block;
            background-color: #0d6efd;
            color: #fff;
            padding: 10px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .menu-card-link:hover {
            background-color: #0b5ed7;
            color: #fff;
        }
        .menu-card-icon {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }
        .not-available {
            opacity: 0.5;
            pointer-events: none;
        }
        .not-available .menu-card-link {
            background-color: #6c757d;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-admin {
            background-color: #cfe2ff;
            color: #0d6efd;
        }
        .status-no-admin {
            background-color: #f8d7da;
            color: #dc3545;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            display: inline-block;
            background-color: #3498db;
            color: #fff;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Menu de Administração Direto</h1>
            <div>
                <a href="dashboard_simple_fixed.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar para o Dashboard
                </a>
            </div>
        </div>

        <div class="alert alert-warning">
            <strong>Atenção!</strong> Esta é uma versão alternativa do menu de administração que não depende de sessões PHP.
            Use esta página se estiver enfrentando problemas com o menu normal.
        </div>

        <?php if ($tony_is_admin): ?>
            <div class="alert alert-info">
                <strong>Status:</strong> O usuário Tony tem a role de administrador no banco de dados.
                Se você estiver logado como Tony, você deve ter acesso a todas as funcionalidades de administração.
            </div>
        <?php else: ?>
            <div class="alert alert-danger">
                <strong>Status:</strong> O usuário Tony NÃO tem a role de administrador no banco de dados.
                <a href="fix_tony_user.php" class="btn">Corrigir Usuário Tony</a>
            </div>
        <?php endif; ?>

        <h2>Links Diretos para Páginas de Administração</h2>
        <p>Estes links ignoram as verificações de permissão e levam você diretamente às páginas de administração:</p>

        <div class="admin-menu">
            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-users-cog menu-card-icon"></i>
                        Gerenciar Usuários
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Adicione, edite ou remova usuários do sistema. Gerencie informações de conta e credenciais.
                    </p>
                    <div>
                        <a href="admin_users.php" class="menu-card-link">
                            <i class="fas fa-users"></i> Listar Usuários
                        </a>
                        <a href="admin_user_add.php" class="menu-card-link">
                            <i class="fas fa-user-plus"></i> Adicionar Usuário
                        </a>
                    </div>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-user-tag menu-card-icon"></i>
                        Gerenciar Permissões
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Configure as permissões para cada role no sistema. Defina quais ações cada tipo de usuário pode realizar.
                    </p>
                    <div>
                        <a href="admin_roles.php" class="menu-card-link">
                            <i class="fas fa-shield-alt"></i> Listar Roles
                        </a>
                        <a href="admin_role_edit.php" class="menu-card-link">
                            <i class="fas fa-edit"></i> Editar Permissões
                        </a>
                    </div>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-user-shield menu-card-icon"></i>
                        Atribuir Roles
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Atribua roles (funções) aos usuários do sistema. Defina quem é administrador, gerente ou usuário regular.
                    </p>
                    <a href="admin_manage_roles.php" class="menu-card-link">
                        <i class="fas fa-user-edit"></i> Gerenciar Roles de Usuários
                    </a>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-key menu-card-icon"></i>
                        Visualizar Permissões
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Veja todas as permissões atribuídas a cada role no sistema. Visualize quais ações cada tipo de usuário pode realizar.
                    </p>
                    <a href="admin_view_permissions.php" class="menu-card-link">
                        <i class="fas fa-eye"></i> Ver Permissões por Role
                    </a>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-tools menu-card-icon"></i>
                        Ferramentas de Diagnóstico
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Ferramentas para diagnosticar e corrigir problemas no sistema de usuários e permissões.
                    </p>
                    <div>
                        <a href="fix_admin_user.php" class="menu-card-link">
                            <i class="fas fa-user-cog"></i> Corrigir Usuário Admin
                        </a>
                        <a href="create_custom_admin.php" class="menu-card-link" style="background-color: #2ecc71;">
                            <i class="fas fa-user-plus"></i> Criar Admin Personalizado
                        </a>
                        <a href="fix_tony_user.php" class="menu-card-link" style="background-color: #e74c3c;">
                            <i class="fas fa-user-check"></i> Corrigir Usuário Tony
                        </a>
                    </div>
                </div>
            </div>

            <div class="menu-card">
                <div class="menu-card-header">
                    <h2 class="menu-card-title">
                        <i class="fas fa-database menu-card-icon"></i>
                        Banco de Dados
                    </h2>
                </div>
                <div class="menu-card-body">
                    <p class="menu-card-description">
                        Ferramentas para verificar e corrigir problemas nas tabelas do banco de dados.
                    </p>
                    <div>
                        <a href="fix_database_tables.php" class="menu-card-link">
                            <i class="fas fa-table"></i> Corrigir Tabelas
                        </a>
                        <a href="check_admin_status.php" class="menu-card-link">
                            <i class="fas fa-info-circle"></i> Verificar Status
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <h2>Solução para Problemas de Sessão</h2>
        <div class="alert alert-info">
            <p>Os erros que você está vendo estão relacionados a problemas com as sessões PHP no servidor. Isso pode acontecer por várias razões:</p>
            <ul>
                <li>O diretório de sessões PHP não existe ou não tem permissões corretas</li>
                <li>Há um problema com a configuração do PHP no servidor</li>
                <li>A sessão expirou ou foi corrompida</li>
            </ul>
            <p>Para resolver este problema, você pode:</p>
            <ol>
                <li>Usar os links diretos acima, que não dependem de sessões</li>
                <li>Limpar os cookies do navegador e fazer login novamente</li>
                <li>Contatar o administrador do servidor para verificar as permissões do diretório de sessões</li>
            </ol>
        </div>
    </div>
</body>
</html>
<?php
// Fechar conexão
if (isset($conn) && $conn instanceof mysqli) {
    $conn->close();
}
?>
