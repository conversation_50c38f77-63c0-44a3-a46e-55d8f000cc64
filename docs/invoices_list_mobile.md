# Invoices List Mobile - Documentação (Atualizada)

## Visão Geral

A página `invoices_list.php` é uma versão otimizada para mobile da listagem de faturas, projetada especificamente para dispositivos móveis com foco na usabilidade e performance. Esta versão substitui completamente a página `invoices.php` em todo o projeto.

## Mudanças Implementadas

### 1. Redirecionamentos Atualizados
- **Todos os links** que direcionavam para `invoices.php` agora direcionam para `invoices_list.php`
- **Links de visualização** (`invoice_view.php`) foram alterados para `generate_invoice_pdf.php`
- **Menus e sidebars** atualizados em todo o projeto

### 2. Funcionalidades dos Botões de Ação
- **Botão Print removido** das ações da fatura
- **Botão Share** implementado com funcionalidade igual ao modal de sucesso do `invoice_create.php`
- **Botão Delete** executa exclusão direta via API sem redirecionamento
- **Botões em linha única** lado a lado em mobile

### 3. Campo de Busca Melhorado
- **Busca sempre ativa** - mantém foco durante digitação
- **Busca via AJAX** - sem recarregar página
- **Ícone de lupa** visível antes do placeholder
- **Borda normal e visível** no campo de busca

### 4. Ordenação Avançada
- **Três fases de ordenação**: crescente, decrescente, desativado
- **Status com ciclo especial**: paid → unpaid → partial → cancelled → status
- **Título dinâmico** no cabeçalho Status que muda conforme ordenação

## Funcionalidades Principais

### 1. Cabeçalho e Menu
- **Cabeçalho fixo** com o nome da empresa e botão de menu hamburger
- **Menu deslizante** com navegação para todas as seções do sistema
- **Responsivo** e otimizado para toque

### 2. Busca Dinâmica
- **Campo de busca em tempo real** com debounce de 500ms
- **Filtros por campo específico**:
  - Todos os campos
  - Número da fatura
  - Nome do cliente
  - Endereço do cliente
  - Total
  - Status
- **Filtro por status**:
  - Todos os status
  - Unpaid (Não pago)
  - Paid (Pago)
  - Partial (Parcial)
  - Cancelled (Cancelado)

### 3. Tabela Responsiva
- **Fonte de 8px** para evitar transbordo na tela
- **Colunas visíveis**:
  - ID (Número da fatura)
  - Date (Data no formato mm/dd/yy)
  - Name (Nome do cliente)
  - Address (Endereço truncado)
  - Total (Valor formatado)
  - Status (Com cores diferenciadas)

### 4. Ordenação Clicável
- **Clique nos cabeçalhos** para ordenar:
  - **Data**: Mais recentes ↔ Mais antigas
  - **Status**: Unpaid → Paid → Partial → All
  - **Total**: Menor → Maior valor
  - **Nome**: A-Z ↔ Z-A
  - **ID**: Crescente ↔ Decrescente

### 5. Seleção de Linha e Ações
- **Clique em qualquer linha** para selecioná-la
- **Linha de ações aparece** abaixo da linha selecionada
- **Botões de ação disponíveis**:
  - **View**: Visualizar fatura
  - **Edit**: Editar fatura
  - **Payment**: Gerenciar pagamentos
  - **Print**: Imprimir fatura
  - **Share**: Compartilhar link
  - **Delete**: Excluir com confirmação

### 6. Confirmação de Exclusão
- **Modal de confirmação** ao tentar excluir
- **Exibe informações** da fatura:
  - Nome do cliente
  - Número da fatura
- **Botões de confirmação** ou cancelamento

## Estrutura de Arquivos

```
invoices_list.php                # Arquivo principal da página
invoices_list_table_only.php     # Retorna apenas tabela para AJAX
css/invoices-list-mobile.css     # Estilos específicos para mobile
js/invoices-list-mobile.js       # JavaScript para funcionalidades
delete_invoice_api.php           # API para exclusão de faturas
docs/invoices_list_mobile.md    # Esta documentação
```

### Novos Arquivos Criados
- **`delete_invoice_api.php`**: API REST para exclusão segura de faturas
- **`invoices_list_table_only.php`**: Template para atualizações AJAX da tabela
- **CSS e JS modulares**: Separação completa de responsabilidades

## Tecnologias Utilizadas

### Backend (PHP)
- **Busca dinâmica** com SQL LIKE
- **Filtros combinados** (campo + status)
- **Ordenação segura** com whitelist de campos
- **Escape de dados** para prevenir SQL injection
- **JOIN com tabela customers** para dados completos

### Frontend (CSS)
- **Mobile-first design**
- **Flexbox e Grid** para layouts responsivos
- **Animações suaves** para melhor UX
- **Estados visuais** para feedback do usuário
- **Acessibilidade** com foco e contraste

### JavaScript
- **Busca com debounce** para performance
- **Seleção de linha** com estados visuais
- **Modal de confirmação** com acessibilidade
- **Navegação por teclado** (setas, Enter, ESC)
- **Menu hamburger** responsivo

## Responsividade

### Breakpoints
- **768px**: Tablet e desktop pequeno
- **480px**: Smartphones grandes
- **360px**: Smartphones pequenos

### Adaptações por Tamanho
- **> 768px**: Layout desktop com menu lateral
- **≤ 768px**: Menu hamburger, tabela compacta
- **≤ 480px**: Filtros em coluna, botões empilhados
- **≤ 360px**: Fonte ainda menor, padding reduzido

## Acessibilidade

### Recursos Implementados
- **ARIA labels** para elementos interativos
- **Navegação por teclado** completa
- **Anúncios para screen readers** em seleções
- **Contraste adequado** para textos
- **Foco visível** em todos os elementos
- **Suporte a modo de alto contraste**
- **Redução de movimento** quando solicitado

### Navegação por Teclado
- **Setas ↑↓**: Navegar entre linhas
- **Enter/Espaço**: Selecionar/deselecionar linha
- **ESC**: Fechar modal ou menu
- **Tab**: Navegar entre elementos focáveis

## Performance

### Otimizações
- **Debounce na busca** (500ms)
- **CSS e JS externos** para cache
- **Consultas SQL otimizadas** com índices
- **Carregamento progressivo** com estados visuais
- **Animações CSS** em vez de JavaScript

### Métricas Esperadas
- **First Paint**: < 1s
- **Time to Interactive**: < 2s
- **Lighthouse Score**: > 90 (mobile)

## Segurança

### Medidas Implementadas
- **Escape de dados** em todas as saídas
- **Prepared statements** para consultas dinâmicas
- **Validação de entrada** nos filtros
- **Whitelist de campos** para ordenação
- **Sanitização de parâmetros** GET

## Manutenção

### Estrutura Modular
- **Separação de responsabilidades**:
  - PHP: Lógica de negócio e dados
  - CSS: Apresentação e layout
  - JS: Interatividade e UX

### Facilidade de Modificação
- **Variáveis CSS** para cores e espaçamentos
- **Funções JavaScript** bem documentadas
- **Consultas SQL** parametrizadas
- **Comentários explicativos** em código complexo

## Uso

### Acesso
1. Navegue para `invoices_list.php`
2. Use o menu hamburger para navegar
3. Digite na busca para filtrar resultados
4. Clique nos cabeçalhos para ordenar
5. Selecione uma linha para ver ações
6. Use os botões de ação conforme necessário

### Dicas de Uso
- **Busca inteligente**: Digite qualquer parte do que procura
- **Filtros combinados**: Use campo + status para busca precisa
- **Seleção visual**: Linha azul indica seleção ativa
- **Ações contextuais**: Botões aparecem apenas quando necessário
- **Confirmação segura**: Sempre confirme exclusões

## Troubleshooting

### Problemas Comuns
1. **Tabela não carrega**: Verificar conexão com banco
2. **Busca não funciona**: Verificar JavaScript habilitado
3. **Layout quebrado**: Verificar CSS carregado
4. **Ações não aparecem**: Verificar seleção de linha
5. **Modal não abre**: Verificar JavaScript e CSS

### Logs e Debug
- **Erros PHP**: Verificar logs do servidor
- **Erros JavaScript**: Abrir console do navegador
- **Problemas CSS**: Usar DevTools para inspecionar
- **Consultas SQL**: Ativar log de queries no banco

## Futuras Melhorias

### Funcionalidades Planejadas
- **Paginação** para grandes volumes
- **Exportação** para PDF/Excel
- **Filtros avançados** por data
- **Busca por código de barras**
- **Modo offline** com cache
- **Notificações push** para atualizações

### Otimizações Técnicas
- **Virtual scrolling** para performance
- **Service Worker** para cache
- **Lazy loading** de imagens
- **Compressão** de assets
- **CDN** para recursos estáticos
