<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Verificar se estamos usando o modo bypass
$bypass_auth = isset($_GET['bypass']) && $_GET['bypass'] === 'true';

if (!$bypass_auth) {
    // Include session check
    require_once 'check_session.php';

    // Include permissions helper
    require_once 'includes/permissions.php';

    // Verificar se o usuário é administrador
    if (!user_has_role('administrator')) {
        header('Location: dashboard_simple_fixed.php');
        exit;
    }
} else {
    // Modo bypass - exibir aviso
    echo '<div style="background-color: #fff3cd; color: #856404; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #ffeeba;">
            <strong>Atenção!</strong> Você está acessando esta página no modo bypass, ignorando as verificações de permissão.
          </div>';
}

// Initialize variables
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$username = '';
$email = '';
$role_id = '';
$message = '';
$message_type = '';
$roles = [];
$user = null;

// No modo bypass, sempre permitir acesso
if ($bypass_auth) {
    $admin_exists = true;
    if (!function_exists('user_has_role')) {
        // Definir a função user_has_role para compatibilidade
        function user_has_role($role) {
            return true;
        }
    }
} else {
    // Check if admin user exists
    $admin_exists = false;

    // Primeiro, verificar se a coluna 'name' existe na tabela user_roles
    $check_column = $conn->query("SHOW COLUMNS FROM user_roles LIKE 'name'");
    if ($check_column && $check_column->num_rows > 0) {
        // Usar a consulta original se a coluna existir
        $query = "SELECT 1 FROM users u
                  JOIN user_roles ur ON u.role_id = ur.id
                  WHERE ur.name = 'administrator'
                  LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    } else {
        // Usar uma consulta alternativa se a coluna não existir
        // Verificar se há algum usuário com role_id = 1 (assumindo que 1 é o ID do administrador)
        $query = "SELECT 1 FROM users WHERE role_id = 1 LIMIT 1";
        $result = $conn->query($query);
        if ($result && $result->num_rows > 0) {
            $admin_exists = true;
        }
    }
}

// Check if user is admin or if no admin exists yet
$is_admin = user_has_role('administrator');
$allow_access = $is_admin || !$admin_exists;

// If access is not allowed, redirect to dashboard
if (!$allow_access) {
    header('Location: dashboard_simple.php');
    exit;
}

// Check if user exists
if ($user_id > 0) {
    $query = "SELECT u.id, u.username, u.email, u.role_id, ur.name as role_name
              FROM users u
              LEFT JOIN user_roles ur ON u.role_id = ur.id
              WHERE u.id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $username = $user['username'];
        $email = $user['email'];
        $role_id = $user['role_id'];
    } else {
        // User not found, redirect to user list
        header('Location: admin_users.php');
        exit;
    }
} else {
    // No user ID provided, redirect to user list
    header('Location: admin_users.php');
    exit;
}

// Get all roles
$query = "SELECT id, name, description FROM user_roles ORDER BY name";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $roles[] = $row;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role_id = (int)($_POST['role_id'] ?? 0);

    // Validate input
    $errors = [];

    if (empty($username)) {
        $errors[] = "Username is required.";
    } else {
        // Check if username already exists (excluding current user)
        $query = "SELECT 1 FROM users WHERE username = ? AND id != ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('si', $username, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $errors[] = "Username already exists.";
        }
    }

    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format.";
    } else {
        // Check if email already exists (excluding current user)
        $query = "SELECT 1 FROM users WHERE email = ? AND id != ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('si', $email, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $errors[] = "Email already exists.";
        }
    }

    // Password is optional when editing
    if (!empty($password) && strlen($password) < 6) {
        $errors[] = "Password must be at least 6 characters.";
    } elseif (!empty($password) && $password !== $confirm_password) {
        $errors[] = "Passwords do not match.";
    }

    if ($role_id <= 0) {
        $errors[] = "Please select a role.";
    }

    // Special protection for administrator users
    $is_admin_user = false;
    $query = "SELECT 1 FROM users u
              JOIN user_roles ur ON u.role_id = ur.id
              WHERE u.id = ? AND ur.name = 'administrator'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $result->num_rows > 0) {
        $is_admin_user = true;
    }

    // If this is the only admin user, don't allow changing the role
    if ($is_admin_user) {
        $query = "SELECT COUNT(*) as admin_count FROM users u
                  JOIN user_roles ur ON u.role_id = ur.id
                  WHERE ur.name = 'administrator'";
        $result = $conn->query($query);
        if ($result && $row = $result->fetch_assoc()) {
            $admin_count = (int)$row['admin_count'];

            // Get the role name for the selected role_id
            $new_role_name = '';
            $query = "SELECT name FROM user_roles WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('i', $role_id);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result && $row = $result->fetch_assoc()) {
                $new_role_name = $row['name'];
            }

            if ($admin_count === 1 && $new_role_name !== 'administrator') {
                $errors[] = "Cannot change the role of the only administrator user.";
            }
        }
    }

    // If no errors, update the user
    if (empty($errors)) {
        if (!empty($password)) {
            // Update with new password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $query = "UPDATE users SET username = ?, email = ?, password = ?, role_id = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('sssis', $username, $email, $hashed_password, $role_id, $user_id);
        } else {
            // Update without changing password
            $query = "UPDATE users SET username = ?, email = ?, role_id = ? WHERE id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('ssis', $username, $email, $role_id, $user_id);
        }

        if ($stmt->execute()) {
            $message = "User updated successfully.";
            $message_type = 'success';
        } else {
            $message = "Error updating user: " . $conn->error;
            $message_type = 'error';
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit User</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 16px;
        }

        .form-control:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .password-strength {
            margin-top: 5px;
            height: 5px;
            border-radius: 2px;
            background-color: #e9ecef;
            overflow: hidden;
        }

        .password-strength-meter {
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .strength-weak {
            background-color: #dc3545;
            width: 33%;
        }

        .strength-medium {
            background-color: #ffc107;
            width: 66%;
        }

        .strength-strong {
            background-color: #28a745;
            width: 100%;
        }

        .password-feedback {
            font-size: 12px;
            margin-top: 5px;
        }

        .required-field::after {
            content: " *";
            color: #dc3545;
        }

        .form-note {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">Edit User</h1>
                    <a href="admin_users.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="message <?php echo $message_type === 'success' ? 'message-success' : 'message-error'; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">User Information</h2>
                    </div>
                    <div class="admin-card-body">
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="username" class="form-label required-field">Username</label>
                                <input type="text" id="username" name="username" class="form-control" value="<?php echo htmlspecialchars($username); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label required-field">Email</label>
                                <input type="email" id="email" name="email" class="form-control" value="<?php echo htmlspecialchars($email); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" id="password" name="password" class="form-control">
                                <div class="password-strength">
                                    <div id="password-strength-meter" class="password-strength-meter"></div>
                                </div>
                                <div id="password-feedback" class="password-feedback"></div>
                                <div class="form-note">Leave blank to keep the current password.</div>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password" class="form-label">Confirm Password</label>
                                <input type="password" id="confirm_password" name="confirm_password" class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="role_id" class="form-label required-field">Role</label>
                                <select id="role_id" name="role_id" class="form-control" required>
                                    <option value="">Select a role</option>
                                    <?php foreach ($roles as $role): ?>
                                        <option value="<?php echo $role['id']; ?>" <?php echo ($role_id == $role['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($role['name']); ?> - <?php echo htmlspecialchars($role['description']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-actions">
                                <a href="admin_users.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update User</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        // Password strength meter
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const strengthMeter = document.getElementById('password-strength-meter');
            const feedback = document.getElementById('password-feedback');

            passwordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                let strength = 0;
                let feedback_text = '';

                if (password.length >= 6) {
                    strength += 1;
                }

                if (password.match(/[A-Z]/)) {
                    strength += 1;
                }

                if (password.match(/[0-9]/)) {
                    strength += 1;
                }

                if (password.match(/[^A-Za-z0-9]/)) {
                    strength += 1;
                }

                // Update the strength meter
                strengthMeter.className = 'password-strength-meter';

                if (password.length === 0) {
                    strengthMeter.style.width = '0%';
                    feedback.textContent = '';
                } else if (strength < 2) {
                    strengthMeter.classList.add('strength-weak');
                    feedback.textContent = 'Weak password';
                    feedback.style.color = '#dc3545';
                } else if (strength < 4) {
                    strengthMeter.classList.add('strength-medium');
                    feedback.textContent = 'Medium strength password';
                    feedback.style.color = '#ffc107';
                } else {
                    strengthMeter.classList.add('strength-strong');
                    feedback.textContent = 'Strong password';
                    feedback.style.color = '#28a745';
                }
            });

            // Check password match
            const confirmInput = document.getElementById('confirm_password');
            confirmInput.addEventListener('input', function() {
                if (passwordInput.value !== confirmInput.value) {
                    confirmInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmInput.setCustomValidity('');
                }
            });
        });
    </script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
