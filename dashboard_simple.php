<?php
// Include session check
require_once 'check_session.php';

// Include database connection
require_once 'db_connect.php';

// Determina qual página está ativa
$current_page = basename($_SERVER['PHP_SELF']);

// Get dashboard statistics
$stats = [
    'customers' => 0,
    'inventory' => 0,
    'quotes' => 0,
    'invoices' => 0,
    'pending_quotes' => 0,
    'unpaid_invoices' => 0
];

// Count customers
$result = db_query("SELECT COUNT(*) as count FROM customers");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['customers'] = $row['count'];
}

// Count inventory items
$result = db_query("SELECT COUNT(*) as count FROM inventory");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['inventory'] = $row['count'];
}

// Count quotes
$result = db_query("SELECT COUNT(*) as count FROM quotes");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['quotes'] = $row['count'];
}

// Count invoices
$result = db_query("SELECT COUNT(*) as count FROM invoices");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['invoices'] = $row['count'];
}

// Count pending quotes
$result = db_query("SELECT COUNT(*) as count FROM quotes WHERE status = 'pending'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['pending_quotes'] = $row['count'];
}

// Count unpaid invoices
$result = db_query("SELECT COUNT(*) as count FROM invoices WHERE status = 'unpaid'");
if ($result && $row = db_fetch_assoc($result)) {
    $stats['unpaid_invoices'] = $row['count'];
}

// Get recent invoices
$recent_invoices = [];
$result = db_query("
    SELECT i.*, c.name as customer_name
    FROM invoices i
    JOIN customers c ON i.customer_id = c.id
    ORDER BY i.created_at DESC
    LIMIT 5
");
if ($result && db_num_rows($result) > 0) {
    $recent_invoices = db_fetch_all($result);
}

// Get latest quote for PDF test and modal test
$latest_quote_id = null;
$latest_quote = null;
$result = db_query("
    SELECT q.*, c.name as customer_name
    FROM quotes q
    JOIN customers c ON q.customer_id = c.id
    ORDER BY q.created_at DESC
    LIMIT 1
");
if ($result && $row = db_fetch_assoc($result)) {
    $latest_quote_id = $row['id'];
    $latest_quote = $row;
}
?>
<?php
// Verificar se o arquivo existe antes de incluir
if (file_exists('includes/header.php')) {
    include 'includes/header.php';
} else {
    // Header básico se o arquivo não existir
    echo '<!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="color-scheme" content="light">
        <title>Dashboard - Tony\'s AC Repair</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            /* Force light mode - prevent dark mode activation on iPhone */
            :root { color-scheme: light !important; }
            html { color-scheme: light !important; }
            body { color-scheme: light !important; background-color: #f8f9fa !important; color: #333 !important; }
        </style>
    </head>';
}
?>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard_simple-layout.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Modal Styles -->
    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fefefe;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            color: #28a745;
            font-size: 1.5rem;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 10px 20px 20px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            flex-wrap: wrap;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover,
        .close:focus {
            color: #000;
            text-decoration: none;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.2s;
        }

        .modal .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .modal .btn-primary:hover {
            background-color: #0056b3;
        }

        .modal .btn-success {
            background-color: #28a745;
            color: white;
        }

        .modal .btn-success:hover {
            background-color: #1e7e34;
        }

        .modal .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .modal .btn-secondary:hover {
            background-color: #545b62;
        }

        @media (max-width: 576px) {
            .modal-content {
                width: 95%;
                margin: 10px;
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <h1>Tony's AC Repair</h1>
        <button class="menu-toggle" id="menuToggle" aria-expanded="false" aria-controls="slideMenu" aria-label="Toggle navigation menu">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Slide Menu -->
    <nav class="slide-menu" id="slideMenu" role="navigation" aria-label="Main navigation">
        <div class="menu-grid">
            <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">
                <i class="fa fa-home"></i>
                <span>Painel</span>
            </a>
            <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">
                <i class="fa fa-users"></i>
                <span>Clientes</span>
            </a>
            <?php if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) !== 'user'): ?>
            <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">
                <i class="fa fa-box"></i>
                <span>Estoque</span>
            </a>
            <?php endif; ?>
            <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice"></i>
                <span>Orçamentos</span>
            </a>
            <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice-dollar"></i>
                <span>Faturas</span>
            </a>
            <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php') ? 'active' : ''; ?>">
                <i class="fa fa-headset"></i>
                <span>Chamados</span>
            </a>
            <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">
                <i class="fa fa-share-alt"></i>
                <span>Links</span>
            </a>
            <?php if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) !== 'user'): ?>
            <a href="reports.php" class="menu-item <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>">
                <i class="fa fa-chart-bar"></i>
                <span>Relatórios</span>
            </a>
            <?php endif; ?>
            <a href="logout.php" class="menu-item">
                <i class="fa fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </nav>

    <!-- Menu Overlay -->
    <div class="menu-overlay" id="menuOverlay" aria-hidden="true"></div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Quick Access Grid -->
        <section class="quick-access">
            <h2>Acesso Rápido</h2>
            <div class="quick-access-grid">
                <a href="customer_create.php" class="quick-btn btn-info">
                    <i class="fas fa-user-plus"></i>
                    <span>Novo Cliente</span>
                </a>
                <a href="inventory_create.php" class="quick-btn btn-warning">
                    <i class="fas fa-box-open"></i>
                    <span>Novo Produto</span>
                </a>
                <a href="quote_create.php" class="quick-btn btn-success">
                    <i class="fas fa-file-invoice"></i>
                    <span>Novo Orçamento</span>
                </a>
                <a href="invoice_create.php" class="quick-btn btn-primary">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>Nova Fatura</span>
                </a>
                <a href="reports.php" class="quick-btn btn-danger">
                    <i class="fas fa-chart-bar"></i>
                    <span>Relatórios</span>
                </a>
                <a href="service_call_create.php" class="quick-btn btn-purple">
                    <i class="fas fa-headset"></i>
                    <span>Novo Chamado</span>
                </a>
                <?php if ($latest_quote_id): ?>
                <a href="generate_quote_pdf.php?id=<?php echo $latest_quote_id; ?>" class="quick-btn btn-secondary" target="_blank" title="Testar layout do PDF com o último orçamento criado">
                    <i class="fas fa-file-pdf"></i>
                    <span>Teste PDF</span>
                </a>
                <button onclick="testQuoteSuccessModal()" class="quick-btn btn-success" title="Testar modal de sucesso do orçamento">
                    <i class="fas fa-vial"></i>
                    <span>Test Modal</span>
                </button>
                <?php endif; ?>
            </div>
        </section>

        <!-- Recent Invoices -->
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">Recent Invoices</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                <table class="table table-bordered table-striped data-table">
                    <thead>
                        <tr>
                            <th>Number</th>
                            <th>Customer</th>
                            <th class="hide-xs">Date</th>
                            <th class="hide-xs">Due Date</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($recent_invoices)): ?>
                            <tr>
                                <td colspan="7" class="text-center">No invoices found.</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($recent_invoices as $invoice): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                    <td><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                    <td class="hide-xs"><?php echo date('d/m/Y', strtotime($invoice['invoice_date'])); ?></td>
                                    <td class="hide-xs">
                                        <?php
                                        if (!empty($invoice['due_date'])) {
                                            echo date('d/m/Y', strtotime($invoice['due_date']));

                                            // Check if overdue
                                            if (strtotime($invoice['due_date']) < strtotime('today') && $invoice['status'] == 'unpaid') {
                                                echo ' <span class="status-badge status-unpaid">overdue</span>';
                                            }
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                    <td>R$ <?php echo number_format($invoice['total'], 2, ',', '.'); ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        $status_text = '';

                                        switch ($invoice['status']) {
                                            case 'paid':
                                                $status_class = 'status-paid';
                                                $status_text = 'Paid';
                                                break;
                                            case 'unpaid':
                                                $status_class = 'status-unpaid';
                                                $status_text = 'Unpaid';
                                                break;
                                            case 'partial':
                                                $status_class = 'status-pending';
                                                $status_text = 'Partial';
                                                break;
                                            case 'cancelled':
                                                $status_class = 'status-unpaid';
                                                $status_text = 'Cancelled';
                                                break;
                                            default:
                                                $status_text = $invoice['status'];
                                        }
                                        ?>
                                        <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="invoice_view.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-info"><i class="fa fa-eye"></i></a>
                                            <a href="invoice_edit.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-primary"><i class="fa fa-edit"></i></a>
                                            <a href="invoice_print.php?id=<?php echo $invoice['id']; ?>" class="btn btn-sm btn-secondary" target="_blank"><i class="fa fa-print"></i></a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
                </div>
                <div class="text-end mt-2">
                    <a href="invoices_list.php" class="btn btn-outline-primary">Ver Todas as Faturas</a>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <section class="summary-cards">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Orçamentos Pendentes</h5>
                </div>
                <div class="card-body">
                    <h3><?php echo $stats['pending_quotes']; ?></h3>
                    <p class="card-title">Aguardando aprovação</p>
                    <a href="quotes.php?status=pending" class="btn btn-warning btn-sm mt-2">Ver Detalhes</a>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Faturas Não Pagas</h5>
                </div>
                <div class="card-body">
                    <h3><?php echo $stats['unpaid_invoices']; ?></h3>
                    <p class="card-title">Pendentes de pagamento</p>
                    <a href="invoices_list.php?filter_status=unpaid" class="btn btn-danger btn-sm mt-2">Ver Detalhes</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Quote Success Modal for Testing -->
    <?php if ($latest_quote): ?>
    <div id="quoteSuccessModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Quote Created Successfully!</h2>
                <span class="close" onclick="closeQuoteSuccessModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div style="text-align: center; margin-bottom: 20px;">
                    <i class="fas fa-check-circle" style="font-size: 48px; color: #28a745; margin-bottom: 15px;"></i>
                    <p id="quoteSuccessMessage" style="font-size: 16px; color: #495057; margin-bottom: 20px;">
                        Quote <?php echo htmlspecialchars($latest_quote['quote_number']); ?> has been created successfully!
                    </p>
                </div>

                <div style="display: flex; justify-content: center; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">
                    <button type="button" class="btn btn-primary" onclick="viewAndPrintQuotePDF()">
                        <i class="fas fa-print"></i>
                        View/Print PDF
                    </button>
                    <button type="button" class="btn btn-success" onclick="shareQuotePDFLink()">
                        <i class="fas fa-share-alt"></i>
                        Share PDF Link
                    </button>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeQuoteSuccessModal()">Close</button>
                    <button type="button" class="btn btn-success" onclick="createNewQuote()">Create New Quote</button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- JavaScript para Menu Mobile -->
    <script src="js/dashboard-mobile-menu.js"></script>

    <!-- Quote Modal Testing JavaScript -->
    <?php if ($latest_quote): ?>
    <script>
        // Quote modal testing variables
        let currentQuoteId = <?php echo $latest_quote['id']; ?>;
        let currentQuoteNumber = '<?php echo addslashes($latest_quote['quote_number']); ?>';
        let currentCustomerName = '<?php echo addslashes($latest_quote['customer_name']); ?>';

        // Function to test quote success modal
        function testQuoteSuccessModal() {
            showQuoteSuccessModal(currentQuoteId, currentQuoteNumber, `Quote ${currentQuoteNumber} has been created successfully!`);
        }

        // Functions for the quote success modal
        function showQuoteSuccessModal(quoteId, quoteNumber, message) {
            currentQuoteId = quoteId;
            currentQuoteNumber = quoteNumber;

            document.getElementById('quoteSuccessMessage').textContent = message;
            document.getElementById('quoteSuccessModal').style.display = 'block';
        }

        function closeQuoteSuccessModal() {
            document.getElementById('quoteSuccessModal').style.display = 'none';
        }

        function createNewQuote() {
            closeQuoteSuccessModal();
            window.location.href = 'quote_create.php';
        }

        // Function to view and print quote PDF
        function viewAndPrintQuotePDF() {
            if (!currentQuoteId) {
                alert('Error: Quote ID not found.');
                return;
            }

            // Open PDF in new window and trigger print dialog
            const pdfUrl = `generate_quote_pdf.php?id=${currentQuoteId}`;
            const printWindow = window.open(pdfUrl, '_blank');

            // Wait for the window to load and then trigger print
            if (printWindow) {
                printWindow.onload = function() {
                    setTimeout(() => {
                        printWindow.print();
                    }, 500);
                };
            }
        }

        // Function to share quote PDF link using device sharing options
        function shareQuotePDFLink() {
            if (!currentQuoteId) {
                alert('Error: Quote ID not found.');
                return;
            }

            // Show loading
            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Link...';

            // Create shared link via API
            const formData = new FormData();
            formData.append('type', 'quote');
            formData.append('item_id', currentQuoteId);
            formData.append('expires_days', 30);

            fetch('create_shared_link_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    shareQuoteWithDeviceOptions(data.link_url);
                } else {
                    alert('Error creating link: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error creating link:', error);
                alert('Error creating link. Please try again.');
            })
            .finally(() => {
                // Restore button
                button.disabled = false;
                button.innerHTML = originalText;
            });
        }

        // Function to share using device native sharing options
        function shareQuoteWithDeviceOptions(shareUrl) {
            // Check if Web Share API is supported (mobile devices)
            if (navigator.share) {
                // For Web Share API, don't include the URL in the text since it's passed separately
                const webShareMessage = `Hello! Here is the link to view quote ${currentQuoteNumber} for ${currentCustomerName}.\n\nThis link expires in 30 days.\n\nThank you!`;

                navigator.share({
                    title: `Quote ${currentQuoteNumber} - Tony's AC Repair`,
                    text: webShareMessage,
                    url: shareUrl
                }).catch(err => {
                    console.log('Error sharing:', err);
                    // Fallback to manual sharing options with full message including URL
                    const fullMessage = `Hello! Here is the link to view quote ${currentQuoteNumber} for ${currentCustomerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
                    showManualShareOptions(shareUrl, fullMessage);
                });
            } else {
                // Fallback for desktop or unsupported browsers - include URL in message
                const fullMessage = `Hello! Here is the link to view quote ${currentQuoteNumber} for ${currentCustomerName}:\n\n${shareUrl}\n\nThis link expires in 30 days.\n\nThank you!`;
                showManualShareOptions(shareUrl, fullMessage);
            }
        }

        // Function to show manual sharing options
        function showManualShareOptions(shareUrl, message) {
            // Create modal for sharing options
            const shareModal = document.createElement('div');
            shareModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            shareModal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 400px; width: 90%;">
                    <h3 style="margin: 0 0 15px 0;">Share Quote</h3>
                    <p style="margin: 0 0 15px 0; color: #666;">Choose how to share the quote link:</p>

                    <div style="display: flex; flex-direction: column; gap: 10px; margin-bottom: 15px;">
                        <button onclick="shareViaWhatsApp('${shareUrl}', '${message.replace(/'/g, "\\'")}'); closeShareModal();" style="padding: 10px; background: #25D366; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            <i class="fab fa-whatsapp"></i> WhatsApp
                        </button>
                        <button onclick="copyLinkToClipboard('${shareUrl}'); closeShareModal();" style="padding: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            <i class="fas fa-copy"></i> Copy Link
                        </button>
                    </div>

                    <div style="text-align: right;">
                        <button onclick="closeShareModal()" style="padding: 8px 15px; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Close
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(shareModal);
            window.currentShareModal = shareModal;
        }

        // Function to share via WhatsApp
        function shareViaWhatsApp(shareUrl, message) {
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Function to copy link to clipboard
        function copyLinkToClipboard(shareUrl) {
            navigator.clipboard.writeText(shareUrl).then(() => {
                alert('Link copied to clipboard!');
            }).catch(() => {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = shareUrl;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('Link copied to clipboard!');
            });
        }

        // Function to close share modal
        function closeShareModal() {
            if (window.currentShareModal) {
                document.body.removeChild(window.currentShareModal);
                window.currentShareModal = null;
            }
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('quoteSuccessModal');
            if (event.target === modal) {
                closeQuoteSuccessModal();
            }
        });
    </script>
    <?php endif; ?>

<?php include 'includes/footer.php'; ?>

<!-- <script src="js/dashboard-buttons.js"></script>
<script src="js/dashboard-buttons-fix.js"></script> -->

<?php
// Close database connection
db_close();
?>
