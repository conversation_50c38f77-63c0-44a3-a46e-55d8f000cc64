<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Helper functions
function format_date($date) {
    return date('m/d/Y', strtotime($date));
}

function format_money($value) {
    return number_format($value, 2, '.', ',');
}

// Initialize variables
$id = 0;
$error = '';
$invoice = null;
$items = [];
$customer = null;
$quote = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get invoice data
    $result = db_query("SELECT * FROM invoices WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $invoice = db_fetch_assoc($result);

        // Get customer data
        $customer_id = $invoice['customer_id'];
        $result = db_query("SELECT * FROM customers WHERE id = $customer_id");

        if ($result && db_num_rows($result) > 0) {
            $customer = db_fetch_assoc($result);
        }

        // Get quote data if available
        if (!empty($invoice['quote_id'])) {
            $quote_id = $invoice['quote_id'];
            $result = db_query("SELECT * FROM quotes WHERE id = $quote_id");

            if ($result && db_num_rows($result) > 0) {
                $quote = db_fetch_assoc($result);
            }
        }

        // Get invoice items
        $result = db_query("
            SELECT ii.*, i.name as item_name, i.sku as item_sku
            FROM invoice_items ii
            JOIN inventory i ON ii.inventory_id = i.id
            WHERE ii.invoice_id = $id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }

        // Get payment history
        $payments = [];
        $total_paid = 0;

        // Check if invoice_payments table exists
        $check_table = db_query("SHOW TABLES LIKE 'invoice_payments'");
        if (db_num_rows($check_table) > 0) {
            $payments_result = db_query("SELECT * FROM invoice_payments WHERE invoice_id = $id ORDER BY payment_date DESC");

            if ($payments_result && db_num_rows($payments_result) > 0) {
                while ($payment = db_fetch_assoc($payments_result)) {
                    $payments[] = $payment;
                    $total_paid += $payment['amount'];
                }
            }
        }

        // Calculate remaining amount
        $remaining = $invoice['total'] - $total_paid;

        // Update invoice with payment information if not already set
        if (!isset($invoice['paid_amount']) || $invoice['paid_amount'] != $total_paid) {
            // Check if columns exist
            $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'paid_amount'");
            if (db_num_rows($check_column) == 0) {
                db_query("ALTER TABLE invoices ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0");
            }

            $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'remaining_amount'");
            if (db_num_rows($check_column) == 0) {
                db_query("ALTER TABLE invoices ADD COLUMN remaining_amount DECIMAL(10,2) DEFAULT 0");
            }

            // Update invoice with current values
            db_query("UPDATE invoices SET paid_amount = $total_paid, remaining_amount = $remaining WHERE id = $id");
        }
    } else {
        $error = 'Invoice not found.';
    }
} else {
    $error = 'Invoice ID not provided.';
}

// Update invoice status if requested
if (isset($_GET['action']) && $invoice) {
    $action = $_GET['action'];
    $status = '';

    switch ($action) {
        case 'paid':
            $status = 'paid';
            break;
        case 'unpaid':
            $status = 'unpaid';
            break;
        case 'partial':
            $status = 'partial';
            break;
        case 'cancelled':
            $status = 'cancelled';
            break;
    }

    if (!empty($status)) {
        // Se o status for 'paid', definir paid_amount como o total e remaining_amount como 0
        if ($status == 'paid') {
            $total = $invoice['total'];
            if (db_query("UPDATE invoices SET status = '$status', paid_amount = $total, remaining_amount = 0 WHERE id = $id")) {
                $invoice['status'] = $status;
                $invoice['paid_amount'] = $total;
                $invoice['remaining_amount'] = 0;
            }
        }
        // Se o status for 'unpaid', definir paid_amount como 0 e remaining_amount como o total
        elseif ($status == 'unpaid') {
            $total = $invoice['total'];
            if (db_query("UPDATE invoices SET status = '$status', paid_amount = 0, remaining_amount = $total WHERE id = $id")) {
                $invoice['status'] = $status;
                $invoice['paid_amount'] = 0;
                $invoice['remaining_amount'] = $total;
            }
        }
        // Para outros status, apenas atualizar o status
        else {
            if (db_query("UPDATE invoices SET status = '$status' WHERE id = $id")) {
                $invoice['status'] = $status;
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Invoice - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/invoice-view-buttons.css">
    <link rel="stylesheet" href="css/invoice-edit-address.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if ($invoice && $customer): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">Invoice #<?php echo htmlspecialchars($invoice['invoice_number']); ?></h1>
                        <div class="action-buttons">
                            <a href="invoices_list.php" class="btn btn-primary" title="Back to Invoices List"><i class="fas fa-arrow-left"></i> Back to List</a>
                            <a href="invoice_edit.php?id=<?php echo $id; ?>" class="btn btn-secondary" title="Edit this Invoice"><i class="fas fa-edit"></i> Edit Invoice</a>
                            <a href="invoice_print.php?id=<?php echo $id; ?>" class="btn btn-info" target="_blank" title="Print this Invoice"><i class="fas fa-print"></i> Print Invoice</a>
                        </div>
                    </div>
                    <div class="card-body">
                    <?php if (isset($_GET['address_updated']) && $_GET['address_updated'] == 1): ?>
                        <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 15px; margin-bottom: 20px; border-radius: 4px; border: 1px solid #c3e6cb;">
                            <i class="fas fa-check-circle"></i> Customer address has been successfully updated.
                        </div>
                    <?php endif; ?>
                        <div class="invoice-status" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
                            <div style="display: flex; flex-wrap: wrap; justify-content: space-between; margin-bottom: 15px;">
                                <div>
                                    <strong>Status:</strong>
                                    <?php
                                    $status = '';
                                    switch ($invoice['status']) {
                                        case 'paid':
                                            $status = '<span class="status-paid">Pago</span>';
                                            break;
                                        case 'unpaid':
                                            $status = '<span class="status-unpaid">Não Pago</span>';
                                            break;
                                        case 'partial':
                                            $status = '<span class="status-partial">Parcial</span>';
                                            break;
                                        case 'cancelled':
                                            $status = '<span class="status-cancelled">Cancelado</span>';
                                            break;
                                        default:
                                            $status = $invoice['status'];
                                    }
                                    echo $status;
                                    ?>
                                </div>

                                <div style="display: flex; gap: 20px;">
                                    <div>
                                        <strong>Total:</strong>
                                        <span>$ <?php echo format_money($invoice['total']); ?></span>
                                    </div>

                                    <div>
                                        <strong>Pago:</strong>
                                        <span style="color: #27ae60;">$ <?php echo format_money($total_paid); ?></span>
                                    </div>

                                    <div>
                                        <strong>Restante:</strong>
                                        <span style="color: <?php echo $remaining <= 0 ? '#27ae60' : '#e74c3c'; ?>;">$ <?php echo format_money($remaining); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="status-actions" style="display: flex; flex-wrap: wrap; gap: 10px;">
                                <?php if ($invoice['status'] != 'cancelled'): ?>
                                    <a href="invoice_payment.php?id=<?php echo $id; ?>" class="btn btn-primary"><i class="fas fa-dollar-sign"></i> Record Payment</a>
                                <?php endif; ?>
                                <a href="invoice_view.php?id=<?php echo $id; ?>&action=paid" class="btn btn-success"><i class="fas fa-check-circle"></i> Mark as Paid</a>
                                <a href="invoice_view.php?id=<?php echo $id; ?>&action=unpaid" class="btn btn-warning"><i class="fas fa-times-circle"></i> Mark as Unpaid</a>
                                <a href="invoice_view.php?id=<?php echo $id; ?>&action=partial" class="btn btn-info"><i class="fas fa-adjust"></i> Mark as Partial</a>
                                <a href="invoice_view.php?id=<?php echo $id; ?>&action=cancelled" class="btn btn-danger"><i class="fas fa-ban"></i> Cancel</a>
                            </div>
                        </div>

                        <div class="invoice-details">
                            <div class="info-container" style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
                                <div class="info-card" style="flex: 1; min-width: 300px; background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50; display: flex; justify-content: space-between; align-items: center;">
                                        <span><i class="fas fa-user"></i> Customer Information</span>
                                        <a href="invoice_edit_address_simple.php?id=<?php echo $id; ?>" class="edit-address-btn" title="Edit Customer Address"><i class="fas fa-edit"></i> Edit Address</a>
                                    </h3>
                                    <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px 15px; align-items: baseline;">
                                        <strong>Name:</strong> <span><?php echo htmlspecialchars($customer['name']); ?></span>
                                        <strong>Email:</strong> <span><?php echo htmlspecialchars($customer['email']); ?></span>
                                        <strong>Phone:</strong> <span><?php echo htmlspecialchars($customer['phone']); ?></span>
                                        <strong>Address:</strong> <span><?php echo htmlspecialchars($customer['address']); ?></span>
                                        <strong>City:</strong> <span><?php echo htmlspecialchars($customer['city']); ?></span>
                                        <strong>State/ZIP:</strong> <span><?php echo htmlspecialchars($customer['state']); ?> <?php echo htmlspecialchars($customer['zip']); ?></span>
                                    </div>
                                </div>

                                <div class="info-card" style="flex: 1; min-width: 300px; background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-file-invoice-dollar"></i> Invoice Information</h3>
                                    <div style="display: grid; grid-template-columns: auto 1fr; gap: 8px 15px; align-items: baseline;">
                                        <strong>Number:</strong> <span><?php echo htmlspecialchars($invoice['invoice_number']); ?></span>
                                        <strong>Date:</strong> <span><?php echo date('m/d/Y', strtotime($invoice['invoice_date'])); ?></span>
                                        <strong>Due Date:</strong> <span><?php echo !empty($invoice['due_date']) ? date('m/d/Y', strtotime($invoice['due_date'])) : 'Not specified'; ?></span>
                                        <?php if ($quote): ?>
                                        <strong>Quote:</strong> <span><a href="quote_view.php?id=<?php echo $quote['id']; ?>"><?php echo htmlspecialchars($quote['quote_number']); ?></a></span>
                                        <?php endif; ?>
                                        <strong>Created:</strong> <span><?php echo date('m/d/Y h:i A', strtotime($invoice['created_at'])); ?></span>
                                        <strong>Updated:</strong> <span><?php echo date('m/d/Y h:i A', strtotime($invoice['updated_at'])); ?></span>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($invoice['service_address'])): ?>
                            <div class="service-address-section" style="background-color: #e8f5e8; border-radius: 8px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); border-left: 4px solid #28a745;">
                                <h3 style="margin-top: 0; border-bottom: 2px solid #28a745; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-map-marker-alt"></i> Service Address</h3>
                                <p style="margin: 0; font-size: 14px; line-height: 1.5; white-space: pre-line;"><?php echo htmlspecialchars($invoice['service_address']); ?></p>
                                <small style="color: #6c757d; font-style: italic;">Location where the service was performed</small>
                            </div>
                            <?php endif; ?>

                            <div class="items-section" style="background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-list"></i> Invoice Items</h3>

                                <?php if (empty($items)): ?>
                                    <p>No items found.</p>
                                <?php else: ?>
                                    <div class="table-responsive" style="overflow-x: auto;">
                                        <table class="data-table" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>SKU</th>
                                                    <th>Description</th>
                                                    <th>Quantity</th>
                                                    <th>Unit Price</th>
                                                    <th>Subtotal</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($items as $item): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($item['item_sku']); ?></td>
                                                        <td><?php echo htmlspecialchars($item['description']); ?></td>
                                                        <td><?php echo $item['quantity']; ?></td>
                                                        <td>$ <?php echo number_format($item['price'], 2, '.', ','); ?></td>
                                                        <td>$ <?php echo number_format($item['subtotal'], 2, '.', ','); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="totals-section" style="background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); margin-top: 20px;">
                                <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-calculator"></i> Invoice Summary</h3>
                                <div style="display: grid; grid-template-columns: 1fr auto; gap: 8px; margin-top: 10px;">
                                    <div style="font-weight: 600; color: #555;">Subtotal:</div>
                                    <div style="text-align: right; font-weight: 600;">$ <?php echo number_format($invoice['subtotal'], 2, '.', ','); ?></div>

                                    <?php if ($invoice['tax_rate'] > 0): ?>
                                        <div style="font-weight: 600; color: #555;">Tax (<?php echo number_format($invoice['tax_rate'], 2, '.', ','); ?>%):</div>
                                        <div style="text-align: right; font-weight: 600;">$ <?php echo number_format($invoice['tax_amount'], 2, '.', ','); ?></div>
                                    <?php endif; ?>

                                    <?php if ($invoice['discount_amount'] > 0): ?>
                                        <div style="font-weight: 600; color: #555;">Discount:</div>
                                        <div style="text-align: right; font-weight: 600;">$ <?php echo number_format($invoice['discount_amount'], 2, '.', ','); ?></div>
                                    <?php endif; ?>

                                    <div style="font-weight: 700; color: #2c3e50; font-size: 1.1em; border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">Total:</div>
                                    <div style="text-align: right; font-weight: 700; color: #2c3e50; font-size: 1.1em; border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">$ <?php echo number_format($invoice['total'], 2, '.', ','); ?></div>
                            </div>

                            <?php if (!empty($payments)): ?>
                                <div class="payment-history" style="background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); margin-top: 20px;">
                                    <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-history"></i> Payment History</h3>
                                    <div class="table-responsive" style="overflow-x: auto; margin-top: 10px;">
                                        <table class="data-table" style="width: 100%; border-collapse: collapse;">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Amount</th>
                                                    <th>Method</th>
                                                    <th>Notes</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($payments as $payment): ?>
                                                    <tr>
                                                        <td><?php echo format_date($payment['payment_date']); ?></td>
                                                        <td>$ <?php echo format_money($payment['amount']); ?></td>
                                                        <td>
                                                            <?php
                                                                $method = $payment['payment_method'];
                                                                $method_labels = [
                                                                    'zelle' => 'Zelle',
                                                                    'card' => 'Card',
                                                                    'cash' => 'Cash',
                                                                    'check' => 'Check'
                                                                ];
                                                                echo isset($method_labels[$method]) ? $method_labels[$method] : ucfirst($method);
                                                            ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($payment['notes']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($invoice['notes'])): ?>
                                <div class="notes-section" style="background-color: #f8f9fa; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05); margin-top: 20px;">
                                    <h3 style="margin-top: 0; border-bottom: 2px solid #3498db; padding-bottom: 8px; color: #2c3e50;"><i class="fas fa-sticky-note"></i> Notes</h3>
                                    <p style="margin-top: 10px; white-space: pre-line;"><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
