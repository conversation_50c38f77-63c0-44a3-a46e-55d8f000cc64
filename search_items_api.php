<?php
// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(0);

// Start output buffering to catch any unwanted output
ob_start();

// Set content type to JSON
header('Content-Type: application/json');

// Include database connection
require_once 'db_connect.php';

try {
    // Check if there's a search term
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';

    if (empty($search)) {
        ob_clean();
        echo json_encode([]);
        exit;
    }

    // Connect to database
    if (!db_connect()) {
        ob_clean();
        echo json_encode(['error' => 'Database connection failed']);
        exit;
    }

    // Escape search term
    $search_escaped = db_escape($search);

    // Query to search items
    $query = "SELECT id, name, price, sku, description, quantity
              FROM inventory
              WHERE name LIKE '%$search_escaped%' 
              OR sku LIKE '%$search_escaped%'
              OR description LIKE '%$search_escaped%'
              ORDER BY name
              LIMIT 10";

    $result = db_query($query);

    if (!$result) {
        ob_clean();
        echo json_encode(['error' => 'Query error: ' . (function_exists('db_error') ? db_error() : 'Unknown error')]);
        exit;
    }

    $items = [];

    if (function_exists('db_num_rows') && db_num_rows($result) > 0) {
        while ($row = db_fetch_assoc($result)) {
            $items[] = [
                'id' => (int)$row['id'],
                'name' => $row['name'] ?? '',
                'price' => floatval($row['price']),
                'sku' => $row['sku'] ?? '',
                'description' => $row['description'] ?? '',
                'quantity' => (int)($row['quantity'] ?? 0)
            ];
        }
    }

    ob_clean();
    echo json_encode($items);

} catch (Exception $e) {
    ob_clean();
    echo json_encode(['error' => 'Internal error: ' . $e->getMessage()]);
} finally {
    if (function_exists('db_close')) {
        db_close();
    }
}

ob_end_flush();
?>
