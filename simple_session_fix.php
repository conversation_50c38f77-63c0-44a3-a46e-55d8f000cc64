<?php
/**
 * Simple Session Fix for cPanel Hosting
 * Minimal approach to avoid ini_set warnings
 */

// Prevent multiple inclusions
if (defined('SIMPLE_SESSION_FIX_LOADED')) {
    return;
}
define('SIMPLE_SESSION_FIX_LOADED', true);

// Only proceed if no session is active
if (session_status() === PHP_SESSION_NONE) {
    
    // Create local session directory
    $session_dir = __DIR__ . '/sessions';
    if (!file_exists($session_dir)) {
        @mkdir($session_dir, 0755, true);
    }
    
    // Set session save path only if directory is writable
    if (is_writable($session_dir)) {
        @ini_set('session.save_path', $session_dir);
    } else {
        // Try alternative directory
        $alt_session_dir = __DIR__ . '/tmp';
        if (!file_exists($alt_session_dir)) {
            @mkdir($alt_session_dir, 0755, true);
        }
        if (is_writable($alt_session_dir)) {
            @ini_set('session.save_path', $alt_session_dir);
        }
    }
    
    // Start session with error suppression
    @session_start();
}
?>
