/**
 * Mobile-specific styles for invoices_list.php
 * Focused on mobile-first design with responsive table and search functionality
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* Header Mobile */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #2c3e50, #3498db);

    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.mobile-header h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    z-index: 1001;
    position: relative;
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
    flex-shrink: 0;
}

.menu-toggle:hover {
    background-color: rgba(255,255,255,0.2);
}

.menu-toggle:focus {
    outline: none;
}

/* Menu Deslizante */
.slide-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-height: calc(100vh - 60px);
    overflow-y: auto;
}

.slide-menu:focus {
    outline: none;
}

.slide-menu.active {
    transform: translateY(0);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: background-color 0.2s ease;
    min-height: 100px;
    border: 1px solid rgba(255,255,255,0.1);
}

.menu-item:hover {
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
}

.menu-item.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.menu-item i {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    display: block;
}

.menu-item span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* Overlay */
.menu-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 998;
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Layout Principal */
.main-content {
    margin-top: 60px;
    padding: 1rem;
    min-height: calc(100vh - 60px);
}

/* Cabeçalho da página */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0 0.5rem;
}

.page-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.page-header .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 6px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #007bff;
    color: white;
    border: none;
    transition: background-color 0.2s ease;
}

.page-header .btn:hover {
    background-color: #0056b3;
    text-decoration: none;
    color: white;
}

.page-header .btn i {
    font-size: 0.8rem;
}

/* Search and Filter Section */
.search-filter-section {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-container {
    display: flex;
    gap: 0.5rem;

}

.search-input-wrapper {
    position: relative;
    flex: 1;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    font-size: 0.9rem;
    z-index: 2;
    pointer-events: none;
}

.search-input {
    width: 100%;
    padding: 0.75rem 4rem 0.75rem 2.5rem;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    background-color: white;
    box-sizing: border-box;
}

.search-input::placeholder {
    color: #999;
    padding-left: 0;
}

.clear-search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    font-size: 0.75rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.clear-search-btn:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.clear-search-btn i {
    font-size: 0.7rem;
}

/* Mobile adjustments for clear button */
@media (max-width: 768px) {
    .clear-search-btn {
        right: 0.25rem;
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }

    .search-input {
        padding-right: 3.5rem;
    }
}

/* Filter icon styling */
.filter-icon {
    color: #6c757d;
    font-size: 0.8rem;
    margin-left: 0.25rem;
}

.filter-icon.active {
    color: #007bff;
}

.filter-select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Table Styles */
.invoices-table {
    background: white;
    border-radius: 8px;
    overflow-y: auto;
    overflow-x: hidden; /* Evita scroll horizontal */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-height: calc(100vh - 200px); /* Permite scroll com cabeçalho fixo */
    position: relative;
    width: 100%;
}

.table-mobile {
    width: 100%;
    border-collapse: collapse;
    font-size: 10px; /* ⭐ FONTE PRINCIPAL DA TABELA - Aumentada em 2px (era 8px) */
    table-layout: fixed; /* Layout fixo para controle preciso das colunas */
}

/* Fonte das células da tabela (herda da .table-mobile) */
.table-mobile td {
    font-size: inherit; /* Herda o tamanho da fonte da tabela */
}

.table-mobile th,
.table-mobile td {
    padding: 0.3rem 0.15rem;
    text-align: left;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
    line-height: 1.2;
}

.table-mobile th {
    /* jonatas background-color: #e9ecef; */
    background: #c5d8eb; /* cabeçalho da tabela */
    font-weight: 600;
    cursor: pointer;
    position: sticky;
    top: 0;
    z-index: 10;
    user-select: none;
    transition: background-color 0.2s ease;
    border-bottom: 2px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-mobile th:hover {
    background-color: #dee2e6;
}

/* Optimized column widths to fit all columns in viewport */
.table-mobile th:nth-child(1), /* ID */
.table-mobile td:nth-child(1) {
    width: 12%;
    min-width: 40px;
    max-width: 55px;
}

.table-mobile th:nth-child(2), /* Date */
.table-mobile td:nth-child(2) {
    width: 15%;
    min-width: 50px;
    max-width: 70px;
}

.table-mobile th:nth-child(3), /* Name */
.table-mobile td:nth-child(3) {
    width: 22%;
    min-width: 70px;
    max-width: 100px;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.1;
    max-height: 2.4em;
    overflow: hidden;
}

.table-mobile th:nth-child(4), /* Address */
.table-mobile td:nth-child(4) {
    width: 22%;
    min-width: 70px;
    max-width: 100px;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.1;
    max-height: 2.4em;
    overflow: hidden;
}

.table-mobile th:nth-child(5), /* Total */
.table-mobile td:nth-child(5) {
    width: 15%;
    min-width: 50px;
    max-width: 70px;
    text-align: right;
}

.table-mobile th:nth-child(6), /* Status */
.table-mobile td:nth-child(6) {
    width: 14%;
    min-width: 50px;
    max-width: 70px;
    text-align: center;
}

/* Status column - same font size as other columns */
.table-mobile td:nth-child(6) span {
    font-size: inherit; /* Status badges herdam o tamanho da tabela */
    font-weight: 600;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Status header layout - text above icon */
.status-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.status-text {
    font-size: inherit; /* Herda do cabeçalho da tabela */
    line-height: 1;
}

.filter-icon {
    font-size: 0.6em; /* Ícone menor que o texto */
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.filter-icon.active {
    opacity: 1;
    color: #007bff;
}

.table-mobile tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.table-mobile tbody tr:hover {
    background-color: #f8f9fa;
}

.table-mobile tbody tr.selected {
    background-color: #e3f2fd;
    border-left: 3px solid #007bff;
}

/* Action Buttons Row */
.action-row {
    display: none;
    background-color: #f0f8ff;
    border-top: 2px solid #007bff;
    animation: slideDown 0.3s ease;
}

.action-row.show {
    display: table-row;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.action-buttons {
    padding: 0.75rem;
    text-align: center;
    display: flex;
    flex-wrap: nowrap;
    gap: 0.25rem;
    justify-content: center;
    overflow-x: auto;
}

.action-buttons .btn {
    margin: 0;
    padding: 0.6rem 0.5rem; /* ⭐ Altura aumentada para melhor clique */
    font-size: 0.7rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.2rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: auto;
    flex-shrink: 0;
}

.action-buttons .btn-view { 
    background-color: #6c757d; 
    color: white; 
}

.action-buttons .btn-view:hover { 
    background-color: #5a6268; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-edit { 
    background-color: #ffc107; 
    color: #212529; 
}

.action-buttons .btn-edit:hover { 
    background-color: #e0a800; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-payment { 
    background-color: #28a745; 
    color: white; 
}

.action-buttons .btn-payment:hover { 
    background-color: #218838; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-print { 
    background-color: #17a2b8; 
    color: white; 
}

.action-buttons .btn-print:hover { 
    background-color: #138496; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-share { 
    background-color: #007bff; 
    color: white; 
}

.action-buttons .btn-share:hover { 
    background-color: #0056b3; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons .btn-delete { 
    background-color: #dc3545; 
    color: white; 
}

.action-buttons .btn-delete:hover { 
    background-color: #c82333; 
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Status Badges */
.status-paid { 
    color: #28a745; 
    font-weight: 600; 
    font-size: 9px;
}

.status-unpaid { 
    color: #dc3545; 
    font-weight: 600; 
    font-size: 9px;
}

.status-partial { 
    color: #ffc107; 
    font-weight: 600; 
    font-size: 9px;
}

.status-cancelled { 
    color: #6c757d; 
    font-weight: 600; 
    font-size: 9px;
}

/* Sort Icons */
.sort-icon {
    margin-left: 0.25rem;
    font-size: 0.7rem;
    opacity: 0.5;
    transition: opacity 0.2s ease, color 0.2s ease;
    color: #6c757d;
}

.sort-icon.active {
    opacity: 1;
    color: #007bff;
}

/* Specific sort direction icons */
.fa-sort-up {
    color: #007bff !important;
    opacity: 1 !important;
}

.fa-sort-down {
    color: #007bff !important;
    opacity: 1 !important;
}

/* Delete Confirmation Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    animation: slideUp 0.3s ease;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-content h3 {
    color: #dc3545;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.modal-content p {
    margin-bottom: 0.5rem;
    color: #495057;
}

.modal-buttons {
    margin-top: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.modal-buttons .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.btn-confirm {
    background-color: #dc3545;
    color: white;
}

.btn-confirm:hover {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Empty state */
.empty-state {
    padding: 3rem 2rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .search-container {
        flex-direction: column;
    }
    
    .filter-dropdowns {
        justify-content: space-between;
    }
    
    .filter-select {
        flex: 1;
        min-width: auto;
    }
    
    /* Mobile herda font-size da .table-mobile principal */

    .table-mobile th,
    .table-mobile td {
        padding: 0.2rem 0.1rem;
        line-height: 1.1;
    }

    /* Tighter column widths for mobile */
    .table-mobile th:nth-child(1), /* ID */
    .table-mobile td:nth-child(1) {
        width: 8%;
        min-width: 30px;
        max-width: 40px;
    }

    .table-mobile th:nth-child(2), /* Date */
    .table-mobile td:nth-child(2) {
        width: 12%;
        min-width: 40px;
        max-width: 55px;
    }

    .table-mobile th:nth-child(3), /* Name */
    .table-mobile td:nth-child(3) {
        width: 28%;
        min-width: 70px;
        max-width: 100px;
    }

    .table-mobile th:nth-child(4), /* Address */
    .table-mobile td:nth-child(4) {
        width: 28%;
        min-width: 70px;
        max-width: 100px;
    }

    .table-mobile th:nth-child(5), /* Total */
    .table-mobile td:nth-child(5) {
        width: 14%;
        min-width: 45px;
        max-width: 60px;
    }

    .table-mobile th:nth-child(6), /* Status */
    .table-mobile td:nth-child(6) {
        width: 10%;
        min-width: 35px;
        max-width: 50px;
    }
    
    .action-buttons {
        flex-wrap: wrap;
        gap: 0.2rem;
    }

    .action-buttons .btn {
        font-size: 0.65rem;
        padding: 0.5rem 0.4rem; /* Altura aumentada para mobile */
        flex: 1;
        min-width: 60px;
    }
    
    .modal-content {
        margin: 10% auto;
        padding: 1.5rem;
    }
    
    .modal-buttons {
        flex-direction: column;
    }
    
    .modal-buttons .btn {
        margin-bottom: 0.5rem;
    }
}

/* Very small screens */
@media (max-width: 360px) {
    /* Telas pequenas herdam font-size da .table-mobile principal */

    .table-mobile th,
    .table-mobile td {
        padding: 0.15rem 0.05rem;
        line-height: 1.0;
    }

    /* Ultra-compact column widths for very small screens */
    .table-mobile th:nth-child(1), /* ID */
    .table-mobile td:nth-child(1) {
        width: 6%;
        min-width: 25px;
        max-width: 30px;
    }

    .table-mobile th:nth-child(2), /* Date */
    .table-mobile td:nth-child(2) {
        width: 10%;
        min-width: 35px;
        max-width: 45px;
    }

    .table-mobile th:nth-child(3), /* Name */
    .table-mobile td:nth-child(3) {
        width: 30%;
        min-width: 60px;
        max-width: 80px;
    }

    .table-mobile th:nth-child(4), /* Address */
    .table-mobile td:nth-child(4) {
        width: 30%;
        min-width: 60px;
        max-width: 80px;
    }

    .table-mobile th:nth-child(5), /* Total */
    .table-mobile td:nth-child(5) {
        width: 14%;
        min-width: 40px;
        max-width: 50px;
    }

    .table-mobile th:nth-child(6), /* Status */
    .table-mobile td:nth-child(6) {
        width: 10%;
        min-width: 30px;
        max-width: 40px;
    }
    
    .action-buttons .btn {
        font-size: 0.7rem;
        padding: 0.5rem 0.5rem; /* Altura aumentada para telas pequenas */
    }
    
    .search-filter-section {
        padding: 0.75rem;
    }
    
    .filter-select,
    .search-input {
        font-size: 14px;
        padding: 0.6rem;
    }
}

/* Loading state */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.table-mobile tbody tr:focus,
.action-buttons .btn:focus,
.filter-select:focus,
.search-input:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .table-mobile th {
        border: 2px solid #000;
    }
    
    .table-mobile tbody tr.selected {
        border: 2px solid #000;
        background-color: #ffff00;
        color: #000;
    }
    
    .action-buttons .btn {
        border: 2px solid #000;
    }
}

/* Payment Modal Styles - Compact Layout */
.payment-modal-content {
    max-width: 900px;
    width: 95%;
    max-height: 95vh;
    overflow-y: auto;
}

.payment-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.payment-modal-header h3 {
    color: #007bff;
    margin: 0;
    font-size: 1.25rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.close-btn:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.payment-modal-body {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.invoice-info {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
}

.invoice-info p {
    margin: 0;
    font-size: 0.9rem;
}

.payment-history h4,
.add-payment-section h4 {
    color: #495057;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.25rem;
}

.payment-history-table {
    overflow-x: auto;
    margin-bottom: 0.75rem;
}

#paymentsTable {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

#paymentsTable th,
#paymentsTable td {
    padding: 0.5rem 0.25rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

#paymentsTable th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

#paymentsTable tbody tr:hover {
    background-color: #f8f9fa;
}

.payment-actions {
    width: 80px;
    text-align: center;
}

.edit-payment-btn,
.delete-payment-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    margin: 0 0.125rem;
    border-radius: 3px;
    font-size: 0.75rem;
}

.edit-payment-btn {
    color: #007bff;
}

.edit-payment-btn:hover {
    background-color: #e3f2fd;
}

.delete-payment-btn {
    color: #dc3545;
}

.delete-payment-btn:hover {
    background-color: #ffebee;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.form-row.full-width {
    grid-template-columns: 1fr;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid #dee2e6;
}

.form-buttons .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* Mobile responsive adjustments for payment modal */
@media (max-width: 768px) {
    .payment-modal-content {
        margin: 2% auto;
        padding: 0.75rem;
        width: 98%;
        max-height: 98vh;
    }

    .invoice-info {
        grid-template-columns: 1fr 1fr;
        gap: 0.25rem;
        padding: 0.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .form-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .payment-modal-header {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    #paymentsTable {
        font-size: 0.75rem;
    }

    #paymentsTable th,
    #paymentsTable td {
        padding: 0.25rem 0.125rem;
    }

    .payment-actions {
        width: 60px;
    }

    .edit-payment-btn,
    .delete-payment-btn {
        font-size: 0.7rem;
        padding: 0.125rem;
    }
}

/* Message animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .action-row,
    .modal,
    .modal-content,
    .action-buttons .btn,
    .table-mobile tbody tr {
        animation: none;
        transition: none;
    }

    .action-buttons .btn:hover {
        transform: none;
    }

    #successMessage,
    #errorMessage {
        animation: none !important;
    }
}
