:root {
    --primary-color: #2196F3;
    --secondary-color: #1976D2;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FFC107;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --border-color: #ddd;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --radius: 8px;
    --transition: all 0.3s ease;
}

.invoice-payment-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.invoice-header {
    background: white;
    padding: 20px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.invoice-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.detail-card {
    background: white;
    padding: 20px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.detail-card h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.payment-form {
    background: white;
    padding: 25px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius);
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
}

.payments-history {
    background: white;
    padding: 25px;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
}

.payments-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.payments-table th,
.payments-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.payments-table th {
    background: var(--light-gray);
    font-weight: 500;
}

.alert {
    padding: 15px;
    border-radius: var(--radius);
    margin-bottom: 20px;
}

.alert-success {
    background: #E8F5E9;
    color: #2E7D32;
    border: 1px solid #A5D6A7;
}

.alert-danger {
    background: #FFEBEE;
    color: #C62828;
    border: 1px solid #FFCDD2;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.status-pending {
    background: var(--warning-color);
    color: #333;
}

.status-partial {
    background: var(--primary-color);
    color: white;
}

.status-paid {
    background: var(--success-color);
    color: white;
}

@media (max-width: 768px) {
    .invoice-payment-container {
        padding: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .payments-table {
        display: block;
        overflow-x: auto;
    }

    .detail-card {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .invoice-header {
        padding: 15px;
    }

    .form-control {
        font-size: 16px;
    }

    .status-badge {
        display: block;
        text-align: center;
        margin-top: 5px;
    }
}