/**
 * Mobile-specific styles for invoice page buttons
 */

/* Default styles (will be overridden for mobile) */
.mobile-filter-buttons {
    display: none;
}

/* Mobile styles */
@media (max-width: 768px) {
    /* Hide original filter options and new invoice button */
    .filter-options,
    .card-header .btn-primary {
        display: none !important;
    }

    /* Show mobile filter buttons */
    .mobile-filter-buttons {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 4px;
        margin-bottom: 15px;
        width: 100%;
        padding: 0 2px;
    }

    /* Adjust grid for different screen sizes */
    @media (max-width: 400px) {
        .mobile-filter-buttons {
            grid-template-columns: repeat(5, 1fr);
            gap: 2px;
            padding: 0 1px;
        }
    }

    /* Style for mobile filter buttons */
    .mobile-filter-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 6px 2px;
        border-radius: 6px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #495057;
        text-decoration: none;
        transition: all 0.2s ease;
        height: 45px;
        font-size: 9px;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }

    /* Icon styling */
    .mobile-filter-btn i {
        font-size: 12px;
        margin-bottom: 2px;
        display: block;
    }

    /* Active button styling */
    .mobile-filter-btn.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    /* Button colors */
    .mobile-filter-btn.btn-all {
        border-color: #6c757d;
    }

    .mobile-filter-btn.btn-all.active {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .mobile-filter-btn.btn-unpaid {
        border-color: #dc3545;
    }

    .mobile-filter-btn.btn-unpaid.active {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .mobile-filter-btn.btn-paid {
        border-color: #28a745;
    }

    .mobile-filter-btn.btn-paid.active {
        background-color: #28a745;
        border-color: #28a745;
    }

    .mobile-filter-btn.btn-partial {
        border-color: #ffc107;
    }

    .mobile-filter-btn.btn-partial.active {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }

    .mobile-filter-btn.btn-cancelled {
        border-color: #6c757d;
    }

    .mobile-filter-btn.btn-cancelled.active {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .mobile-filter-btn.btn-new {
        border-color: #007bff;
        background-color: #007bff;
        color: white;
    }

    /* Hover effects */
    .mobile-filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    /* Card body padding adjustment */
    .card-body {
        padding-top: 10px;
    }

    /* Table mobile optimizations */
    .table-responsive {
        font-size: 12px;
    }

    .table-responsive table {
        font-size: 12px;
    }

    .table-responsive th,
    .table-responsive td {
        padding: 6px 4px;
        font-size: 11px;
        line-height: 1.2;
    }

    .table-responsive th {
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Action buttons in table */
    .action-buttons .btn {
        font-size: 10px;
        padding: 4px 6px;
    }

    .action-buttons .btn i {
        font-size: 10px;
    }

    /* Status badges */
    .status-badge {
        font-size: 9px;
        padding: 2px 6px;
    }

    /* Amount columns */
    .table-responsive .text-right {
        font-size: 11px;
        font-weight: 500;
    }
}
