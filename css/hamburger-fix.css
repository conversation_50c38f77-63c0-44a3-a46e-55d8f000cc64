/* Estilos específicos para o menu hamburger mobile - Dashboard Simple */

/* ===== MENU TOGGLE BUTTON ===== */
.menu-toggle {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 1001;
    position: relative;
    width: 44px;
    height: 44px;
}

.menu-toggle:hover {
    background-color: rgba(255,255,255,0.1);
    transform: scale(1.05);
}

.menu-toggle:active {
    transform: scale(0.95);
}

.menu-toggle:focus {
    outline: 2px solid rgba(255,255,255,0.5);
    outline-offset: 2px;
}

/* ===== ANIMAÇÃO DO ÍCONE HAMBURGER ===== */
.menu-toggle i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

/* Quando o menu está ativo, transformar hamburger em X */
.slide-menu.active ~ .mobile-header .menu-toggle i {
    transform: rotate(90deg);
}

/* ===== MELHORIAS PARA O SLIDE MENU ===== */
@media (max-width: 768px) {
    /* Garantir que o menu toggle seja sempre visível */
    .menu-toggle {
        display: flex !important;
        z-index: 1001;
    }

    /* Melhorar a transição do slide menu */
    .slide-menu {
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform;
    }

    /* Overlay com transição suave */
    .menu-overlay {
        transition: all 0.3s ease;
        backdrop-filter: blur(2px);
    }

    /* Prevenir scroll do body quando menu está aberto */
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }

    /* Melhorar performance das animações */
    .menu-item {
        will-change: transform, opacity;
        backface-visibility: hidden;
    }

    /* Ajustes para dispositivos com notch */
    @supports (padding-top: env(safe-area-inset-top)) {
        .mobile-header {
            padding-top: env(safe-area-inset-top);
            height: calc(60px + env(safe-area-inset-top));
        }

        .slide-menu {
            top: calc(60px + env(safe-area-inset-top));
        }

        .main-content {
            margin-top: calc(60px + env(safe-area-inset-top));
        }

        .menu-overlay {
            top: calc(60px + env(safe-area-inset-top));
        }
    }
}

/* ===== ANIMAÇÕES ESPECÍFICAS ===== */
@keyframes hamburgerToX {
    0% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(45deg) scale(0.8);
    }
    100% {
        transform: rotate(90deg);
    }
}

@keyframes xToHamburger {
    0% {
        transform: rotate(90deg);
    }
    50% {
        transform: rotate(45deg) scale(0.8);
    }
    100% {
        transform: rotate(0deg);
    }
}

/* ===== ESTADOS ESPECÍFICOS ===== */
.menu-toggle[aria-expanded="true"] i {
    animation: hamburgerToX 0.3s ease forwards;
}

.menu-toggle[aria-expanded="false"] i {
    animation: xToHamburger 0.3s ease forwards;
}

/* ===== MELHORIAS DE ACESSIBILIDADE ===== */
@media (prefers-reduced-motion: reduce) {
    .menu-toggle i,
    .slide-menu,
    .menu-overlay,
    .menu-item {
        transition: none !important;
        animation: none !important;
    }
}

/* ===== SUPORTE A LANDSCAPE ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-header {
        height: 50px;
    }

    .slide-menu {
        top: 50px;
        max-height: calc(100vh - 50px);
    }

    .main-content {
        margin-top: 50px;
    }

    .menu-overlay {
        top: 50px;
    }

    .menu-grid {
        padding: 1rem;
        gap: 0.5rem;
    }

    .menu-item {
        min-height: 70px;
        padding: 0.75rem 0.5rem;
    }
}
