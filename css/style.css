/* Main Styles for NOVO Sistema */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 0px;
}

/* Header */
header {
    background-color: #2c3e50;
    color: #fff;
    padding: 0.5rem 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: auto;
    padding: 0.25rem 10px;

}

.logo {
    font-size: 1.3rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: 1rem;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s;
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.nav-links a:hover {
    color: #3498db;
}

/* Main Content */
main {
    padding: 2rem 0;
}

/* Cards */
.card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    margin: 0;
    color: #2c3e50;
}

.card-body {
    padding: 1rem;
}

/* Form Styles */
.form-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;

}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Grid para formulários */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

/* Campos específicos */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="number"],
input[type="date"],
input[type="time"],
input[type="search"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    /* jonatas border-radius: 4px; */
    font-size: 16px; /* Previne zoom em iOS */
    font-family: inherit;
    -webkit-appearance: none; /* Remove estilo padrão em iOS */
    appearance: none;
}

/* Select personalizado */
select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 12px;
    padding-right: 2.5rem;
}

/* Textarea */
textarea {
    min-height: 100px;
    resize: vertical;
}

/* Checkbox e Radio customizados */
.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.form-check input[type="checkbox"],
.form-check input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
}

/* Grupos de botões */
.form-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* File input */
input[type="file"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

/* Validation styles */
.form-control.is-invalid {
    border-color: var(--danger-color);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-buttons {
        flex-direction: column;
    }

    .form-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Ajuste para inputs de data em dispositivos móveis */
    input[type="date"],
    input[type="time"] {
        min-height: 44px; /* Melhor área de toque */
    }

    /* Ajuste para selects em dispositivos móveis */
    select {
        min-height: 44px;
    }

    /* Ajuste para checkboxes e radios em dispositivos móveis */
    .form-check {
        min-height: 44px;
    }

    /* Aumenta área de toque para todos os controles interativos */
    .form-control,
    .btn,
    input[type="file"] {
        min-height: 44px;
    }
}

/* Ajustes específicos para telas muito pequenas */
@media (max-width: 576px) {
    .form-container {
        padding: 10px;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-size: 0.9rem;
    }

    /* Ajuste para inputs numéricos */
    input[type="number"] {
        -moz-appearance: textfield; /* Remove spinners no Firefox */
    }

    input[type="number"]::-webkit-outer-spin-button,
    input[type="number"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Melhora visualização de campos de arquivo */
    input[type="file"] {
        font-size: 14px;
    }

    /* Ajusta tamanho de texto em dispositivos móveis */
    .form-control {
        font-size: 16px;
    }
}

/* Suporte para modo escuro removido para evitar ativação automática no iPhone */

/* Buttons */
.btn {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #7f8c8d;
}

.btn-secondary:hover {
    background-color: #6c7a7d;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* ===== CORREÇÕES DE FOCO E SELEÇÃO PARA BOTÕES ===== */
.btn:focus,
.btn:active,
.btn:focus-visible {
    outline: none;
    -webkit-tap-highlight-color: transparent;
}

.btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn::selection,
.btn::-moz-selection {
    background: transparent;
}

/* Tables */
.data-table {
    font-size: 8px;
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background-color: #f9f9f9;
    font-weight: 600;
    cursor: pointer;
}

.data-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Dashboard */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    text-align: center;
}

.dashboard-card h2 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: #3498db;
}

.dashboard-card p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

/* Items in quotes and invoices */
.items-container {
    margin-bottom: 1.5rem;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 1rem;
    background-color: #f9f9f9;
}

.item-row {
    display: grid;
    grid-template-columns: 3fr 4fr 1fr 2fr 2fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.item-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

/* Totals section */
.totals-section {
    margin-top: 2rem;
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.totals-label {
    font-weight: 500;
}

.grand-total {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Estilos para histórico de pagamentos */
.payments-history {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.invoice-info {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
}

.invoice-info p {
    margin: 0.5rem 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    text-align: left;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* Responsividade para tabelas em dispositivos móveis */
@media (max-width: 768px) {
    .table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .invoice-info {
        padding: 0.75rem;
    }
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
    margin-top: 2rem;
}



/* Responsive */
@media (max-width: 768px) {

    .navbar {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0.25rem 10px;

    }

    .nav-links {
        display: none;
        width: 100%;
        flex-direction: column;
        margin-top: 1rem;
        text-align: center;
    }

    .nav-links.active {
        display: flex;
    }

    .nav-links li {
        margin: 0.25rem 0;
    }

    .nav-links {
        margin-top: 0.5rem;
    }

    header {
        padding: 0.3rem 10px;
    }

    .logo {
        font-size: 1.2rem;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .btn {
        margin-top: 1rem;
    }

    .item-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .data-table {
        display: block;
        overflow-x: auto;
    }
}

/* Títulos padronizados - otimizado para mobile primeiro - reduzidos em 25% */
h3,
.form-group label,
.totals-label,
label {
    font-size: 11px !important;
    font-weight: 600 !important;
    color: #495057 !important;
    margin: 15px 0 8px 0 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    line-height: 1.2 !important;
}

/* Específico para labels de formulário - reduzidos em 25% */
.form-group label,
.customer-form-grid label,
.invoice-details label,
.form-label {
    font-size: 11px !important;
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

/* Layout Taxa e Desconto para mobile */
@media (max-width: 768px) {
    .totals-row-group {
        display: flex !important;
        flex-direction: row !important;
        gap: 12px !important;
        margin-bottom: 12px !important;
    }

    /* Forçar campos Taxa e Desconto lado a lado no mobile */
    .totals-row-group:first-child {
        display: flex !important;
        flex-direction: row !important;
        gap: 12px !important;
    }

    .totals-field {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .totals-label {
        font-size: 14px !important;
        margin-bottom: 6px !important;
        display: block !important;
        font-weight: 500 !important;
    }

    /* Formatação igual ao bloco Itens da Fatura */
    .input-with-dropdown {
        display: flex !important;
        align-items: center !important;
        border: 2px solid #e9ecef !important;
        border-radius: 6px !important;
        background-color: white !important;
        /* jonatas padding: 12px 14px;*/
        line-height: 1.4;
        /* jonatas overflow: visible !important; */
        width: 100% !important;
    }

    .input-with-dropdown input {
        flex: 1 !important;
        border: none !important;
        outline: none !important;
        padding: 9px 12px !important;
        font-size: 14px !important;
        background: transparent !important;
        /*jonatas width: calc(100% - 45px) !important;*/
        min-width: 0 !important;
    }

    .input-with-dropdown select {
        border: none !important;
        outline: none !important;
        background-color: #f8f9fa !important;
        padding: 9px 8px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #495057 !important;
        width: 45px !important;
        min-width: 45px !important;
        max-width: 45px !important;
        border-left: 2px solid #e9ecef !important;
        cursor: pointer !important;
    }

    .totals-section input[readonly],
    .totals-field input[readonly] {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        border: 2px solid #e9ecef !important;
        border-radius: 6px !important;
        padding: 12px 14px;
        font-size: 16px !important;
        line-height: 1.4;
        width: 100% !important;
        box-sizing: border-box !important;
    }
}

@media (max-width: 480px) {
    .totals-label {
        font-size: 13px !important;
    }
}

/* Botão "+" abaixo dos campos Qtd e Preço */
.add-button-inline {
    margin-top: 6px !important;
    display: none;
    grid-column: 2 / 4 !important; /* Ocupa as colunas 2 e 3 (Qtd e Preço) */
}

.add-item-btn-inline {
    width: 100% !important;
    height: 38px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    font-size: 18px !important;
    font-weight: bold !important;
    border-radius: 6px !important;
    background-color: #28a745 !important;
    border: none !important;
    color: white !important;
    cursor: pointer !important;
}

.add-item-btn-inline:hover {
    background-color: #218838 !important;
}

.btn-clear-item {
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    font-size: 16px !important;
    color: #721c24 !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 18px !important;
    height: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

@media (max-width: 768px) {
    .add-button-inline {
        grid-template-columns: 1fr 60px 80px !important;
        gap: 8px !important;
    }

    .add-item-btn-inline {
        height: 32px !important;
        font-size: 16px !important;
    }
}

@media (max-width: 480px) {
    .add-button-inline {
        grid-template-columns: 1fr 50px 70px !important;
        gap: 6px !important;
    }

    .add-item-btn-inline {
        height: 30px !important;
        font-size: 14px !important;
    }
}

/* Força consistência de títulos após carregamento - reduzidos em 25% */
body h3,
body .form-group label,
body .totals-label,
body label,
body .form-label {
    font-size: 11px !important;
    font-weight: 600 !important;
    color: #495057 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    line-height: 1.2 !important;
}

body .form-group label,
body .customer-form-grid label,
body .invoice-details label,
body .form-label {
    margin-bottom: 8px !important;
    display: block !important;
}

/* Força aplicação em mobile - reduzidos em 25% */
@media (max-width: 768px) {
    body h3,
    body .form-group label,
    body label,
    body .form-label {
        font-size: 11px !important;
        font-weight: 600 !important;
        color: #495057 !important;
    }
}


/* Consistência de altura para campos de texto comuns - REMOVIDO
   Agora controlado pelo CSS principal no invoice_create.php */
