/**
 * Dashboard Simple Layout Styles - Mobile First
 * Layout reformulado focando em mobile com menu deslizante
 * Elimina sidebar tradicional em favor de UX mobile
 */

/* ===== RESET E BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* ===== HEADER MOBILE ===== */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1rem;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.mobile-header h1 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}
.menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 1001;
    position: relative;
    width: 44px;
    height: 44px;
    min-width: 44px;
    min-height: 44px;
    flex-shrink: 0;
}

.menu-toggle:hover {
    background-color: rgba(255,255,255,0.2);
    transform: scale(1.05);
}

.menu-toggle:active {
    transform: scale(0.95);
}

.menu-toggle:focus {
    outline: none;
}

.menu-toggle i {
    transition: all 0.3s ease;
    transform-origin: center;
    will-change: transform;
    backface-visibility: hidden;
}

/* Animações com keyframes para melhor compatibilidade Android */
@keyframes rotateToX {
    from { transform: rotate(0deg); }
    to { transform: rotate(90deg); }
}

@keyframes rotateToHamburger {
    from { transform: rotate(90deg); }
    to { transform: rotate(0deg); }
}

/* Animação baseada no aria-expanded (mais confiável) */
.menu-toggle[aria-expanded="true"] i {
    animation: rotateToX 0.3s ease forwards;
}

.menu-toggle[aria-expanded="false"] i {
    animation: rotateToHamburger 0.3s ease forwards;
}

/* Fallback para body.menu-open */
body.menu-open .menu-toggle i {
    transform: rotate(90deg);
}

/* ===== MENU DESLIZANTE ===== */
.slide-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    transform: translateY(-100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 999;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    max-height: calc(100vh - 60px);
    overflow-y: auto;
}

.slide-menu:focus {
    outline: none;
}

.slide-menu.active {
    transform: translateY(0);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.5rem;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    min-height: 100px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.menu-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.menu-item.active {
    background: linear-gradient(135deg, #3498db, #2980b9);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.menu-item i {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    display: block;
}

.menu-item span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* ===== OVERLAY ===== */
.menu-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 998;
}

.menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ===== LAYOUT PRINCIPAL ===== */
.main-content {
    margin-top: 60px;
    padding: 1rem;
    min-height: calc(100vh - 60px);
}

/* ===== QUICK ACCESS GRID ===== */
.quick-access {
    margin-bottom: 2rem;
}

.quick-access h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 0.5rem;
    background: white !important;
    border-radius: 16px;
    text-decoration: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-height: 120px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

/* Ensure button elements inherit quick-btn styles properly */
button.quick-btn {
    font-family: inherit;
    cursor: pointer;
}

/* Ensure button inherits color variables for each type */
button.quick-btn.btn-primary { --btn-color: #3498db; --btn-color-rgb: 52, 152, 219; }
button.quick-btn.btn-success { --btn-color: #27ae60; --btn-color-rgb: 39, 174, 96; }
button.quick-btn.btn-warning { --btn-color: #f39c12; --btn-color-rgb: 243, 156, 18; }
button.quick-btn.btn-danger { --btn-color: #e74c3c; --btn-color-rgb: 231, 76, 60; }
button.quick-btn.btn-info { --btn-color: #17a2b8; --btn-color-rgb: 23, 162, 184; }
button.quick-btn.btn-purple { --btn-color: #9b59b6; --btn-color-rgb: 155, 89, 182; }
button.quick-btn.btn-secondary { --btn-color: #6c757d; --btn-color-rgb: 108, 117, 125; }

/* Removido: barra colorida no topo dos botões que causava seleção visual */

.quick-btn:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    text-decoration: none;
    background: white !important;
    /* Remove a borda colorida no hover */
    border-color: transparent;
}

/* Removido :active para evitar conflito com animações JavaScript */
.quick-btn:active {
    /* Desabilitado para evitar tremor - animação controlada via JS */
    transform: none;
    border: 2px solid transparent;
    transition: none;
}

/* Remove seleção de texto em todos os estados */
.quick-btn::selection {
    background: transparent;
}

.quick-btn::-moz-selection {
    background: transparent;
}

.quick-btn i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: var(--btn-color);
    transition: all 0.3s ease;
}

.quick-btn:hover i {
    transform: scale(1.1);
}

.quick-btn span {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: center;
    line-height: 1.2;
}

/* Cores específicas dos botões com valores RGB */
.quick-btn.btn-primary {
    --btn-color: #3498db;
    --btn-color-rgb: 52, 152, 219;
}

.quick-btn.btn-success {
    --btn-color: #27ae60;
    --btn-color-rgb: 39, 174, 96;
}

.quick-btn.btn-warning {
    --btn-color: #f39c12;
    --btn-color-rgb: 243, 156, 18;
}

.quick-btn.btn-danger {
    --btn-color: #e74c3c;
    --btn-color-rgb: 231, 76, 60;
}

.quick-btn.btn-info {
    --btn-color: #17a2b8;
    --btn-color-rgb: 23, 162, 184;
}

.quick-btn.btn-purple {
    --btn-color: #9b59b6;
    --btn-color-rgb: 155, 89, 182;
}

.quick-btn.btn-secondary {
    --btn-color: #6c757d;
    --btn-color-rgb: 108, 117, 125;
    border: 2px dashed #6c757d;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.quick-btn.btn-secondary:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    border-style: solid;
}

.quick-btn.btn-secondary span {
    color: #495057;
    font-weight: 700;
}

.quick-btn.btn-secondary i {
    color: #dc3545;
}

/* ===== CORREÇÕES ESPECÍFICAS PARA BOTÃO NOVA FATURA ===== */
.quick-btn.btn-danger {
    /* Garante que não há seleção vermelha */
    background: white;
    border: 2px solid transparent;
}

.quick-btn.btn-danger:hover {
    background: white;
    /* Remove completamente a borda no hover */
    border-color: transparent;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.quick-btn.btn-danger:active {
    background: white;
    border-color: transparent;
    transform: none; /* Remove conflito */
}

.quick-btn.btn-danger:focus {
    background: white;
    border-color: transparent;
    outline: none;
}

/* Estilos específicos para botão Nova Fatura */
.quick-access-grid .quick-btn.btn-danger.clicked,
.quick-btn.btn-danger.clicked {
    background: white;
    /* Borda estática vermelha com alta especificidade */
    border: 2px solid var(--btn-color) !important;
    outline: none;
}

.quick-btn.btn-danger.tap-animation {
    background: white;
    animation: tapScale 0.2s ease-out;
}

/* ===== REGRA PARA BOTÕES SEM CONFLITAR COM .clicked ===== */
.quick-access-grid .quick-btn:not(.clicked) {
    border: 2px solid transparent;
    outline: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.quick-access-grid .quick-btn:not(.clicked):hover {
    border: 2px solid transparent;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* ===== CLASSES PARA ANIMAÇÕES DE INTERAÇÃO ===== */
/* Borda estática com alta especificidade */
.quick-access-grid .quick-btn.clicked,
.quick-btn.clicked {
    /* Borda estática - aparece imediatamente */
    border: 2px solid var(--btn-color) !important;
    outline: none;
    /* Remove transição da borda para aparecer imediatamente */
    transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

/* Animação separada - aplicada depois da borda */
.quick-btn.tap-animation {
    animation: tapScale 0.2s ease-out;
}

/* Remove conflitos com :active */
.quick-btn.clicked:active,
.quick-btn.tap-animation:active {
    transform: none;
    border: 2px solid var(--btn-color) !important;
}

/* ===== ALERTAS ===== */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert i {
    margin-right: 0.5rem;
}

/* ===== CARDS E TABELAS ===== */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
    background: white;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1.25rem;
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.card-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.25rem;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    margin: -0.25rem;
}

.data-table {
    margin: 0;
    font-size: 0.9rem;
}

.data-table th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-top: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: 0.875rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.data-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* ===== SUMMARY CARDS ===== */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-cards .card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.summary-cards .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.summary-cards .card-body {
    padding: 1.5rem;
    text-align: center;
}

.summary-cards h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
}

.summary-cards .card-title {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .quick-access-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .quick-btn {
        min-height: 100px;
        padding: 1rem 0.5rem;
    }

    .quick-btn i {
        font-size: 1.6rem;
        margin-bottom: 0.5rem;
    }

    .quick-btn span {
        font-size: 0.8rem;
    }

    .menu-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        padding: 1rem;
    }

    .menu-item {
        min-height: 80px;
        padding: 1rem 0.5rem;
    }

    .menu-item i {
        font-size: 1.5rem;
        margin-bottom: 0.4rem;
    }

    .menu-item span {
        font-size: 0.8rem;
    }

    .summary-cards {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .card-body {
        padding: 1rem;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem 0.4rem;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem;
    }

    .quick-access-grid {
        gap: 0.5rem;
    }

    .quick-btn {
        min-height: 85px;
        padding: 0.75rem 0.25rem;
        border-radius: 12px;
    }

    .quick-btn i {
        font-size: 1.4rem;
        margin-bottom: 0.4rem;
    }

    .quick-btn span {
        font-size: 0.75rem;
        line-height: 1.1;
    }

    .menu-grid {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .menu-item {
        min-height: 70px;
        padding: 0.75rem 0.25rem;
    }

    .menu-item i {
        font-size: 1.3rem;
        margin-bottom: 0.3rem;
    }

    .menu-item span {
        font-size: 0.75rem;
    }

    .card {
        border-radius: 12px;
        margin-bottom: 1rem;
    }

    .card-header,
    .card-body {
        padding: 0.875rem;
    }

    .quick-access h2 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .summary-cards h3 {
        font-size: 1.8rem;
    }
}

/* ===== ANIMAÇÕES ===== */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* ===== ANIMAÇÃO SCALE ON TAP ===== */
@keyframes tapScale {
    0% {
        transform: scale(1) translate(0, -4px);
    }
    50% {
        transform: scale(0.94) translate(1px, -1px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }
    100% {
        transform: scale(1) translate(0, -4px);
    }
}

/* Removido borderPulse - não usado mais */

/* Removido fadeIn automático para evitar conflitos */
.quick-btn {
    /* animation: fadeIn 0.4s ease forwards; */
}

.menu-item {
    animation: slideDown 0.3s ease forwards;
}

.card {
    animation: fadeIn 0.5s ease forwards;
}

/* Animação removida - conflitava com transform */

/* ===== MELHORIAS DE ACESSIBILIDADE ===== */
/* Remove todas as bordas de foco indesejadas */
.quick-btn:focus,
.quick-btn:focus-visible {
    outline: none;
}

/* Remove seleção de texto nos botões */
.quick-btn {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* Remove highlight de toque em dispositivos móveis */
.quick-btn:active,
.quick-btn:focus,
.quick-btn:hover {
    -webkit-tap-highlight-color: transparent;
    outline: none;
}

/* Menu items só mostram foco quando navegados por teclado */
.menu-item:focus {
    outline: 3px solid #3498db;
    outline-offset: 2px;
}

/* Remove foco visual quando menu acabou de abrir */
.slide-menu.active .menu-item:focus:not(:focus-visible) {
    outline: none;
}

/* ===== DARK MODE SUPPORT REMOVIDO ===== */
/* Regras de modo escuro removidas para evitar ativação automática no iPhone */

/* ===== CORREÇÕES GLOBAIS DE SELEÇÃO ===== */
/* Remove seleção de texto em elementos interativos */
button,
.btn,
.quick-btn,
.menu-item,
.menu-toggle,
a[role="button"] {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* ===== CORREÇÃO GLOBAL SEM CONFLITAR COM .clicked ===== */
.quick-btn:not(.clicked),
.quick-btn:not(.clicked):hover,
.quick-btn:not(.clicked):active,
.quick-btn:not(.clicked):focus,
.quick-btn:not(.clicked):visited {
    border: 2px solid transparent;
    outline: none;
    text-decoration: none;
}

/* Remove outline padrão do navegador em todos os botões */
button:focus,
.btn:focus,
.quick-btn:focus,
.menu-toggle:focus,
a:focus {
    outline: none;
}

/* Remove seleção de texto global em elementos específicos */
::selection {
    background: rgba(52, 152, 219, 0.2);
}

::-moz-selection {
    background: rgba(52, 152, 219, 0.2);
}

/* Remove seleção em botões especificamente */
.quick-btn::selection,
.menu-item::selection,
button::selection {
    background: transparent;
}

.quick-btn::-moz-selection,
.menu-item::-moz-selection,
button::-moz-selection {
    background: transparent;
}

/* ===== UTILITÁRIOS ===== */
.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}
