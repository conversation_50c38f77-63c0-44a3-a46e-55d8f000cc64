/* Invoice PDF Styles - Optimized for Letter Size (8.5" x 11") */

@page {
    size: letter;
    margin: 0.2in;
}



/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    font-size: 10pt;
    line-height: 1.3;
    color: #000000;
    background: white;
    padding: 48px 10px 10px 10px;
    max-width: none;
    margin: 10px;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

/* Print Button */
.print-button {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    width: 100%;
    text-align: center;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.print-button:hover {
    background: #0056b3;
}

/* Header Section */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    padding: 5px 5px 15px 5px;

    border-bottom: 2px solid #007bff;
    flex-wrap: nowrap;
}

.company-info {
    flex: 1;
    text-align: left;
}

.company-info h1 {
    font-size: 20pt;
    color: #007bff;
    margin-bottom: 8px;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: flex !important;
    align-items: center;
    gap: 6px;
}

.company-info h1 img {
    flex-shrink: 0;
    vertical-align: middle;
    margin-top: -2px;
}

.quote-title {
    font-size: 20pt;
        margin-bottom: 8px;
        font-weight: bold;
        color: #007bff;
}

.company-details {
    font-size: 9pt;
    color: #000000;
    line-height: 1.4;
}

.invoice-info {
    flex: 0 0 auto;
    text-align: right;
    min-width: 0px;
}

.invoice-number {
    font-size: 18pt;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 8px;
    line-height: 1.2;
    text-align: right;
}

.invoice-dates {
    font-size: 9pt;
    color: #000000;
    line-height: 1.4;
}

/* Section Titles */
.section-title {
    font-size: 11pt;
    font-weight: bold;
    color: #007bff;
    margin: 0px 0 5px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Section Title Icons - Font Awesome styling */
.section-title.label-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title .label-icon {
    font-size: 10pt;
    color: #007bff;
    opacity: 0.8;
}

/* Two Columns Layout */
.two-columns {
    display: flex;
    gap: 20px;
    align-items: stretch;
}

.two-columns > div {
    flex: 1;
}

/* Customer Information */
.customer-info {
    background: transparent;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #007bff;
    margin-bottom: 15px;
    font-size: 9pt;
    height: 65%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.customer-name {
    font-size: 11pt;
    font-weight: bold;
    margin-bottom: 5px;
    color: #000000;
}

/* Service Address - Centralized Horizontally and Vertically */
.customer-info[style*="background-color: #e8f5e8"] {
    align-items: center !important;
    text-align: center !important;
}

.customer-info[style*="background-color: #e8f5e8"] .section-title {
    margin-bottom: 5px !important;
}

.customer-info[style*="background-color: #e8f5e8"] div[style*="text-align: center"] {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    flex: 1 !important;
}

/* Items Table */
.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
    font-size: 9pt;
}

.items-table th {
    background: #007bff;
    color: white;
    padding: 8px 6px;
    text-align: center;
    font-weight: bold;
    font-size: 9pt;
    border: 1px solid #0056b3;
}

.items-table th:first-child {
    text-align: left;
}

.items-table td {
    padding: 6px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.items-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.items-table tbody tr:hover {
    background: #e3f2fd;
}

/* Text Alignment */
.text-right { 
    text-align: right; 
}

.text-center { 
    text-align: center; 
}

/* Totals Section */
.totals {

    float: right;
    width: 280px;
    clear: both;
}

.totals + .section-title {
    clear: both;
    display: block;
    width: 100%;
}

.totals + .section-title + .customer-info {
    margin-top: 8px;
    width: 100%;
    display: block;
}

.totals table {
    width: 100%;
    border-collapse: collapse;
    font-size: 9pt;
}

.totals td {
    padding: 6px 10px;
    border-bottom: 1px solid #e9ecef;
}

.total-label {
    font-weight: bold;
    text-align: right;
    background: #f8f9fa;
    color: #555;
}

.total-amount {
    text-align: right;
    font-weight: bold;
    color: #000000;
}

/* Grand Total Styling */
.grand-total {
    background: #f0f8ff !important;
    font-size: 10pt !important;
    border-top: 2px solid #007bff !important;
    border-bottom: 2px solid #007bff !important;
}

.grand-total .total-label {
    color: #000 !important;
    font-weight: bold !important;
    font-size: 10pt !important;
}

.grand-total .total-amount {
    color: #000 !important;
    font-weight: bold !important;
    font-size: 10pt !important;
}

/* Payment Information */
.payment-info {
    clear: both;
    padding: 1px 5px 5px;
    background: transparent;
    border-radius: 4px;
    border: 1px solid #007bff;
    font-size: 9pt;
}

.payment-status {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 8pt;
    font-weight: bold;
    text-transform: uppercase;
}

.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-unpaid {
    background: #f8d7da;
    color: #721c24;
}

.status-partial {
    background: #fff3cd;
    color: #856404;
}

/* Quote Status Styles */
.status-pending {
    background: #e2e3e5;
    color: #383d41;
}

.status-approved {
    background: #d4edda;
    color: #155724;
}

.status-rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Notes Section */
.notes {
    clear: both;
    margin-bottom: 10px;
    padding: 12px;
    background: transparent;
    border-radius: 4px;
    border: 1px solid #007bff;
    font-size: 9pt;
}

/* Footer */
.footer {
    margin-top: 30px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    text-align: center;
    font-size: 8pt;
    color: #000000;
    clear: both;
    page-break-inside: avoid;
    break-inside: avoid;
}

.footer p {
    margin-bottom: 3px;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 768px) {
    body {
        padding: 48px 10px 10px 10px;
        max-width: 100%;

        zoom: 1;
    }
    .company-info h1 {
        font-size: 16pt;
    }

    .company-info h1 img {
        width: 28px;
        height: 28px;
    }

    .quote-title {
        font-size: 16pt;
    }

    .header {
        flex-direction: row;
        justify-content: center;
        padding: 5px 5px 15px 5px;
    }

    .invoice-info {
        text-align: right;
        margin-top: 0;
        min-width: 0px;
    }

    .invoice-number {
        font-size: 12pt;
    }
    
    .totals {
        float: none;
        width: 100%;
        margin-bottom: 15px;
    }
    
    .items-table {
        font-size: 8pt;
    }
    
    .items-table th,
    .items-table td {
        padding: 4px;
    }

    .two-columns {
        gap: 10px;
    }

    .totals + .section-title {
        clear: both;
        display: block;
        width: 100%;
    }

    .totals + .section-title + .customer-info {
        margin-top: 8px;
    }
}

/* Print-specific optimizations */
@media print {
    body {
        print-color-adjust: exact;
        -webkit-print-color-adjust: exact;
        padding: 0.2in;
        font-size: 9pt;
        /* jonatas height: 10in;
        width: 7.5in; */
        position: relative;
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        box-sizing: border-box;
    }

    .no-print {
        display: none !important;
    }

    .print-button {
        display: none !important;
    }

    .print-page {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Container principal para o conteúdo */
    .content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        min-height: 0;
    }

    /* Seção de itens adaptável */
    .items-section {
        flex: 1;
        min-height: 0;
        overflow: hidden;
    }

    .header {
        margin-bottom: 15px;
        flex-direction: row;
        justify-content: center;
        flex-shrink: 0;
    }

    .company-info {
        text-align: left;
    }

    .invoice-info {
        text-align: right;
        min-width: 0px;
    }
    
    .section-title {
        margin: 4px 0 4px 0;
        flex-shrink: 0;
    }

    .customer-info {
        margin-bottom: 8px;
        padding: 8px;
        flex-shrink: 0;
    }

    .items-table {
        margin-bottom: 8px;
        flex-shrink: 0;
        font-size: 8pt;
    }

    .items-table th,
    .items-table td {
        padding: 4px 6px;
    }

    .totals {
        flex-shrink: 0;
        margin-bottom: 0px;
        align-self: flex-end;
    }

    .totals + .section-title {
        margin-top: 0px;
    }

    .totals + .customer-info {
        margin: 0;
        clear: both;
        flex-shrink: 0;
    }

    .payment-info {
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .notes {
        padding: 8px;
        margin-bottom: 10px;
        font-size: 8pt;
        flex-shrink: 0;
    }
    
    .footer {
        position: relative;
        margin-top: auto;
        padding-top: 10px;
        border-top: 1px solid #e9ecef;
        text-align: center;
        font-size: 7pt;
        color: #000000;
        clear: both;
        flex-shrink: 0;
        height: 60px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}
