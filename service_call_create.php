<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Verificar se a tabela service_calls existe e criá-la se necessário
$table_exists = false;
$check_table = db_query("SHOW TABLES LIKE 'service_calls'");
if ($check_table && db_num_rows($check_table) > 0) {
    $table_exists = true;
} else {
    // Criar a tabela service_calls
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `service_calls` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `customer_id` int(11) NOT NULL,
        `title` varchar(100) NOT NULL,
        `description` text DEFAULT NULL,
        `scheduled_date` datetime NOT NULL,
        `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending',
        `service_history` text DEFAULT NULL,
        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `customer_id` (`customer_id`),
        CONSTRAINT `service_calls_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if (db_query($create_table_sql)) {
        $table_exists = true;
        error_log("Tabela service_calls criada com sucesso");
    } else {
        error_log("Erro ao criar tabela service_calls: " . db_error());
    }
}

// Initialize variables
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$title = $description = $scheduled_date = $service_history = '';
$status = 'pending';
$error = $success = '';

// Get all customers for dropdown
$customers = [];
$result = db_query("SELECT id, name, phone, email FROM customers ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    $customer_id = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : 0;
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $scheduled_date = isset($_POST['scheduled_date']) ? trim($_POST['scheduled_date']) : '';
    $status = isset($_POST['status']) ? trim($_POST['status']) : 'pending';
    $service_history = isset($_POST['service_history']) ? trim($_POST['service_history']) : '';

    // Validate required fields
    if (empty($customer_id) || empty($title) || empty($scheduled_date)) {
        $error = 'Por favor, preencha todos os campos obrigatórios.';
    } else {
        try {
            // Format date for MySQL
            $formatted_date = date('Y-m-d H:i:s', strtotime($scheduled_date));

            // Escapar valores para evitar SQL injection
            $title_escaped = db_escape($title);
            $description_escaped = db_escape($description);
            $status_escaped = db_escape($status);
            $service_history_escaped = db_escape($service_history);

            // Insert new service call
            $query = "INSERT INTO service_calls (customer_id, title, description, scheduled_date, status, service_history)
                      VALUES ($customer_id, '$title_escaped', '$description_escaped',
                      '$formatted_date', '$status_escaped', '$service_history_escaped')";

            // Registrar a query para debug
            error_log("Query: " . $query);

            if (db_query($query)) {
                $success = 'Chamado de atendimento registrado com sucesso!';
                // Clear form data
                $customer_id = $title = $description = $scheduled_date = $service_history = '';
                $status = 'pending';

                // Redirect to service calls list after 2 seconds
                header("refresh:2;url=service_calls.php");
            } else {
                $error = 'Erro ao registrar chamado: ' . db_error();
                error_log("Erro ao executar query: " . db_error());
            }
        } catch (Exception $e) {
            $error = 'Erro ao processar o formulário: ' . $e->getMessage();
            error_log("Exceção: " . $e->getMessage());
        }
    }
}

// Verificar se a função db_escape existe
if (!function_exists('db_escape')) {
    function db_escape($string) {
        global $conn;
        return mysqli_real_escape_string($conn, $string);
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Chamado de Atendimento - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        .service-call-form-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -15px;
            margin-left: -15px;
        }

        .form-col {
            flex: 0 0 50%;
            max-width: 50%;
            padding-right: 15px;
            padding-left: 15px;
        }

        @media (max-width: 768px) {
            .form-col {
                flex: 0 0 100%;
                max-width: 100%;
            }
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
        }

        .form-group textarea {
            min-height: 100px;
        }

        .required-field {
            color: red;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tony's AC Repair</div>
                <ul class="nav-links">
                    <li><a href="dashboard_simple.php">Painel</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                    <li><a href="service_calls.php" class="active">Chamados</a></li>
                </ul>
                <div class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="page-header">
                <h1><i class="fas fa-headset"></i> Novo Chamado de Atendimento</h1>
                <a href="service_calls.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar</a>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="service-call-form-container">
                <form method="POST" action="service_call_create.php" class="needs-validation">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="customer_id">Cliente <span class="required-field">*</span></label>
                                <select id="customer_id" name="customer_id" required>
                                    <option value="">Selecione um cliente</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>" <?php echo ($customer_id == $customer['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?> - <?php echo htmlspecialchars($customer['phone']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="title">Título <span class="required-field">*</span></label>
                                <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="scheduled_date">Data Agendada <span class="required-field">*</span></label>
                                <input type="text" id="scheduled_date" name="scheduled_date" value="<?php echo htmlspecialchars($scheduled_date); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="pending" <?php echo ($status == 'pending') ? 'selected' : ''; ?>>Pendente</option>
                                    <option value="in_progress" <?php echo ($status == 'in_progress') ? 'selected' : ''; ?>>Em Andamento</option>
                                    <option value="completed" <?php echo ($status == 'completed') ? 'selected' : ''; ?>>Concluído</option>
                                    <option value="cancelled" <?php echo ($status == 'cancelled') ? 'selected' : ''; ?>>Cancelado</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-col">
                            <div class="form-group">
                                <label for="description">Descrição</label>
                                <textarea id="description" name="description"><?php echo htmlspecialchars($description); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="service_history">Histórico de Atendimento</label>
                                <textarea id="service_history" name="service_history"><?php echo htmlspecialchars($service_history); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Salvar Chamado</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="js/timezone-config.js"></script>
    <script>
        $(document).ready(function() {
            // Menu toggle para dispositivos móveis
            $('.menu-toggle').click(function() {
                $('.nav-links').toggleClass('active');
            });

            // Inicializar o seletor de data e hora com timezone da Florida
            flatpickr("#scheduled_date", {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
                time_24hr: true,
                minDate: "today",
                defaultDate: new Date().toLocaleString("en-US", {timeZone: "America/New_York"})
            });
        });
    </script>
</body>
</html>
