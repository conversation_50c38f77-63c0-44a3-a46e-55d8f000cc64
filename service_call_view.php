<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = $success = '';
$service_call = null;

// Process status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = isset($_POST['status']) ? trim($_POST['status']) : '';

    if (!empty($new_status)) {
        try {
            // Update status
            $query = "UPDATE service_calls SET status = '" . db_escape($new_status) . "' WHERE id = $id";

            // Registrar a query para debug
            error_log("Query: " . $query);

            if (db_query($query)) {
                $success = 'Status atualizado com sucesso!';

                // Refresh service call data
                $result = db_query("
                    SELECT sc.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email, c.address as customer_address
                    FROM service_calls sc
                    JOIN customers c ON sc.customer_id = c.id
                    WHERE sc.id = $id
                ");

                if ($result && db_num_rows($result) > 0) {
                    $service_call = db_fetch_assoc($result);
                }
            } else {
                $error = 'Erro ao atualizar status: ' . db_error();
                error_log("Erro ao executar query: " . db_error());
            }
        } catch (Exception $e) {
            $error = 'Erro ao processar a atualização: ' . $e->getMessage();
            error_log("Exceção: " . $e->getMessage());
        }
    } else {
        $error = 'Status inválido.';
    }
}

// Get service call data
if ($id > 0) {
    $result = db_query("
        SELECT sc.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email, c.address as customer_address
        FROM service_calls sc
        JOIN customers c ON sc.customer_id = c.id
        WHERE sc.id = $id
    ");

    if ($result && db_num_rows($result) > 0) {
        $service_call = db_fetch_assoc($result);
    } else {
        $error = 'Chamado não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// Função para traduzir o status
function translate_status($status) {
    $translations = [
        'pending' => 'Pendente',
        'in_progress' => 'Em Andamento',
        'completed' => 'Concluído',
        'cancelled' => 'Cancelado'
    ];

    return isset($translations[$status]) ? $translations[$status] : $status;
}

// Função para obter a classe CSS do status
function get_status_class($status) {
    $classes = [
        'pending' => 'status-pending',
        'in_progress' => 'status-in-progress',
        'completed' => 'status-completed',
        'cancelled' => 'status-cancelled'
    ];

    return isset($classes[$status]) ? $classes[$status] : '';
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Chamado - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Estilos gerais */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.4;
            color: #333;
            background-color: #f5f7fa;
            font-size: 14px;
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .card-title {
            margin: 0;
            font-size: 1.2rem;
            color: #495057;
            font-weight: 600;
        }

        .card-body {
            padding: 12px 15px;
        }

        /* Detalhes do chamado */
        .service-call-details {
            background-color: #fff;
            border-radius: 6px;
            padding: 0;
            margin-bottom: 15px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            width: 150px;
            flex-shrink: 0;
            color: #495057;
            font-size: 13px;
        }

        .detail-value {
            flex: 1;
            color: #212529;
        }

        /* Status badges */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            text-align: center;
            min-width: 90px;
            letter-spacing: 0.3px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #b8daff;
        }

        .status-completed {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* Seções de informações */
        .customer-info, .service-info {
            margin-bottom: 15px;
            padding: 12px 15px;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.02);
        }

        .customer-info {
            background-color: #f0f9ff;
            border-left: 3px solid #17a2b8;
        }

        .service-info {
            background-color: #f8f9fa;
            border-left: 3px solid #6c757d;
        }

        .section-title {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
            margin-bottom: 12px;
            font-size: 1rem;
            color: #495057;
            font-weight: 600;
        }

        .service-history {
            background-color: #f8f9fa;
            padding: 12px 15px;
            border-radius: 6px;
            margin-top: 15px;
            white-space: pre-line;
            border-left: 3px solid #28a745;
            box-shadow: 0 1px 4px rgba(0,0,0,0.02);
            font-size: 13px;
        }

        /* Menu hamburger de ações */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .update-status-button {
            padding: 6px 10px;
            border-radius: 3px;
            background-color: #28a745;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            text-decoration: none;
        }

        .update-status-button:hover {
            background-color: #218838;
        }

        .actions-hamburger-menu {
            display: inline-block;
            position: relative;
            z-index: 1000;
        }

        .actions-hamburger-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 6px 12px;
            background-color: #007bff;
            border-radius: 4px;
            color: white;
            font-size: 13px;
            transition: background-color 0.2s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .actions-hamburger-icon:hover {
            background-color: #0069d9;
        }

        .actions-hamburger-icon i {
            margin-right: 6px;
        }

        .actions-menu-items {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 4px;
            padding: 5px 0;
            min-width: 180px;
            z-index: 1001;
            margin-top: 5px;
            border: 1px solid #e9ecef;
        }

        .actions-menu-items:before {
            content: '';
            position: absolute;
            top: -6px;
            right: 12px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid white;
        }

        .actions-menu-items.show {
            display: block;
            animation: fadeIn 0.15s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .actions-menu-items a,
        .actions-menu-items button {
            display: flex;
            align-items: center;
            width: 100%;
            text-align: left;
            padding: 6px 12px;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            background: none;
            font-size: 13px;
            cursor: pointer;
            font-weight: 500;
        }

        .actions-menu-items a:hover,
        .actions-menu-items button:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }

        .actions-menu-items i {
            width: 20px;
            margin-right: 8px;
            text-align: center;
            color: #6c757d;
        }

        .actions-menu-items a:hover i,
        .actions-menu-items button:hover i {
            color: #007bff;
        }

        /* Formulário de atualização de status */
        .status-update-form {
            margin-top: 10px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .status-select {
            padding: 6px 10px;
            border-radius: 3px;
            border: 1px solid #ced4da;
            background-color: white;
            font-size: 13px;
            width: 100%;
            max-width: 200px;
            margin-bottom: 8px;
        }

        /* Alertas */
        .alert {
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            border-left: 3px solid transparent;
            font-size: 13px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            body {
                font-size: 13px;
            }

            .container {
                padding: 0 8px;
            }

            .card-body {
                padding: 10px 12px;
            }

            .detail-row {
                flex-direction: column;
                margin-bottom: 6px;
                padding-bottom: 6px;
            }

            .detail-label {
                width: 100%;
                margin-bottom: 3px;
                font-size: 12px;
            }

            .card-header {
                flex-direction: row;
                justify-content: space-between;
                padding: 8px 12px;
            }

            .card-title {
                margin-bottom: 0;
                font-size: 1rem;
            }

            .section-title {
                font-size: 0.9rem;
                padding-bottom: 6px;
                margin-bottom: 8px;
            }

            .customer-info, .service-info {
                padding: 10px 12px;
                margin-bottom: 10px;
            }

            .service-history {
                padding: 10px 12px;
                margin-top: 10px;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav class="navbar">
                <div class="logo">Tony's AC Repair</div>

                <!-- Menu Normal -->
                <ul class="nav-links">
                    <li><a href="dashboard_simple.php">Painel</a></li>
                    <li><a href="customers.php">Clientes</a></li>
                    <li><a href="inventory.php">Estoque</a></li>
                    <li><a href="quotes.php">Orçamentos</a></li>
                    <li><a href="invoices_list.php">Faturas</a></li>
                    <li><a href="service_calls.php" class="active">Chamados</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <p><a href="service_calls.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Chamados</a></p>
            <?php elseif (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
                <?php if ($service_call): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title"><i class="fas fa-headset"></i> Chamado #<?php echo $service_call['id']; ?></h1>
                        <div class="action-buttons">
                            <!-- Botão de atualização de status -->
                            <button type="button" class="update-status-button" id="update-status-toggle-success">
                                <i class="fas fa-sync-alt"></i> Atualizar Status
                            </button>

                            <!-- Menu hamburger para ações -->
                            <div class="actions-hamburger-menu">
                                <div class="actions-hamburger-icon" id="actions-hamburger-button-success">
                                    <i class="fas fa-bars"></i> Ações
                                </div>
                                <div class="actions-menu-items" id="actions-menu-items-success">
                                    <a href="service_calls.php"><i class="fas fa-arrow-left"></i> Voltar</a>
                                    <a href="service_call_edit.php?id=<?php echo $id; ?>"><i class="fas fa-edit"></i> Editar</a>
                                    <a href="invoice_create.php?customer_id=<?php echo $service_call['customer_id']; ?>"><i class="fas fa-file-invoice-dollar"></i> Gerar Fatura</a>
                                    <a href="service_call_print.php?id=<?php echo $id; ?>" target="_blank"><i class="fas fa-print"></i> Imprimir</a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php elseif ($service_call): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title"><i class="fas fa-headset"></i> Chamado #<?php echo $service_call['id']; ?></h1>
                        <div class="action-buttons">
                            <!-- Botão de atualização de status -->
                            <button type="button" class="update-status-button" id="update-status-toggle">
                                <i class="fas fa-sync-alt"></i> Atualizar Status
                            </button>

                            <!-- Menu hamburger para ações -->
                            <div class="actions-hamburger-menu">
                                <div class="actions-hamburger-icon" id="actions-hamburger-button">
                                    <i class="fas fa-bars"></i> Ações
                                </div>
                                <div class="actions-menu-items" id="actions-menu-items">
                                    <a href="service_calls.php"><i class="fas fa-arrow-left"></i> Voltar</a>
                                    <a href="service_call_edit.php?id=<?php echo $id; ?>"><i class="fas fa-edit"></i> Editar</a>
                                    <a href="invoice_create.php?customer_id=<?php echo $service_call['customer_id']; ?>"><i class="fas fa-file-invoice-dollar"></i> Gerar Fatura</a>
                                    <button onclick="printServiceCall()"><i class="fas fa-print"></i> Imprimir</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="service-call-details">
                            <div class="customer-info">
                                <h3 class="section-title"><i class="fas fa-user-circle fa-sm"></i> Informações do Cliente</h3>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-user fa-sm"></i> Nome:</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_name']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-phone fa-sm"></i> Telefone:</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_phone']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-envelope fa-sm"></i> Email:</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_email']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-map-marker-alt fa-sm"></i> Endereço:</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_address']); ?></div>
                                </div>
                            </div>

                            <div class="service-info">
                                <h3 class="section-title"><i class="fas fa-info-circle fa-sm"></i> Informações do Chamado</h3>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-tag fa-sm"></i> Título:</div>
                                    <div class="detail-value"><?php echo htmlspecialchars($service_call['title']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-calendar fa-sm"></i> Data Agendada:</div>
                                    <div class="detail-value"><?php echo format_date($service_call['scheduled_date']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-info-circle fa-sm"></i> Status:</div>
                                    <div class="detail-value">
                                        <span class="status-badge <?php echo get_status_class($service_call['status']); ?>">
                                            <?php echo translate_status($service_call['status']); ?>
                                        </span>

                                        <form method="POST" action="service_call_view.php?id=<?php echo $id; ?>" class="status-update-form" id="status-update-form" style="display: none;">
                                            <h4 style="margin-top: 0; margin-bottom: 8px; font-size: 13px; color: #495057;"><i class="fas fa-exchange-alt"></i> Alterar Status</h4>
                                            <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center;">
                                                <div style="flex: 1; min-width: 150px;">
                                                    <select name="status" class="status-select">
                                                        <option value="pending" <?php echo ($service_call['status'] == 'pending') ? 'selected' : ''; ?>>Pendente</option>
                                                        <option value="in_progress" <?php echo ($service_call['status'] == 'in_progress') ? 'selected' : ''; ?>>Em Andamento</option>
                                                        <option value="completed" <?php echo ($service_call['status'] == 'completed') ? 'selected' : ''; ?>>Concluído</option>
                                                        <option value="cancelled" <?php echo ($service_call['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelado</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <button type="submit" name="update_status" class="btn btn-primary" style="padding: 6px 10px; border-radius: 3px; background-color: #007bff; color: white; border: none; cursor: pointer; font-weight: 500; display: inline-flex; align-items: center; gap: 5px; font-size: 12px;">
                                                        <i class="fas fa-save"></i> Salvar
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-clock fa-sm"></i> Criado em:</div>
                                    <div class="detail-value"><?php echo format_date($service_call['created_at']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-clock fa-sm"></i> Atualizado em:</div>
                                    <div class="detail-value"><?php echo format_date($service_call['updated_at']); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label"><i class="fas fa-align-left fa-sm"></i> Descrição:</div>
                                    <div class="detail-value"><?php echo nl2br(htmlspecialchars($service_call['description'])); ?></div>
                                </div>
                            </div>

                            <h3 class="section-title"><i class="fas fa-history fa-sm"></i> Histórico de Atendimento</h3>
                            <div class="service-history">
                                <?php if (empty($service_call['service_history'])): ?>
                                    <p>Nenhum histórico de atendimento registrado.</p>
                                <?php else: ?>
                                    <?php echo nl2br(htmlspecialchars($service_call['service_history'])); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Menu hamburger de ações toggle
            $('#actions-hamburger-button').click(function(e) {
                e.stopPropagation();
                $('#actions-menu-items').toggleClass('show');
                $('#actions-menu-items-success').removeClass('show');
            });

            // Menu hamburger de ações toggle (versão com sucesso)
            $('#actions-hamburger-button-success').click(function(e) {
                e.stopPropagation();
                $('#actions-menu-items-success').toggleClass('show');
                $('#actions-menu-items').removeClass('show');
            });

            // Botão de atualização de status toggle
            $('#update-status-toggle').click(function() {
                $('#status-update-form').slideToggle(200);
            });

            // Botão de atualização de status toggle (versão com sucesso)
            $('#update-status-toggle-success').click(function() {
                $('#status-update-form').slideToggle(200);
            });

            // Fechar menu ao clicar fora dele
            $(document).click(function(e) {
                if (!$(e.target).closest('.actions-hamburger-menu').length) {
                    $('.actions-menu-items').removeClass('show');
                }
            });

            // Menu toggle para dispositivos móveis (menu antigo)
            $('.menu-toggle').click(function() {
                $('.nav-links').toggleClass('active');
            });
        });

        // Função para imprimir o chamado
        function printServiceCall() {
            // Redirecionar para a página de impressão
            window.open('service_call_print.php?id=<?php echo $id; ?>', '_blank');

            // Conteúdo HTML para impressão - Não usado mais, redirecionando para service_call_print.php
            /*var printContent = `
                <!DOCTYPE html>
                <html lang="pt-BR">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Chamado #<?php echo $service_call['id']; ?> - Impressão</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            line-height: 1.6;
                            margin: 0;
                            padding: 20px;
                        }
                        .header {
                            text-align: center;
                            margin-bottom: 20px;
                            padding-bottom: 10px;
                            border-bottom: 2px solid #333;
                        }
                        .section {
                            margin-bottom: 20px;
                            padding: 15px;
                            border: 1px solid #ddd;
                            border-radius: 5px;
                        }
                        .section-title {
                            font-size: 18px;
                            font-weight: bold;
                            margin-bottom: 10px;
                            padding-bottom: 5px;
                            border-bottom: 1px solid #ddd;
                        }
                        .detail-row {
                            display: flex;
                            margin-bottom: 8px;
                        }
                        .detail-label {
                            font-weight: bold;
                            width: 150px;
                            flex-shrink: 0;
                        }
                        .detail-value {
                            flex: 1;
                        }
                        .status {
                            display: inline-block;
                            padding: 3px 8px;
                            border-radius: 3px;
                            font-weight: bold;
                        }
                        .status-pending {
                            background-color: #ffeeba;
                            color: #856404;
                        }
                        .status-in-progress {
                            background-color: #b8daff;
                            color: #004085;
                        }
                        .status-completed {
                            background-color: #c3e6cb;
                            color: #155724;
                        }
                        .status-cancelled {
                            background-color: #f5c6cb;
                            color: #721c24;
                        }
                        .footer {
                            margin-top: 30px;
                            text-align: center;
                            font-size: 12px;
                            color: #666;
                        }
                        @media print {
                            body {
                                padding: 0;
                                margin: 0;
                            }
                            .no-print {
                                display: none;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Chamado de Atendimento #<?php echo $service_call['id']; ?></h1>
                        <p>Data de Impressão: ${new Date().toLocaleString('pt-BR')}</p>
                    </div>

                    <div class="section">
                        <div class="section-title">Informações do Cliente</div>
                        <div class="detail-row">
                            <div class="detail-label">Nome:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_name']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Telefone:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_phone']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Email:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_email']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Endereço:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_address']); ?></div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Informações do Chamado</div>
                        <div class="detail-row">
                            <div class="detail-label">Título:</div>
                            <div class="detail-value"><?php echo htmlspecialchars($service_call['title']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Data Agendada:</div>
                            <div class="detail-value"><?php echo format_date($service_call['scheduled_date']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Status:</div>
                            <div class="detail-value">
                                <span class="status status-<?php echo $service_call['status']; ?>">
                                    <?php echo translate_status($service_call['status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Criado em:</div>
                            <div class="detail-value"><?php echo format_date($service_call['created_at']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Atualizado em:</div>
                            <div class="detail-value"><?php echo format_date($service_call['updated_at']); ?></div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Descrição:</div>
                            <div class="detail-value"><?php echo nl2br(htmlspecialchars($service_call['description'])); ?></div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Histórico de Atendimento</div>
                        <div>
                            <?php if (empty($service_call['service_history'])): ?>
                                <p>Nenhum histórico de atendimento registrado.</p>
                            <?php else: ?>
                                <?php echo nl2br(htmlspecialchars($service_call['service_history'])); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="footer">
                        <p>Tony's AC Repair LLC - <?php echo date('Y'); ?></p>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 20px;">
                        <button onclick="window.print()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            Imprimir
                        </button>
                        <button onclick="window.close()" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                            Fechar
                        </button>
                    </div>
                </body>
                </html>
            `;*/

            // Código antigo comentado - agora redirecionando para service_call_print.php
            /*
            printWindow.document.open();
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.focus();
            */
        }
    </script>
</body>
</html>
