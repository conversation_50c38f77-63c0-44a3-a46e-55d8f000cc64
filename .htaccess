# Forçar HTTPS
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Adicionar cabeçalhos de segurança
<IfModule mod_headers.c>
    # Strict Transport Security
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"

    # Prevenir MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # Proteção contra clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # Proteção XSS
    Header always set X-XSS-Protection "1; mode=block"

    # Política de referência
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Forçar atualização de recursos inseguros para seguros
    Header always set Content-Security-Policy "upgrade-insecure-requests"

    # Desativar cache para dispositivos móveis
    <FilesMatch "\.(html|htm|php)$">
        <If "%{HTTP_USER_AGENT} =~ /(android|iphone|ipad|ipod|blackberry|windows phone)/i">
            Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
            Header set Pragma "no-cache"
        </If>
    </FilesMatch>
</IfModule>

# PHP settings
<IfModule mod_php7.c>
    php_flag session.cookie_httponly on
    php_flag session.cookie_secure on
    php_flag session.use_only_cookies on
</IfModule>

# Prevent directory listing
Options -Indexes

# Protect .htaccess file
<Files .htaccess>
    Order Allow,Deny
    Deny from all
</Files>

# Protect sensitive files
<FilesMatch "^(db_connect\.php|security_check\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Ensure correct MIME types for CSS and JS files
<IfModule mime_module>
    AddType text/css .css
    AddType application/javascript .js
</IfModule>

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php82” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php82 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
