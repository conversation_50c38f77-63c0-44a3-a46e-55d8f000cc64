<?php
// Configuração personalizada de sessão
// Este arquivo é incluído no início de cada script que usa sessões

// Definir o caminho de sessão para um diretório local
session_save_path('/home/<USER>/public_html/sessions');

// Outras configurações de sessão
ini_set('session.gc_maxlifetime', 86400); // 24 horas
ini_set('session.cookie_lifetime', 86400); // 24 horas
ini_set('session.use_cookies', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Definir como 1 se estiver usando HTTPS
ini_set('session.use_trans_sid', 0);
ini_set('session.cache_limiter', 'nocache');
ini_set('session.hash_function', 'sha256');

// Iniciar a sessão se ainda não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>