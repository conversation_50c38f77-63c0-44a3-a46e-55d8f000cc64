<?php
// Include session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Check if this is a shared link view
$is_shared = isset($_GET['shared']) && $_GET['shared'] == 1;

// Check if user is logged in (only if not a shared view)
if (!$is_shared && !isset($_SESSION['user_id'])) {
    die('Acesso negado. Faça login para continuar.');
}

// Get invoice ID
$invoice_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($invoice_id <= 0) {
    die('ID da fatura inválido.');
}

// Fetch invoice data
$query = "SELECT i.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.address as customer_address 
          FROM invoices i 
          JOIN customers c ON i.customer_id = c.id 
          WHERE i.id = $invoice_id";

$result = db_query($query);
if (!$result || db_num_rows($result) == 0) {
    die('Fatura não encontrada.');
}

$invoice = db_fetch_assoc($result);

// Fetch invoice items
$items_query = "SELECT * FROM invoice_items WHERE invoice_id = $invoice_id ORDER BY id";
$items_result = db_query($items_query);

// Convert result to array
$items = [];
while ($item = db_fetch_assoc($items_result)) {
    $items[] = $item;
}

// Fetch payment history - try both table names for compatibility
$payments_query = "SELECT * FROM invoice_payments WHERE invoice_id = $invoice_id ORDER BY payment_date DESC";
$payments_result = db_query($payments_query);

// If invoice_payments doesn't exist, try payments table
if (!$payments_result) {
    $payments_query = "SELECT * FROM payments WHERE invoice_id = $invoice_id ORDER BY payment_date DESC";
    $payments_result = db_query($payments_query);
}

$payments = [];
$total_paid = 0;
while ($payment = db_fetch_assoc($payments_result)) {
    $payments[] = $payment;
    $total_paid += $payment['amount'];
}

// Calculate remaining amount
$remaining_amount = $invoice['total'] - $total_paid;

// Determine actual payment status based on payments
$actual_status = 'unpaid';
if (abs($remaining_amount) < 0.01) {
    $actual_status = 'paid';
} elseif ($total_paid > 0) {
    $actual_status = 'partial';
}

// Close database connection
db_close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?> - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/invoice-pdf.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
    /* Ajuste do body para elementos fixos - sobrescreve CSS externo */
    body {
        padding-top: 48px !important; /* Espaço para o botão de impressão */
    }

    body.shared-view {
        padding-top: 90px !important; /* Espaço adicional para o banner quando compartilhado */
    }

    /* Regras específicas para impressão - garante padding consistente */
    @media print {
        body {
            padding: 0.2in !important; /* Padding padrão para impressão */
            padding-top: 0.2in !important; /* Remove padding extra do topo na impressão */
            margin: 0 !important; /* Remove margin que pode estar interferindo */
        }

        body.shared-view {
            padding: 0.2in !important; /* Mesmo padding para visualização compartilhada */
            padding-top: 0.2in !important;
            margin: 0 !important;
        }

        /* Garante que elementos fixos não apareçam na impressão */
        .no-print {
            display: none !important;
        }
    }

    /* Payment Status Styles */
    .payment-status {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        font-size: 12px;
        text-transform: uppercase;
    }

    .status-paid {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-unpaid {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-partial {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    /* Payment Flex Layout Styles */
    .payment-flex-container {
        display: flex;
        gap: 20px;
        margin-top: 10px;
        font-size: 9px;
        align-items: flex-start;
    }

    /* Resumo à Esquerda */
    .payment-summary-left {
        flex: 1;
        min-width: 200px;
        padding: 10px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    /* Histórico à Direita */
    .payment-history-right {
        flex: 2;
        min-width: 300px;
    }

    .history-title {
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 9px;
    }

    .payment-history-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 9px;
    }

    .payment-history-table th,
    .payment-history-table td {
        border: 1px solid #ddd;
        padding: 4px;
        font-size: 9px;
    }

    .payment-history-table th {
        background-color: #f5f5f5;
        font-weight: bold;
        text-align: left;
    }

    .payment-history-table .amount-cell {
        text-align: right;
    }

    .no-payments {
        color: #999;
        font-style: italic;
        padding: 20px;
        text-align: center;
        border: 1px solid #ddd;
        background-color: #f9f9f9;
    }

    .status-section {
        text-align: center;
        margin-bottom: 15px;
    }

    .summary-details {
        font-size: 9px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 4px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .summary-item:last-child {
        border-bottom: none;
        font-weight: bold;
    }

    .summary-label {
        font-weight: bold;
        color: #495057;
    }

    .summary-value {
        color: #212529;
        font-weight: bold;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
        .payment-flex-container {
            flex-direction: column;
            gap: 15px;
        }

        .payment-summary-left,
        .payment-history-right {
            flex: none;
            min-width: auto;
        }

        .payment-history-table {
            font-size: 8px;
        }

        .payment-history-table th,
        .payment-history-table td {
            padding: 3px;
            font-size: 8px;
        }

        .summary-details {
            font-size: 8px;
        }
    }

    /* Print-specific adjustments */
    @media print {
        .payment-flex-container {
            display: flex !important;
            flex-direction: row !important;
            gap: 15px !important;
        }

        .payment-summary-left {
            flex: 1 !important;
            min-width: 180px !important;
        }

        .payment-history-right {
            flex: 2 !important;
            min-width: 280px !important;
        }

        .payment-history-table {
            font-size: 8px !important;
        }

        .payment-history-table th,
        .payment-history-table td {
            padding: 3px !important;
            font-size: 8px !important;
        }

        .summary-details {
            font-size: 8px !important;
        }
    }
    </style>
</head>
<body<?php echo $is_shared ? ' class="shared-view"' : ''; ?>>
    <!-- Print Button (always show) -->
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print" style="margin-right: 8px;"></i>Print
    </button>

    <!-- Shared View Header -->
    <?php if ($is_shared): ?>
    <div style="background: #f8f9fa; padding: 8px 15px; text-align: center; border-bottom: 1px solid #dee2e6; margin: 0; width: 100%; position: fixed; top: 48px; left: 0; right: 0; z-index: 999;" class="no-print">
        <p style="margin: 0; color: #6c757d; font-size: 12px; font-weight: normal;">
            <i class="fas fa-share-alt" style="color: #6c757d; margin-right: 6px; font-size: 11px;"></i>
            You are viewing a shared invoice link
        </p>
    </div>
    <?php endif; ?>

    <!-- Print Page Wrapper -->
    <div class="print-page">
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Invoice Header -->
    <div class="header">
        <div class="company-info">
            <h1>
                <img src="assets/logo_Tonys.svg" width="32" height="32" alt="Tony's AC Repair Logo">
                Tony's AC Repair
            </h1>
            <div class="company-details">
                Professional Air Conditioning Services<br>
                (*************<br>
                <EMAIL>
            </div>
        </div>
        <div class="invoice-info">
            <div class="quote-title">INVOICE</div>
            <div class="invoice-number"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
            <div class="invoice-dates">
                Issued: <?php echo date('m/d/Y', strtotime($invoice['invoice_date'])); ?><br>
                Due: <?php echo date('m/d/Y', strtotime(isset($invoice['due_date']) ? $invoice['due_date'] : $invoice['invoice_date'] . ' +1 day')); ?>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="two-columns">
        <div>
            <div class="section-title label-with-icon">
                <i class="fas fa-user label-icon"></i>
                Customer
            </div>
            <div class="customer-info">
                <div class="customer-name"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                <div>
                    <?php if ($invoice['customer_phone']): ?>
                        Phone: <?php echo htmlspecialchars($invoice['customer_phone']); ?><br>
                    <?php endif; ?>
                    <?php if ($invoice['customer_email']): ?>
                        Email: <?php echo htmlspecialchars($invoice['customer_email']); ?><br>
                    <?php endif; ?>
                    <?php if ($invoice['customer_address']): ?>
                        Address: <?php echo htmlspecialchars($invoice['customer_address']); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div>
            <div class="section-title label-with-icon">
                <i class="fas fa-map-marker-alt label-icon"></i>
                Service Address
            </div>
            <div class="customer-info">
                <?php if ($invoice['service_address']): ?>
                    <div><?php echo htmlspecialchars($invoice['service_address']); ?></div>
                <?php else: ?>
                    <div style="color: #999; font-style: italic;">Same as customer address</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Items & Services -->
    <div class="section-title label-with-icon">
        <i class="fas fa-list label-icon"></i>
        Items & Services
    </div>
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 15%;" class="text-center">Qty</th>
                <th style="width: 17.5%;" class="text-right">Price</th>
                <th style="width: 17.5%;" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($items as $item): ?>
            <tr>
                <td><?php echo htmlspecialchars($item['description']); ?></td>
                <td class="text-center"><?php echo number_format($item['quantity'], 0); ?></td>
                <td class="text-right">$<?php echo number_format($item['price'], 2); ?></td>
                <td class="text-right">$<?php echo number_format($item['quantity'] * $item['price'], 2); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Invoice Totals -->
    <div class="totals">
        <table>
            <tr>
                <td class="total-label">Subtotal:</td>
                <td class="total-amount">$<?php echo number_format(isset($invoice['subtotal']) ? $invoice['subtotal'] : 0, 2); ?></td>
            </tr>
            <tr>
                <td class="total-label">Tax:</td>
                <td class="total-amount">$<?php echo number_format(isset($invoice['tax_amount']) ? $invoice['tax_amount'] : 0, 2); ?></td>
            </tr>
            <?php if (isset($invoice['discount_amount']) && $invoice['discount_amount'] > 0): ?>
            <tr>
                <td class="total-label">Discount:</td>
                <td class="total-amount">-$<?php echo number_format($invoice['discount_amount'], 2); ?></td>
            </tr>
            <?php endif; ?>
            <tr class="grand-total">
                <td class="total-label">TOTAL:</td>
                <td class="total-amount">$<?php echo number_format(isset($invoice['total']) ? $invoice['total'] : 0, 2); ?></td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    <div class="section-title label-with-icon">
        <i class="fas fa-sticky-note label-icon"></i>
        Notes
    </div>
    <div class="notes">
        <?php if (isset($invoice['notes']) && !empty($invoice['notes'])): ?>
            <div><?php echo nl2br(htmlspecialchars($invoice['notes'])); ?></div>
        <?php else: ?>
            <div style="color: #999; font-style: italic;">No additional notes</div>
        <?php endif; ?>
    </div>

    <!-- Payment Information -->
    <div class="section-title">Payment Information</div>
    <div class="payment-info">
        <div class="payment-flex-container">
            <!-- Resumo à Esquerda -->
            <div class="payment-summary-left">
                <div class="status-section">
                    <span class="payment-status status-<?php echo $actual_status; ?>">
                        <?php
                        switch($actual_status) {
                            case 'paid': echo 'PAID'; break;
                            case 'unpaid': echo 'UNPAID'; break;
                            case 'partial': echo 'PARTIAL'; break;
                            default: echo 'UNPAID';
                        }
                        ?>
                    </span>
                </div>

                <div class="summary-details">
                    <div class="summary-item">
                        <span class="summary-label">Total Invoice:</span>
                        <span class="summary-value">$<?php echo number_format($invoice['total'], 2); ?></span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Paid:</span>
                        <span class="summary-value">$<?php echo number_format($total_paid, 2); ?></span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Remaining:</span>
                        <span class="summary-value">$<?php echo number_format($remaining_amount, 2); ?></span>
                    </div>
                </div>
            </div>

            <!-- Tabela de Histórico à Direita -->
            <div class="payment-history-right">
                <?php if (!empty($payments)): ?>
                    <div class="history-title">Payment History:</div>
                    <table class="payment-history-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Method</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td><?php echo date('m/d/Y', strtotime($payment['payment_date'])); ?></td>
                                <td><?php echo htmlspecialchars($payment['payment_method']); ?></td>
                                <td class="amount-cell">$<?php echo number_format($payment['amount'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="no-payments">No payment history available</div>
                <?php endif; ?>
            </div>
        </div>

        <div style="margin-top: 10px;">
            <strong>Payment Methods:</strong> Cash, Zelle, Transfer
        </div>
    </div>
        </div> <!-- End Content Wrapper -->

    <!-- Footer -->
    <div class="footer">
        <p><strong>Thank you for your business!</strong></p>
        <p>Tony's AC Repair - Your comfort is our priority</p>
        <p>For questions about this invoice, contact us: (*************</p>
    </div>
    </div> <!-- End Print Page Wrapper -->
</body>
</html>
