<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Determina qual página está ativa
$current_page = basename($_SERVER['PHP_SELF']);

// Get search and filter parameters
$search_query = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter_status = isset($_GET['filter_status']) ? $_GET['filter_status'] : 'all';
$sort_field = isset($_GET['sort']) ? $_GET['sort'] : 'invoice_date';
$sort_order = isset($_GET['order']) ? $_GET['order'] : 'DESC';

// Build query based on filters
$query = "
    SELECT i.*, c.name as customer_name, c.address as customer_address
    FROM invoices i
    JOIN customers c ON i.customer_id = c.id
    WHERE 1=1
";

// Add search filter (search in all fields)
if (!empty($search_query)) {
    $search_escaped = db_escape($search_query);
    $query .= " AND (
        i.invoice_number LIKE '%$search_escaped%' OR
        c.name LIKE '%$search_escaped%' OR
        c.address LIKE '%$search_escaped%' OR
        i.service_address LIKE '%$search_escaped%' OR
        i.total LIKE '%$search_escaped%' OR
        i.status LIKE '%$search_escaped%'
    )";
}

// Add status filter
if ($filter_status !== 'all') {
    $status_escaped = db_escape($filter_status);
    $query .= " AND i.status = '$status_escaped'";
}

// Add sorting
$allowed_sort_fields = ['invoice_date', 'status', 'total', 'invoice_number', 'customer_name', 'service_address'];
if (in_array($sort_field, $allowed_sort_fields)) {
    if ($sort_field === 'customer_name') {
        $sort_column = 'c.name';
    } elseif ($sort_field === 'invoice_date') {
        $sort_column = 'DATE(i.invoice_date)';
    } elseif ($sort_field === 'service_address') {
        $sort_column = 'i.service_address';
    } else {
        $sort_column = 'i.' . $sort_field;
    }
    $query .= " ORDER BY $sort_column " . ($sort_order === 'ASC' ? 'ASC' : 'DESC');
} else {
    $query .= " ORDER BY DATE(i.invoice_date) DESC";
}

// Debug: log the final query (remove in production)
// error_log("Invoice query: " . $query);

// Get all invoices
$invoices = [];
$result = db_query($query);

if ($result && db_num_rows($result) > 0) {
    $invoices = db_fetch_all($result);
}

// Check if this is an AJAX request
$is_ajax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest';

if ($is_ajax) {
    // Return only the table content for AJAX requests
    include 'invoices_list_table_only.php';
    exit;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="color-scheme" content="light">
    <title>Invoices List - Tonys AC Repair</title>
    <!-- <link rel="stylesheet" href="css/style.css"> -->
    <link rel="stylesheet" href="css/invoices-list-mobile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <h1>Tony's AC Repair</h1>
        <button class="menu-toggle" id="menuToggle" aria-expanded="false" aria-controls="slideMenu" aria-label="Toggle navigation menu">
            <i class="fas fa-bars"></i>
        </button>
    </header>

    <!-- Slide Menu -->
    <nav class="slide-menu" id="slideMenu" role="navigation" aria-label="Main navigation">
        <div class="menu-grid">
            <a href="dashboard_simple.php" class="menu-item <?php echo ($current_page == 'dashboard_simple.php') ? 'active' : ''; ?>">
                <i class="fa fa-home"></i>
                <span>Painel</span>
            </a>
            <a href="customers.php" class="menu-item <?php echo ($current_page == 'customers.php') ? 'active' : ''; ?>">
                <i class="fa fa-users"></i>
                <span>Clientes</span>
            </a>
            <a href="inventory.php" class="menu-item <?php echo ($current_page == 'inventory.php') ? 'active' : ''; ?>">
                <i class="fa fa-box"></i>
                <span>Estoque</span>
            </a>
            <a href="quotes.php" class="menu-item <?php echo ($current_page == 'quotes.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice"></i>
                <span>Orçamentos</span>
            </a>
            <a href="invoices_list.php" class="menu-item <?php echo ($current_page == 'invoices.php' || $current_page == 'invoices_list.php') ? 'active' : ''; ?>">
                <i class="fa fa-file-invoice-dollar"></i>
                <span>Faturas</span>
            </a>
            <a href="service_calls.php" class="menu-item <?php echo ($current_page == 'service_calls.php') ? 'active' : ''; ?>">
                <i class="fa fa-headset"></i>
                <span>Chamados</span>
            </a>
            <a href="shared_links.php" class="menu-item <?php echo ($current_page == 'shared_links.php') ? 'active' : ''; ?>">
                <i class="fa fa-share-alt"></i>
                <span>Links</span>
            </a>
            <a href="logout.php" class="menu-item">
                <i class="fa fa-sign-out-alt"></i>
                <span>Sair</span>
            </a>
        </div>
    </nav>

    <!-- Menu Overlay -->
    <div class="menu-overlay" id="menuOverlay" aria-hidden="true"></div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-full">
            <!-- Cabeçalho da página -->
            <div class="page-header">
                <h1>Invoices</h1>
                <a href="invoice_create.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New
                </a>
            </div>

            <!-- Search Section -->
            <div class="search-filter-section">
                <form method="GET" id="searchForm">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   class="search-input"
                                   name="search"
                                   placeholder="Buscar faturas..."
                                   value="<?php echo htmlspecialchars($search_query); ?>"
                                   id="searchInput">
                            <button type="button" class="clear-search-btn" onclick="clearAllFilters()" title="Limpar filtros" id="clearBtn">
                                <i class="fas fa-times"></i> limpar
                            </button>
                        </div>
                    </div>
                    <input type="hidden" name="filter_status" value="<?php echo htmlspecialchars($filter_status); ?>">
                    <input type="hidden" name="sort" value="<?php echo htmlspecialchars($sort_field); ?>">
                    <input type="hidden" name="order" value="<?php echo htmlspecialchars($sort_order); ?>">
                </form>
            </div>

            <!-- Invoices Table -->
            <div class="invoices-table">
                <table class="table-mobile" id="invoicesTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable('invoice_number')" style="cursor: pointer;">
                                    ID <i class="fas fa-sort<?php
                                        if ($sort_field === 'invoice_number') {
                                            echo $sort_order === 'ASC' ? '-up' : '-down';
                                        }
                                    ?> sort-icon <?php echo $sort_field === 'invoice_number' ? 'active' : ''; ?>"></i>
                                </th>
                                <th onclick="sortTable('invoice_date')" style="cursor: pointer;">
                                    Date <i class="fas fa-sort<?php
                                        if ($sort_field === 'invoice_date') {
                                            echo $sort_order === 'ASC' ? '-up' : '-down';
                                        }
                                    ?> sort-icon <?php echo $sort_field === 'invoice_date' ? 'active' : ''; ?>"></i>
                                </th>
                                <th onclick="sortTable('customer_name')" style="cursor: pointer;">
                                    Name <i class="fas fa-sort<?php
                                        if ($sort_field === 'customer_name') {
                                            echo $sort_order === 'ASC' ? '-up' : '-down';
                                        }
                                    ?> sort-icon <?php echo $sort_field === 'customer_name' ? 'active' : ''; ?>"></i>
                                </th>
                                <th onclick="sortTable('service_address')" style="cursor: pointer;">
                                    Address <i class="fas fa-sort<?php
                                        if ($sort_field === 'service_address') {
                                            echo $sort_order === 'ASC' ? '-up' : '-down';
                                        }
                                    ?> sort-icon <?php echo $sort_field === 'service_address' ? 'active' : ''; ?>"></i>
                                </th>
                                <th onclick="sortTable('total')" style="cursor: pointer;">
                                    Total <i class="fas fa-sort<?php
                                        if ($sort_field === 'total') {
                                            echo $sort_order === 'ASC' ? '-up' : '-down';
                                        }
                                    ?> sort-icon <?php echo $sort_field === 'total' ? 'active' : ''; ?>"></i>
                                </th>
                                <th onclick="filterByStatus()" style="cursor: pointer;" id="statusHeader" class="status-header">
                                    <div class="status-header-content">
                                        <div class="status-text">
                                            <?php
                                            $status_labels = ['All', 'Paid', 'Unpaid', 'Partial', 'Cancelled'];
                                            $status_values = ['all', 'paid', 'unpaid', 'partial', 'cancelled'];
                                            $current_index = array_search($filter_status, $status_values);
                                            if ($current_index === false) $current_index = 0;
                                            echo $status_labels[$current_index];
                                            ?>
                                        </div>
                                        <i class="fas fa-filter filter-icon <?php echo $filter_status !== 'all' ? 'active' : ''; ?>"></i>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $index => $invoice): ?>
                                <tr onclick="selectRow(<?php echo $index; ?>)" data-invoice-id="<?php echo $invoice['id']; ?>" data-invoice-number="<?php echo htmlspecialchars($invoice['invoice_number']); ?>" data-customer-name="<?php echo htmlspecialchars($invoice['customer_name']); ?>">
                                    <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                                    <td><?php echo date('m/d', strtotime($invoice['invoice_date'])); ?></td>
                                    <td title="<?php echo htmlspecialchars($invoice['customer_name']); ?>"><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                                    <td title="<?php echo htmlspecialchars($invoice['service_address'] ?? ''); ?>"><?php echo htmlspecialchars($invoice['service_address'] ?? ''); ?></td>
                                    <td>$<?php echo number_format($invoice['total'], 2, '.', ','); ?></td>
                                    <td>
                                        <?php
                                        $status = '';
                                        switch ($invoice['status']) {
                                            case 'paid':
                                                $status = '<span class="status-paid" title="Paid">P</span>';
                                                break;
                                            case 'unpaid':
                                                $status = '<span class="status-unpaid" title="Unpaid">U</span>';
                                                break;
                                            case 'partial':
                                                $status = '<span class="status-partial" title="Partial">Pa</span>';
                                                break;
                                            case 'cancelled':
                                                $status = '<span class="status-cancelled" title="Cancelled">C</span>';
                                                break;
                                            default:
                                                $status = '<span title="' . htmlspecialchars($invoice['status']) . '">' . htmlspecialchars(substr($invoice['status'], 0, 2)) . '</span>';
                                        }
                                        echo $status;
                                        ?>
                                    </td>
                                </tr>
                                <tr class="action-row" id="actionRow<?php echo $index; ?>">
                                    <td colspan="6">
                                        <div class="action-buttons">
                                            <a href="generate_invoice_pdf.php?id=<?php echo $invoice['id']; ?>" class="btn btn-view" title="View" target="_blank">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="invoice_edit.php?id=<?php echo $invoice['id']; ?>" class="btn btn-edit" title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <button onclick="openPaymentModal(<?php echo $invoice['id']; ?>, '<?php echo htmlspecialchars($invoice['invoice_number']); ?>', '<?php echo htmlspecialchars($invoice['customer_name']); ?>')" class="btn btn-payment" title="Payment">
                                                <i class="fas fa-dollar-sign"></i> Payment
                                            </button>
                                            <button onclick="shareInvoice(<?php echo $invoice['id']; ?>, '<?php echo htmlspecialchars($invoice['invoice_number']); ?>', '<?php echo htmlspecialchars($invoice['customer_name']); ?>')" class="btn btn-share" title="Share">
                                                <i class="fas fa-share-alt"></i> Share
                                            </button>
                                            <button onclick="confirmDelete(<?php echo $invoice['id']; ?>, '<?php echo htmlspecialchars($invoice['invoice_number']); ?>', '<?php echo htmlspecialchars($invoice['customer_name']); ?>')" class="btn btn-delete" title="Delete">
                                                <i class="fas fa-trash-alt"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <?php if (empty($invoices)): ?>
                            <tbody>
                                <tr>
                                    <td colspan="6" style="padding: 2rem; text-align: center; color: #6c757d; font-style: italic;">
                                        Nenhuma fatura encontrada.
                                        <br><br>
                                        <button onclick="clearAllFilters()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; font-size: 0.9rem;">
                                            <i class="fas fa-times"></i> Limpar Filtros
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        <?php endif; ?>
                    </table>
                </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <h3>Confirmar Exclusão</h3>
            <p>Tem certeza que deseja excluir a fatura?</p>
            <p><strong>Cliente:</strong> <span id="deleteCustomerName"></span></p>
            <p><strong>Número da Fatura:</strong> <span id="deleteInvoiceNumber"></span></p>
            <div class="modal-buttons">
                <button onclick="closeDeleteModal()" class="btn btn-cancel">Cancelar</button>
                <button onclick="executeDelete()" class="btn btn-confirm">Excluir</button>
            </div>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="paymentModal" class="modal">
        <div class="modal-content payment-modal-content">
            <div class="payment-modal-header">
                <h3>Histórico de Pagamentos</h3>
                <button onclick="closePaymentModal()" class="close-btn" title="Fechar">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="payment-modal-body">
                <div class="invoice-info">
                    <p><strong>Fatura:</strong> <span id="paymentInvoiceNumber"></span></p>
                    <p><strong>Cliente:</strong> <span id="paymentCustomerName"></span></p>
                    <p><strong>Total:</strong> $<span id="paymentInvoiceTotal"></span></p>
                    <p><strong>Pago:</strong> $<span id="paymentTotalPaid"></span></p>
                    <p><strong>Restante:</strong> $<span id="paymentRemaining"></span></p>
                    <p><strong>Status:</strong> <span id="paymentStatus"></span></p>
                </div>

                <div class="payment-history">
                    <h4>Histórico de Pagamentos</h4>
                    <div class="payment-history-table">
                        <table id="paymentsTable">
                            <thead>
                                <tr>
                                    <th>Data</th>
                                    <th>Valor</th>
                                    <th>Método</th>
                                    <th>Observações</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody id="paymentsTableBody">
                                <!-- Payment history will be loaded here -->
                            </tbody>
                        </table>
                        <div id="noPaymentsMessage" style="display: none; text-align: center; padding: 1rem; color: #6c757d; font-style: italic;">
                            Nenhum pagamento registrado ainda.
                        </div>
                    </div>
                </div>

                <div class="add-payment-section">
                    <h4 id="paymentFormTitle">Adicionar Pagamento</h4>
                    <form id="addPaymentForm">
                        <input type="hidden" id="editingPaymentId" name="payment_id">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="paymentDate">Data:</label>
                                <input type="date" id="paymentDate" name="payment_date" required>
                            </div>
                            <div class="form-group">
                                <label for="paymentAmount">Valor:</label>
                                <input type="number" id="paymentAmount" name="amount" step="0.01" min="0.01" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="paymentMethod">Método:</label>
                                <select id="paymentMethod" name="payment_method" required>
                                    <option value="">Selecione...</option>
                                    <option value="cash">Dinheiro</option>
                                    <option value="check">Cheque</option>
                                    <option value="credit_card">Cartão de Crédito</option>
                                    <option value="debit_card">Cartão de Débito</option>
                                    <option value="bank_transfer">Transferência Bancária</option>
                                    <option value="pix">PIX</option>
                                    <option value="other">Outro</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="paymentNotes">Observações:</label>
                                <textarea id="paymentNotes" name="notes" rows="1" placeholder="Observações opcionais..."></textarea>
                            </div>
                        </div>
                        <div class="form-buttons">
                            <button type="button" onclick="cancelEditPayment()" class="btn btn-cancel">Cancelar</button>
                            <button type="submit" class="btn btn-primary" id="registerPaymentBtn">
                                <i class="fas fa-save"></i> <span id="submitBtnText">Registrar Pagamento</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="js/invoices-list-mobile.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
