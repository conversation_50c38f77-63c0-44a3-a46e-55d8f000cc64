<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Include strict permissions if available
if (file_exists('strict_permissions.php')) {
    require_once 'strict_permissions.php';

    // Check if user has permission to access inventory
    if (function_exists('strict_has_role')) {
        if (strict_has_role('user')) {
            // Redirect user to dashboard
            header('Location: dashboard_simple.php');
            exit;
        }
    } else {
        // Fallback to session role check
        if (isset($_SESSION['user_role']) && strtolower($_SESSION['user_role']) === 'user') {
            // Redirect user to dashboard
            header('Location: dashboard_simple.php');
            exit;
        }
    }
}

// Initialize variables
$name = $description = $sku = $category = '';
$price = $cost = $quantity = 0;
$error = $success = '';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $name = isset($_POST['name']) ? $_POST['name'] : '';
    $description = isset($_POST['description']) ? $_POST['description'] : '';
    $sku = isset($_POST['sku']) ? $_POST['sku'] : '';
    $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;
    $cost = isset($_POST['cost']) ? floatval($_POST['cost']) : 0;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 0;
    $category = isset($_POST['category']) ? $_POST['category'] : '';

    // Validate form data
    if (empty($name)) {
        $error = 'Product name is required.';
    } elseif ($price <= 0) {
        $error = 'Price must be greater than zero.';
    } else {
        // Escape all input values to prevent SQL injection
        $name = db_escape($name);
        $description = db_escape($description);
        $sku = db_escape($sku);
        $category = db_escape($category);

        // Create and execute query
        $query = "INSERT INTO inventory (name, description, sku, price, cost, quantity, category) "
               . "VALUES ('$name', '$description', '$sku', $price, $cost, $quantity, '$category')";

        if (db_query($query)) {
            $success = 'Product registered successfully!';
            // Clear form data
            $name = $description = $sku = $category = '';
            $price = $cost = $quantity = 0;
        } else {
            $error = 'Error registering product: ' . db_error();
        }
    }
}

// Get all inventory items
$inventory = [];
$result = db_query("SELECT * FROM inventory ORDER BY name");

if ($result && db_num_rows($result) > 0) {
    $inventory = db_fetch_all($result);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <style>
        .inventory-form-container {
            max-width: 800px;
            margin: 0 auto 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }

        .form-header h1 {
            color: #2c3e50;
            font-size: 24px;
            margin: 0;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }

        .form-col {
            flex: 1;
            padding: 0 10px;
            min-width: 200px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .form-actions {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .required-field {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="inventory-form-container">
                <div class="form-header">
                    <h1>PRODUCT REGISTRATION</h1>
                </div>

                <form id="inventory-form" method="POST" action="inventory.php" class="needs-validation">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="name">Name <span class="required-field">*</span></label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="sku">SKU / Code</label>
                                <input type="text" id="sku" name="sku" value="<?php echo htmlspecialchars($sku); ?>">
                            </div>

                            <div class="form-group">
                                <label for="category">Category</label>
                                <input type="text" id="category" name="category" value="<?php echo htmlspecialchars($category); ?>">
                            </div>
                        </div>

                        <div class="form-col">
                            <div class="form-group">
                                <label for="price">Sale Price <span class="required-field">*</span></label>
                                <input type="number" id="price" name="price" step="0.01" min="0" value="<?php echo $price; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="cost">Cost</label>
                                <input type="number" id="cost" name="cost" step="0.01" min="0" value="<?php echo $cost; ?>">
                            </div>

                            <div class="form-group">
                                <label for="quantity">Quantity in Stock</label>
                                <input type="number" id="quantity" name="quantity" step="1" min="0" value="<?php echo $quantity; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description"><?php echo htmlspecialchars($description); ?></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-register"><i class="fas fa-box-open"></i> Register Product</button>
                    </div>
                </form>
            </div>

            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h2 class="card-title">Product List</h2>
                    <div>
                        <a href="#inventory-form" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); document.getElementById('name').focus(); return false;" class="btn btn-success" style="display: inline-flex; align-items: center;"><i class="fas fa-plus-circle" style="margin-right: 5px;"></i> New Product</a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($inventory)): ?>
                        <p>No products registered yet.</p>
                    <?php else: ?>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                            <thead>
                                <tr>
                                    <th data-sort="name">Name</th>
                                    <th data-sort="sku">SKU</th>
                                    <th data-sort="price">Price</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($inventory as $item): ?>
                                    <tr>
                                        <td data-column="name"><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td data-column="sku"><?php echo htmlspecialchars($item['sku']); ?></td>
                                        <td data-column="price">$ <?php echo number_format($item['price'], 2, '.', ','); ?></td>
                                        <td>
                                            <a href="inventory_edit.php?id=<?php echo $item['id']; ?>" class="btn btn-secondary" style="display: inline-flex; align-items: center; margin-right: 5px;"><i class="fas fa-edit" style="margin-right: 5px;"></i> Edit</a>
                                            <a href="inventory_delete.php?id=<?php echo $item['id']; ?>" class="btn btn-danger delete-btn" style="display: inline-flex; align-items: center;"><i class="fas fa-trash-alt" style="margin-right: 5px;"></i> Delete</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair LLC. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <!-- <script src="js/mobile-menu-fix-new.js"></script> -->
</body>
</html>
<?php
// Close database connection
db_close();
?>
