<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = '';
$item = null;

// Get inventory item
if ($id > 0) {
    $result = db_query("SELECT * FROM inventory WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $item = db_fetch_assoc($result);
    } else {
        $error = 'Produto não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Produto - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Estilos para a página de visualização */
        .product-details {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .detail-label {
            font-weight: bold;
            width: 150px;
            flex-shrink: 0;
        }

        .detail-value {
            flex: 1;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn i {
            font-size: 0.9em;
        }

        .card {
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .card-header {
            padding: 12px 15px;
            background-color: #f8f9fa;
        }

        .card-body {
            padding: 15px;
        }

        .card-title {
            margin: 0;
            font-size: 1.3rem;
        }

        .inventory-icon {
            color: #4a6cf7;
            margin-right: 5px;
        }

        .price-highlight {
            font-size: 1.2em;
            color: #28a745;
            font-weight: bold;
        }

        .stock-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.9em;
            font-weight: bold;
        }

        .in-stock {
            background-color: #d4edda;
            color: #155724;
        }

        .low-stock {
            background-color: #fff3cd;
            color: #856404;
        }

        .out-of-stock {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
                <p><a href="inventory.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Estoque</a></p>
            <?php elseif ($item): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title"><i class="fas fa-box inventory-icon"></i>Detalhes do Produto</h1>
                    </div>
                    <div class="card-body">
                        <div class="product-details">
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-tag"></i> Nome:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($item['name']); ?></div>
                            </div>

                            <?php if (!empty($item['sku'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-barcode"></i> SKU / Código:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($item['sku']); ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($item['category'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-folder"></i> Categoria:</div>
                                <div class="detail-value"><?php echo htmlspecialchars($item['category']); ?></div>
                            </div>
                            <?php endif; ?>

                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-tag"></i> Preço de Venda:</div>
                                <div class="detail-value price-highlight">R$ <?php echo number_format($item['price'], 2, ',', '.'); ?></div>
                            </div>

                            <?php if (!empty($item['cost']) || $item['cost'] > 0): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-dollar-sign"></i> Custo:</div>
                                <div class="detail-value">R$ <?php echo number_format($item['cost'], 2, ',', '.'); ?></div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-chart-line"></i> Margem:</div>
                                <div class="detail-value">
                                    <?php
                                    if ($item['cost'] > 0) {
                                        $margin = (($item['price'] - $item['cost']) / $item['price']) * 100;
                                        echo number_format($margin, 2) . '%';
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-cubes"></i> Quantidade em Estoque:</div>
                                <div class="detail-value">
                                    <?php echo $item['quantity']; ?>
                                    <?php
                                    if ($item['quantity'] <= 0) {
                                        echo ' <span class="stock-status out-of-stock">Sem Estoque</span>';
                                    } elseif ($item['quantity'] < 5) {
                                        echo ' <span class="stock-status low-stock">Estoque Baixo</span>';
                                    } else {
                                        echo ' <span class="stock-status in-stock">Em Estoque</span>';
                                    }
                                    ?>
                                </div>
                            </div>

                            <?php if (!empty($item['description'])): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-align-left"></i> Descrição:</div>
                                <div class="detail-value"><?php echo nl2br(htmlspecialchars($item['description'])); ?></div>
                            </div>
                            <?php endif; ?>

                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-calendar-alt"></i> Data de Cadastro:</div>
                                <div class="detail-value"><?php echo date('d/m/Y H:i', strtotime($item['created_at'])); ?></div>
                            </div>

                            <?php if ($item['updated_at'] != $item['created_at']): ?>
                            <div class="detail-row">
                                <div class="detail-label"><i class="fas fa-edit"></i> Última Atualização:</div>
                                <div class="detail-value"><?php echo date('d/m/Y H:i', strtotime($item['updated_at'])); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="action-buttons">
                            <a href="inventory.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Voltar para Estoque</a>
                            <a href="inventory_edit.php?id=<?php echo $id; ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Editar Produto</a>
                            <a href="inventory_delete.php?id=<?php echo $id; ?>" class="btn btn-danger delete-btn"><i class="fas fa-trash"></i> Excluir Produto</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
