<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Include permissions helper
require_once 'includes/permissions.php';

// Initialize variables
$role_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$role = null;
$permissions = [];
$role_permissions = [];
$message = '';
$message_type = '';

// Check if user is admin
$is_admin = user_has_role('administrator');

// If not admin, redirect to dashboard
if (!$is_admin) {
    header('Location: dashboard_simple.php');
    exit;
}

// Check if role exists
if ($role_id > 0) {
    $query = "SELECT id, name, description FROM user_roles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $role_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $role = $result->fetch_assoc();
    } else {
        // Role not found, redirect to roles list
        header('Location: admin_roles.php');
        exit;
    }
} else {
    // No role ID provided, redirect to roles list
    header('Location: admin_roles.php');
    exit;
}

// Get all permissions
$query = "SELECT id, name, description FROM permissions ORDER BY name";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permissions[] = $row;
    }
}

// Get role permissions
$query = "SELECT permission_id FROM role_permissions WHERE role_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param('i', $role_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result) {
    while ($row = $result->fetch_assoc()) {
        $role_permissions[] = $row['permission_id'];
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get selected permissions
    $selected_permissions = $_POST['permissions'] ?? [];

    // Start transaction
    $conn->begin_transaction();

    try {
        // Delete existing role permissions
        $query = "DELETE FROM role_permissions WHERE role_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('i', $role_id);
        $stmt->execute();

        // Insert new role permissions
        if (!empty($selected_permissions)) {
            $query = "INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)";
            $stmt = $conn->prepare($query);

            foreach ($selected_permissions as $permission_id) {
                $stmt->bind_param('ii', $role_id, $permission_id);
                $stmt->execute();
            }
        }

        // Commit transaction
        $conn->commit();

        $message = "Role permissions updated successfully.";
        $message_type = 'success';

        // Update role_permissions array for display
        $role_permissions = $selected_permissions;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();

        $message = "Error updating role permissions: " . $e->getMessage();
        $message_type = 'error';
    }
}

// Group permissions by category (based on name prefix)
$grouped_permissions = [];
foreach ($permissions as $permission) {
    $parts = explode('_', $permission['name']);
    $category = $parts[0] ?? 'other';

    if (!isset($grouped_permissions[$category])) {
        $grouped_permissions[$category] = [];
    }

    $grouped_permissions[$category][] = $permission;
}

// Sort categories alphabetically
ksort($grouped_permissions);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Role Permissions</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        .permissions-list {
            margin-bottom: 20px;
        }

        .permission-category {
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
            text-transform: capitalize;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }

        .permission-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .permission-item:hover {
            background-color: #f8f9fa;
        }

        .permission-checkbox {
            margin-right: 10px;
        }

        .permission-label {
            font-weight: normal;
            cursor: pointer;
            flex: 1;
        }

        .permission-description {
            color: #6c757d;
            font-size: 14px;
            margin-top: 2px;
        }

        .role-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .role-name {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .role-description {
            color: #6c757d;
        }

        .select-all-container {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
        }

        .select-all-label {
            font-weight: bold;
            margin-left: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">Edit Role Permissions</h1>
                    <a href="admin_roles.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Roles
                    </a>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="message <?php echo $message_type === 'success' ? 'message-success' : 'message-error'; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="role-info">
                    <div class="role-name"><?php echo htmlspecialchars($role['name']); ?></div>
                    <div class="role-description"><?php echo htmlspecialchars($role['description']); ?></div>
                </div>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Manage Permissions</h2>
                    </div>
                    <div class="admin-card-body">
                        <form method="post" action="">
                            <div class="select-all-container">
                                <input type="checkbox" id="select-all" class="permission-checkbox">
                                <label for="select-all" class="select-all-label">Select All Permissions</label>
                            </div>

                            <div class="permissions-list">
                                <?php foreach ($grouped_permissions as $category => $category_permissions): ?>
                                    <div class="permission-category">
                                        <h3 class="category-title"><?php echo htmlspecialchars($category); ?></h3>

                                        <?php foreach ($category_permissions as $permission): ?>
                                            <div class="permission-item">
                                                <input type="checkbox"
                                                       id="permission-<?php echo $permission['id']; ?>"
                                                       name="permissions[]"
                                                       value="<?php echo $permission['id']; ?>"
                                                       class="permission-checkbox"
                                                       <?php echo in_array($permission['id'], $role_permissions) ? 'checked' : ''; ?>>
                                                <label for="permission-<?php echo $permission['id']; ?>" class="permission-label">
                                                    <?php echo htmlspecialchars($permission['name']); ?>
                                                    <div class="permission-description"><?php echo htmlspecialchars($permission['description']); ?></div>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <div class="form-actions">
                                <a href="admin_roles.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Permissions</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Select all functionality
            const selectAllCheckbox = document.getElementById('select-all');
            const permissionCheckboxes = document.querySelectorAll('input[name="permissions[]"]');

            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                permissionCheckboxes.forEach(function(checkbox) {
                    checkbox.checked = isChecked;
                });
            });

            // Update select all checkbox state based on individual checkboxes
            function updateSelectAllCheckbox() {
                const checkedCount = document.querySelectorAll('input[name="permissions[]"]:checked').length;
                const totalCount = permissionCheckboxes.length;

                selectAllCheckbox.checked = checkedCount === totalCount;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < totalCount;
            }

            permissionCheckboxes.forEach(function(checkbox) {
                checkbox.addEventListener('change', updateSelectAllCheckbox);
            });

            // Initial state
            updateSelectAllCheckbox();
        });
    </script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
