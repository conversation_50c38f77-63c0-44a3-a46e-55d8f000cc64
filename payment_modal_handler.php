<?php
// Incluir arquivo de correção de sessão
require_once 'session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'error' => 'Usuário não autenticado']);
    exit;
}

// Get the action
$action = isset($_POST['action']) ? $_POST['action'] : '';

if ($action === 'get_payments') {
    // Get payment history for an invoice
    $invoice_id = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;
    
    if ($invoice_id <= 0) {
        echo json_encode(['success' => false, 'error' => 'ID da fatura inválido']);
        exit;
    }
    
    try {
        // Get invoice details
        $invoice_query = "SELECT i.*, c.name as customer_name 
                         FROM invoices i 
                         JOIN customers c ON i.customer_id = c.id 
                         WHERE i.id = $invoice_id";
        $invoice_result = db_query($invoice_query);
        
        if (!$invoice_result || db_num_rows($invoice_result) === 0) {
            echo json_encode(['success' => false, 'error' => 'Fatura não encontrada']);
            exit;
        }
        
        $invoice = db_fetch_assoc($invoice_result);
        
        // Check if invoice_payments table exists
        $check_table = db_query("SHOW TABLES LIKE 'invoice_payments'");
        if (db_num_rows($check_table) == 0) {
            // Create table if it doesn't exist
            $create_table = "CREATE TABLE IF NOT EXISTS invoice_payments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                invoice_id INT NOT NULL,
                payment_date DATE NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                payment_method VARCHAR(50) NOT NULL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
            )";
            if (!db_query($create_table)) {
                echo json_encode(['success' => false, 'error' => 'Erro ao criar tabela de pagamentos']);
                exit;
            }
        }
        
        // Get payment history
        $payments_query = "SELECT * FROM invoice_payments WHERE invoice_id = $invoice_id ORDER BY payment_date DESC";
        $payments_result = db_query($payments_query);
        
        $payments = [];
        $total_paid = 0;
        
        if ($payments_result) {
            while ($payment = db_fetch_assoc($payments_result)) {
                $payments[] = $payment;
                $total_paid += $payment['amount'];
            }
        }
        
        $remaining = $invoice['total'] - $total_paid;
        
        echo json_encode([
            'success' => true,
            'invoice' => $invoice,
            'payments' => $payments,
            'total_paid' => $total_paid,
            'remaining' => $remaining
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Erro ao buscar dados: ' . $e->getMessage()]);
    }
    
} elseif ($action === 'add_payment') {
    // Add a new payment
    $invoice_id = isset($_POST['invoice_id']) ? intval($_POST['invoice_id']) : 0;
    $payment_date = isset($_POST['payment_date']) ? trim($_POST['payment_date']) : '';
    $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;
    $payment_method = isset($_POST['payment_method']) ? trim($_POST['payment_method']) : '';
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    
    // Validation
    if ($invoice_id <= 0) {
        echo json_encode(['success' => false, 'error' => 'ID da fatura inválido']);
        exit;
    }
    
    if (empty($payment_date)) {
        echo json_encode(['success' => false, 'error' => 'Data do pagamento é obrigatória']);
        exit;
    }
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'error' => 'Valor deve ser maior que zero']);
        exit;
    }
    
    if (empty($payment_method)) {
        echo json_encode(['success' => false, 'error' => 'Método de pagamento é obrigatório']);
        exit;
    }
    
    try {
        // Get invoice details
        $invoice_query = "SELECT * FROM invoices WHERE id = $invoice_id";
        $invoice_result = db_query($invoice_query);
        
        if (!$invoice_result || db_num_rows($invoice_result) === 0) {
            echo json_encode(['success' => false, 'error' => 'Fatura não encontrada']);
            exit;
        }
        
        $invoice = db_fetch_assoc($invoice_result);
        
        // Get current total paid
        $payments_query = "SELECT SUM(amount) as total_paid FROM invoice_payments WHERE invoice_id = $invoice_id";
        $payments_result = db_query($payments_query);
        $total_paid = 0;
        
        if ($payments_result) {
            $payment_data = db_fetch_assoc($payments_result);
            $total_paid = $payment_data['total_paid'] ? $payment_data['total_paid'] : 0;
        }
        
        $remaining = $invoice['total'] - $total_paid;
        
        if ($amount > $remaining) {
            echo json_encode(['success' => false, 'error' => 'Valor não pode ser maior que o saldo restante']);
            exit;
        }
        
        // Start transaction
        db_begin_transaction();
        
        // Insert payment
        $payment_date = db_escape($payment_date);
        $payment_method = db_escape($payment_method);
        $notes = db_escape($notes);
        
        $insert_query = "INSERT INTO invoice_payments (invoice_id, payment_date, amount, payment_method, notes) 
                        VALUES ($invoice_id, '$payment_date', $amount, '$payment_method', '$notes')";
        
        if (!db_query($insert_query)) {
            db_rollback();
            echo json_encode(['success' => false, 'error' => 'Erro ao registrar pagamento']);
            exit;
        }
        
        // Update invoice status
        $new_total_paid = $total_paid + $amount;
        $new_remaining = $invoice['total'] - $new_total_paid;
        
        if (abs($new_remaining) < 0.01) {
            $status = 'paid';
        } elseif (abs($new_remaining - $invoice['total']) < 0.01) {
            $status = 'unpaid';
        } else {
            $status = 'partial';
        }
        
        // Add columns if they don't exist
        $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'paid_amount'");
        if (db_num_rows($check_column) == 0) {
            db_query("ALTER TABLE invoices ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0");
        }
        
        $check_column = db_query("SHOW COLUMNS FROM invoices LIKE 'remaining_amount'");
        if (db_num_rows($check_column) == 0) {
            db_query("ALTER TABLE invoices ADD COLUMN remaining_amount DECIMAL(10,2) DEFAULT 0");
        }
        
        // Update invoice
        $update_query = "UPDATE invoices SET 
                        status = '$status',
                        paid_amount = $new_total_paid,
                        remaining_amount = $new_remaining
                        WHERE id = $invoice_id";
        
        if (!db_query($update_query)) {
            db_rollback();
            echo json_encode(['success' => false, 'error' => 'Erro ao atualizar status da fatura']);
            exit;
        }
        
        // Commit transaction
        db_commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Pagamento registrado com sucesso!',
            'new_status' => $status,
            'total_paid' => $new_total_paid,
            'remaining' => $new_remaining
        ]);
        
    } catch (Exception $e) {
        db_rollback();
        echo json_encode(['success' => false, 'error' => 'Erro ao processar pagamento: ' . $e->getMessage()]);
    }
    
} else {
    echo json_encode(['success' => false, 'error' => 'Ação inválida']);
}

// Close database connection
db_close();
?>
