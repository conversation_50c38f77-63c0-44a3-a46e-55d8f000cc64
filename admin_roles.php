<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Include session check
require_once 'check_session.php';

// Include permissions helper
require_once 'includes/permissions.php';

// Initialize variables
$roles = [];
$message = '';
$message_type = '';

// Check if user is admin
$is_admin = user_has_role('administrator');

// If not admin, redirect to dashboard
if (!$is_admin) {
    header('Location: dashboard_simple.php');
    exit;
}

// Handle role deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $role_id = (int)$_GET['delete'];

    // Don't allow deleting the administrator, manager, or user roles
    $query = "SELECT name FROM user_roles WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $role_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $role = $result->fetch_assoc();
        if (in_array($role['name'], ['administrator', 'manager', 'user'])) {
            $message = "Default roles cannot be deleted.";
            $message_type = 'error';
        } else {
            // Check if any users have this role
            $query = "SELECT COUNT(*) as user_count FROM users WHERE role_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param('i', $role_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result && $row = $result->fetch_assoc()) {
                $user_count = (int)$row['user_count'];

                if ($user_count > 0) {
                    $message = "Cannot delete role because it is assigned to {$user_count} user(s).";
                    $message_type = 'error';
                } else {
                    // Delete role permissions first
                    $query = "DELETE FROM role_permissions WHERE role_id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param('i', $role_id);
                    $stmt->execute();

                    // Delete the role
                    $query = "DELETE FROM user_roles WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param('i', $role_id);

                    if ($stmt->execute() && $stmt->affected_rows > 0) {
                        $message = "Role deleted successfully.";
                        $message_type = 'success';
                    } else {
                        $message = "Error deleting role: " . $conn->error;
                        $message_type = 'error';
                    }
                }
            }
        }
    }
}

// Process form submission for adding a new role
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_role'])) {
    $role_name = trim($_POST['role_name'] ?? '');
    $role_description = trim($_POST['role_description'] ?? '');

    // Validate input
    $errors = [];

    if (empty($role_name)) {
        $errors[] = "Role name is required.";
    } else {
        // Check if role name already exists
        $query = "SELECT 1 FROM user_roles WHERE name = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('s', $role_name);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $errors[] = "Role name already exists.";
        }
    }

    // If no errors, create the role
    if (empty($errors)) {
        $query = "INSERT INTO user_roles (name, description) VALUES (?, ?)";
        $stmt = $conn->prepare($query);
        $stmt->bind_param('ss', $role_name, $role_description);

        if ($stmt->execute()) {
            $message = "Role created successfully.";
            $message_type = 'success';
        } else {
            $message = "Error creating role: " . $conn->error;
            $message_type = 'error';
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'error';
    }
}

// Get all roles
$query = "SELECT ur.id, ur.name, ur.description, COUNT(u.id) as user_count
          FROM user_roles ur
          LEFT JOIN users u ON ur.id = u.role_id
          GROUP BY ur.id
          ORDER BY ur.name";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $roles[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Management</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css"><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .admin-actions {
            display: flex;
            gap: 10px;
        }

        .admin-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .admin-card-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-card-title {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            color: #333;
        }

        .admin-card-body {
            padding: 20px;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table th, .admin-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .admin-table tr:hover {
            background-color: #f8f9fa;
        }

        .role-actions {
            display: flex;
            gap: 10px;
        }

        .action-edit, .action-delete, .action-permissions {
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .action-edit {
            background-color: #e9ecef;
            color: #495057;
        }

        .action-delete {
            background-color: #f8d7da;
            color: #721c24;
        }

        .action-permissions {
            background-color: #d1ecf1;
            color: #0c5460;
        }

        .action-edit:hover {
            background-color: #dee2e6;
        }

        .action-delete:hover {
            background-color: #f5c6cb;
        }

        .action-permissions:hover {
            background-color: #c1e7ec;
        }

        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .message-success {
            background-color: #d4edda;
            color: #155724;
        }

        .message-error {
            background-color: #f8d7da;
            color: #721c24;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 16px;
        }

        .form-control:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 15px;
        }

        .required-field::after {
            content: " *";
            color: #dc3545;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .user-count-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            background-color: #e9ecef;
            color: #495057;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>
    <?php include 'includes/admin_sidebar.php'; ?>
    <div class="app-container admin-content">
        <!-- Main content -->

        <!-- Content -->
        <div class="content flex-grow-1">
            <div class="admin-container">
                <div class="admin-header">
                    <h1 class="admin-title">Role Management</h1>
                    <div class="admin-actions">
                        <a href="admin_users.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Users
                        </a>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="message <?php echo $message_type === 'success' ? 'message-success' : 'message-error'; ?>">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Add New Role</h2>
                    </div>
                    <div class="admin-card-body">
                        <form method="post" action="">
                            <div class="form-group">
                                <label for="role_name" class="form-label required-field">Role Name</label>
                                <input type="text" id="role_name" name="role_name" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="role_description" class="form-label">Description</label>
                                <input type="text" id="role_description" name="role_description" class="form-control">
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="add_role" class="btn btn-primary">Add Role</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="admin-card">
                    <div class="admin-card-header">
                        <h2 class="admin-card-title">Manage Roles</h2>
                    </div>
                    <div class="admin-card-body">
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Users</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($roles as $role): ?>
                                        <tr>
                                            <td><?php echo $role['id']; ?></td>
                                            <td><?php echo htmlspecialchars($role['name']); ?></td>
                                            <td><?php echo htmlspecialchars($role['description']); ?></td>
                                            <td>
                                                <span class="user-count-badge">
                                                    <?php echo $role['user_count']; ?> user<?php echo $role['user_count'] !== 1 ? 's' : ''; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="role-actions">
                                                    <a href="admin_role_edit.php?id=<?php echo $role['id']; ?>" class="action-permissions">
                                                        <i class="fas fa-key"></i> Permissions
                                                    </a>
                                                    <?php if (!in_array($role['name'], ['administrator', 'manager', 'user'])): ?>
                                                        <a href="admin_roles.php?delete=<?php echo $role['id']; ?>" class="action-delete" onclick="return confirm('Are you sure you want to delete this role?');">
                                                            <i class="fas fa-trash-alt"></i> Delete
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
if (function_exists('db_close')) {
    db_close();
} else {
    $conn->close();
}
?>
