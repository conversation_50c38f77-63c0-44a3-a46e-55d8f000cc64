<?php
// Start session
session_start();

// Include database connection
require_once 'db_connect.php';

// Check if this is a shared link view
$is_shared = isset($_GET['shared']) && $_GET['shared'] == 1;

// Initialize variables
$id = 0;
$error = '';
$quote = null;
$items = [];
$customer = null;

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get quote data
    $result = db_query("SELECT * FROM quotes WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);

        // Get customer data
        $customer_id = $quote['customer_id'];
        $result = db_query("SELECT * FROM customers WHERE id = $customer_id");

        if ($result && db_num_rows($result) > 0) {
            $customer = db_fetch_assoc($result);
        }

        // Get quote items
        $result = db_query("
            SELECT qi.*, i.name as item_name, i.sku as item_sku
            FROM quote_items qi
            JOIN inventory i ON qi.inventory_id = i.id
            WHERE qi.quote_id = $id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        }
    } else {
        $error = 'Quote not found.';
    }
} else {
    $error = 'Quote ID not provided.';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote <?php echo $quote ? htmlspecialchars($quote['quote_number']) : ''; ?> - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <link rel="stylesheet" href="css/mobile-menu-fix.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f9f9f9;
        }

        .print-container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 25px;
            border-radius: 5px;
        }

        .print-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
        }

        .company-info {
            flex: 1;
        }

        .company-info h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 28px;
        }

        .company-info p {
            margin: 3px 0;
            color: #555;
            font-size: 0.9em;
        }

        .quote-info {
            text-align: right;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }

        .quote-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #3498db;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .quote-number {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .customer-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .customer-details {
            display: flex;
            flex-wrap: wrap;
        }

        .customer-details div {
            flex: 1;
            min-width: 250px;
        }

        .customer-details p {
            margin: 4px 0;
            font-size: 0.9em;
        }

        .customer-details strong {
            color: #2c3e50;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.9em;
        }

        .items-table th {
            background-color: #3498db;
            color: white;
            padding: 8px 10px;
            text-align: left;
            font-weight: 600;
        }

        .items-table td {
            padding: 6px 10px;
            border-bottom: 1px solid #eee;
        }

        .items-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .items-table tr:hover {
            background-color: #f5f5f5;
        }

        .totals {
            width: 300px;
            margin-left: auto;
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 0.9em;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            border-bottom: 1px solid #eee;
        }

        .grand-total {
            font-weight: bold;
            font-size: 16px;
            border-top: 2px solid #3498db;
            padding-top: 8px;
            margin-top: 8px;
            color: #2c3e50;
        }

        .notes {
            margin-top: 20px;
            border-top: 1px solid #eee;
            padding-top: 10px;
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 5px;
            font-size: 0.9em;
        }

        @media print {
            body {
                padding: 0;
                background-color: #fff;
            }

            .print-container {
                box-shadow: none;
                padding: 0;
                max-width: 100%;
            }

            .no-print, header, .navbar {
                display: none;
            }

            .items-table {
                box-shadow: none;
            }

            .totals {
                box-shadow: none;
            }
        }

        /* Responsive styles for mobile devices */
        @media screen and (max-width: 768px) {
            body {
                padding: 10px;
            }

            .print-container {
                padding: 15px;
                margin-top: 60px;
            }

            .print-header {
                flex-direction: column;
            }

            .quote-info {
                text-align: left;
                margin-top: 15px;
                padding: 10px;
            }

            .customer-details {
                flex-direction: column;
            }

            .customer-details div {
                min-width: 100%;
                margin-bottom: 10px;
            }

            .items-table {
                font-size: 0.8em;
            }

            .items-table th, .items-table td {
                padding: 5px;
            }

            /* Make table responsive */
            .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 15px;
            }

            /* Adjust totals section */
            .totals {
                width: 100%;
                margin-left: 0;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <?php if (!$is_shared): ?>
        <?php include 'includes/header_responsive.php'; ?>
    <?php endif; ?>

    <?php if (!empty($error)): ?>
        <div class="error"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if ($quote && $customer): ?>
    <div class="print-container">
        <div class="print-header">
            <div class="company-info">
                <h1>Tony's AC Repair LLC</h1>
                <p>Orlando, Florida</p>
                <p>Phone: ************</p>
                <p>Email: <EMAIL></p>
                <p>Website: TONYSACREPAIR.COM</p>
            </div>

            <div class="quote-info">
                <div class="quote-title">QUOTE</div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <div class="quote-number"><?php
                        $quote_number = $quote['quote_number'];
                        // Primeiro, procurar por qualquer padrão #YYMMDD-XXX no texto
                        if (preg_match('/#\d{6}-\d{3}/', $quote_number, $matches)) {
                            echo htmlspecialchars($matches[0]);
                        } else if (strpos($quote_number, "\n") !== false) {
                            $parts = explode("\n", $quote_number);
                            $last_part = trim(end($parts));
                            echo htmlspecialchars($last_part);
                        } else {
                            echo htmlspecialchars($quote_number);
                        }
                    ?></div>
                    <div style="font-weight: bold; color:
                    <?php
                    switch ($quote['status']) {
                        case 'pending':
                            echo '#3498db'; // blue
                            $status = 'Pending';
                            break;
                        case 'approved':
                            echo '#27ae60'; // green
                            $status = 'Approved';
                            break;
                        case 'rejected':
                            echo '#e74c3c'; // red
                            $status = 'Rejected';
                            break;
                        case 'expired':
                            echo '#95a5a6'; // gray
                            $status = 'Expired';
                            break;
                        default:
                            echo '#3498db'; // blue
                            $status = $quote['status'];
                    }
                    ?>">
                        Status: <?php echo $status; ?>
                    </div>
                </div>
                <div style="display: flex; justify-content: space-between;">
                    <p>Date: <?php echo date('m/d/Y', strtotime($quote['quote_date'])); ?></p>
                    <p>Valid Until: <?php echo !empty($quote['valid_until']) ? date('m/d/Y', strtotime($quote['valid_until'])) : 'Not specified'; ?></p>
                </div>
            </div>
        </div>

        <div class="customer-info">
            <div class="section-title" style="text-align: center;">CUSTOMER INFORMATION</div>
            <div class="customer-details">
                <div>
                    <p><strong>Name:</strong> <?php echo htmlspecialchars($customer['name']); ?></p>
                    <p><strong>Email:</strong> <?php echo htmlspecialchars($customer['email']); ?></p>
                    <p><strong>Phone:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                </div>
                <div>
                    <p><strong>Address:</strong> <?php echo htmlspecialchars($customer['address']); ?></p>
                    <p><strong>City:</strong> <?php echo htmlspecialchars($customer['city']); ?></p>
                    <p><strong>State:</strong> <?php echo htmlspecialchars($customer['state']); ?></p>
                    <p><strong>ZIP Code:</strong> <?php echo htmlspecialchars($customer['zip']); ?></p>
                </div>
            </div>
        </div>

        <div class="section-title">Quote Items</div>

        <?php if (empty($items)): ?>
            <p>No items found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="items-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>SKU</th>
                        <th>Description</th>
                        <th>Quantity</th>
                        <th>Unit Price</th>
                        <th>Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($item['item_name']); ?></td>
                            <td><?php echo htmlspecialchars($item['item_sku']); ?></td>
                            <td><?php echo htmlspecialchars($item['description']); ?></td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td>$ <?php echo number_format($item['price'], 2, '.', ','); ?></td>
                            <td>$ <?php echo number_format($item['subtotal'], 2, '.', ','); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                </table>
            </div>
        <?php endif; ?>

        <div class="totals">
            <div class="totals-row">
                <span>Subtotal:</span>
                <span>$ <?php echo number_format($quote['subtotal'], 2, '.', ','); ?></span>
            </div>

            <?php if ($quote['tax_rate'] > 0): ?>
                <div class="totals-row">
                    <span>Tax (<?php echo number_format($quote['tax_rate'], 2, '.', ','); ?>%):</span>
                    <span>$ <?php echo number_format($quote['tax_amount'], 2, '.', ','); ?></span>
                </div>
            <?php endif; ?>

            <?php if ($quote['discount_amount'] > 0): ?>
                <div class="totals-row">
                    <span>Discount:</span>
                    <span>$ <?php echo number_format($quote['discount_amount'], 2, '.', ','); ?></span>
                </div>
            <?php endif; ?>

            <div class="totals-row grand-total">
                <span>Total:</span>
                <span>$ <?php echo number_format($quote['total'], 2, '.', ','); ?></span>
            </div>
        </div>

        <?php if (!empty($quote['notes'])): ?>
            <div class="notes">
                <div class="section-title">Notes</div>
                <p><?php echo nl2br(htmlspecialchars($quote['notes'])); ?></p>
            </div>
        <?php endif; ?>

        <div class="no-print" style="margin-top: 15px; text-align: center;">
            <button onclick="window.print()" style="padding: 6px 12px; background-color: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 8px; font-size: 0.9em;">Print</button>
            <?php if (!$is_shared): ?>
            <button onclick="window.location.href='quotes.php'" style="padding: 6px 12px; background-color: #2ecc71; color: white; border: none; border-radius: 3px; cursor: pointer; margin-right: 8px; font-size: 0.9em;">Back to Quotes</button>
            <?php endif; ?>
            <button onclick="window.close()" style="padding: 6px 12px; background-color: #95a5a6; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.9em;">Close</button>
        </div>
    </div> <!-- End of print-container -->
    <?php endif; ?>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically open the print dialog
            // window.print();
        };
    </script>
    <script src="js/mobile-menu-fix-new.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
