<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Create logs directory if it doesn't exist
$log_dir = __DIR__ . '/logs';
if (!file_exists($log_dir)) {
    mkdir($log_dir, 0755, true);
}
ini_set('error_log', $log_dir . '/php_errors.log');

// Configurar sessão diretamente
$session_dir = __DIR__ . '/sessions';
if (!file_exists($session_dir)) {
    @mkdir($session_dir, 0755, true);
}

// Definir o caminho da sessão
ini_set('session.save_path', $session_dir);

// Iniciar a sessão
@session_start();

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = $success = '';
$quote = null;
$items = [];

// Get quote data
if ($id > 0) {
    $result = db_query("
        SELECT q.*, c.name as customer_name
        FROM quotes q
        JOIN customers c ON q.customer_id = c.id
        WHERE q.id = $id
    ");

    if ($result && db_num_rows($result) > 0) {
        $quote = db_fetch_assoc($result);

        // Get quote items - using LEFT JOIN to include items without inventory
        $result = db_query("
            SELECT qi.*, IFNULL(i.name, '') as item_name, IFNULL(i.sku, '') as item_sku
            FROM quote_items qi
            LEFT JOIN inventory i ON qi.inventory_id = i.id
            WHERE qi.quote_id = $id
        ");

        if ($result && db_num_rows($result) > 0) {
            $items = db_fetch_all($result);
        } else {
            // If no items found with the JOIN, try a simpler query
            $result = db_query("SELECT * FROM quote_items WHERE quote_id = $id");
            if ($result && db_num_rows($result) > 0) {
                $items = db_fetch_all($result);
            }
        }
    }
}

// Get all customers for dropdown
$customers = [];
$result = db_query("SELECT id, name FROM customers ORDER BY name");
if ($result && db_num_rows($result) > 0) {
    $customers = db_fetch_all($result);
}

// Get all inventory items for dropdown
$inventory = [];
$result = db_query("SELECT id, name, sku, price FROM inventory ORDER BY name");
if ($result && db_num_rows($result) > 0) {
    $inventory = db_fetch_all($result);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $customer_id = intval($_POST['customer_id']);
    $quote_date = $_POST['quote_date'];
    $valid_until = !empty($_POST['valid_until']) ? $_POST['valid_until'] : null;
    $notes = $_POST['notes'];
    $subtotal = floatval($_POST['subtotal']);
    $tax_rate = floatval($_POST['tax_rate']);
    $tax_amount = floatval($_POST['tax_amount']);
    $discount_amount = floatval($_POST['discount_amount']);
    $total = floatval($_POST['total']);
    $status = $_POST['status'];

    // Get items
    $item_ids = isset($_POST['item_id']) ? $_POST['item_id'] : [];
    $inventory_ids = isset($_POST['inventory_id']) ? $_POST['inventory_id'] : [];
    $item_descriptions = isset($_POST['item_description']) ? $_POST['item_description'] : [];
    $item_quantities = isset($_POST['item_quantity']) ? $_POST['item_quantity'] : [];
    $item_prices = isset($_POST['item_price']) ? $_POST['item_price'] : [];
    $item_subtotals = isset($_POST['item_subtotal']) ? $_POST['item_subtotal'] : [];

    // Validate data
    if ($customer_id <= 0) {
        $error = 'Por favor, selecione um cliente.';
    } elseif (empty($quote_date)) {
        $error = 'A data do orçamento é obrigatória.';
    } elseif (empty($item_descriptions)) {
        $error = 'Adicione pelo menos um item ao orçamento.';
    } else {
        // Start transaction
        db_begin_transaction();
        $transaction_success = true;

        // Update quote
        $query = "UPDATE quotes SET
            customer_id = $customer_id,
            quote_date = '" . db_escape($quote_date) . "',
            valid_until = " . ($valid_until ? "'" . db_escape($valid_until) . "'" : "NULL") . ",
            notes = '" . db_escape($notes) . "',
            subtotal = $subtotal,
            tax_rate = $tax_rate,
            tax_amount = $tax_amount,
            discount_amount = $discount_amount,
            total = $total,
            status = '" . db_escape($status) . "'
            WHERE id = $id";

        if (!db_query($query)) {
            $transaction_success = false;
            $error = 'Erro ao atualizar orçamento: ' . db_error();
        }

        // Delete existing items
        if ($transaction_success) {
            if (!db_query("DELETE FROM quote_items WHERE quote_id = $id")) {
                $transaction_success = false;
                $error = 'Erro ao excluir itens existentes: ' . db_error();
            }
        }

        // Insert new items
        if ($transaction_success) {
            foreach ($item_descriptions as $index => $description) {
                $inventory_id = isset($inventory_ids[$index]) && !empty($inventory_ids[$index]) ? intval($inventory_ids[$index]) : 'NULL';
                $quantity = floatval($item_quantities[$index]);
                $price = floatval($item_prices[$index]);
                $subtotal = floatval($item_subtotals[$index]);

                $query = "INSERT INTO quote_items (quote_id, inventory_id, description, quantity, price, subtotal) VALUES (
                    $id,
                    $inventory_id,
                    '" . db_escape($description) . "',
                    $quantity,
                    $price,
                    $subtotal
                )";

                if (!db_query($query)) {
                    $transaction_success = false;
                    $error = 'Erro ao adicionar item: ' . db_error();
                    break;
                }
            }
        }

        // Commit or rollback transaction
        if ($transaction_success) {
            db_commit();
            $success = 'Orçamento atualizado com sucesso!';

            // Refresh quote data
            $result = db_query("
                SELECT q.*, c.name as customer_name
                FROM quotes q
                JOIN customers c ON q.customer_id = c.id
                WHERE q.id = $id
            ");
            if ($result && db_num_rows($result) > 0) {
                $quote = db_fetch_assoc($result);
            }

            // Refresh items
            $result = db_query("
                SELECT qi.*, IFNULL(i.name, '') as item_name, IFNULL(i.sku, '') as item_sku
                FROM quote_items qi
                LEFT JOIN inventory i ON qi.inventory_id = i.id
                WHERE qi.quote_id = $id
            ");
            if ($result && db_num_rows($result) > 0) {
                $items = db_fetch_all($result);
            } else {
                $result = db_query("SELECT * FROM quote_items WHERE quote_id = $id");
                if ($result && db_num_rows($result) > 0) {
                    $items = db_fetch_all($result);
                }
            }
        } else {
            db_rollback();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Orçamento - Tony's AC Repair</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css">
    <style>
        /* Estilos adicionais para melhorar a formatação */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-body {
            padding: 20px;
        }

        .card-title {
            margin: 0;
            font-size: 1.5rem;
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input, select, textarea {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .btn {
            display: inline-block;
            padding: 8px 15px;
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 1rem;
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-success {
            background-color: #28a745;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        table th, table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        table th {
            background-color: #f8f9fa;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .quote-details {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .quote-details .form-group {
            flex: 1 1 calc(33.333% - 20px);
            min-width: 200px;
        }

        /* Estilos para campos somente leitura */
        input[readonly] {
            background-color: #f8f9fa;
            cursor: not-allowed;
        }

        /* Estilos para o cabeçalho */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-links li {
            margin-left: 20px;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
        }

        .nav-links a:hover {
            color: #4a6cf7;
        }

        /* Estilos para o rodapé */
        footer {
            text-align: center;
            padding: 20px 0;
            color: #6c757d;
            border-top: 1px solid #eee;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <?php include 'includes/header_special.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if ($quote): ?>
                <div class="card">
                    <div class="card-header">
                        <h1 class="card-title">Editar Orçamento #<?php echo htmlspecialchars($quote['quote_number']); ?></h1>
                    </div>
                    <div class="card-body">
                        <form method="post" id="quote-form">
                            <!-- Quote Header Details -->
                            <div class="quote-details">
                                <!-- Customer Selection -->
                                <div class="form-group">
                                    <label for="customer_id">Cliente:</label>
                                    <select name="customer_id" id="customer_id" required>
                                        <option value="">Selecione o Cliente</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?php echo $customer['id']; ?>" <?php echo $customer['id'] == $quote['customer_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Quote Date -->
                                <div class="form-group">
                                    <label for="quote_date">Data do Orçamento:</label>
                                    <input type="date" name="quote_date" id="quote_date" value="<?php echo $quote['quote_date']; ?>" required>
                                </div>

                                <!-- Valid Until Date -->
                                <div class="form-group">
                                    <label for="valid_until">Válido Até:</label>
                                    <input type="date" name="valid_until" id="valid_until" value="<?php echo $quote['valid_until']; ?>">
                                </div>

                                <!-- Status -->
                                <div class="form-group">
                                    <label for="status">Status:</label>
                                    <select name="status" id="status" required>
                                        <option value="pending" <?php echo $quote['status'] == 'pending' ? 'selected' : ''; ?>>Pendente</option>
                                        <option value="approved" <?php echo $quote['status'] == 'approved' ? 'selected' : ''; ?>>Aprovado</option>
                                        <option value="rejected" <?php echo $quote['status'] == 'rejected' ? 'selected' : ''; ?>>Rejeitado</option>
                                        <option value="expired" <?php echo $quote['status'] == 'expired' ? 'selected' : ''; ?>>Expirado</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Quote Items -->
                            <h3>Itens do Orçamento</h3>
                            <table id="quote-items">
                                <thead>
                                    <tr>
                                        <th>Produto</th>
                                        <th>Descrição</th>
                                        <th width="100">Quantidade</th>
                                        <th width="150">Preço</th>
                                        <th width="150">Subtotal</th>
                                        <th width="80">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($items)): ?>
                                        <?php foreach ($items as $index => $item): ?>
                                            <tr>
                                                <td>
                                                    <input type="hidden" name="item_id[]" value="<?php echo $item['id']; ?>">
                                                    <select name="inventory_id[]" class="inventory-select">
                                                        <option value="">Selecione um produto</option>
                                                        <?php foreach ($inventory as $inv): ?>
                                                            <option value="<?php echo $inv['id']; ?>"
                                                                    data-price="<?php echo $inv['price']; ?>"
                                                                    <?php echo $inv['id'] == $item['inventory_id'] ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($inv['name'] . ' (' . $inv['sku'] . ')'); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </td>
                                                <td>
                                                    <input type="text" name="item_description[]" value="<?php echo htmlspecialchars($item['description']); ?>" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="item_quantity[]" class="item-quantity" value="<?php echo $item['quantity']; ?>" min="0.001" step="0.001" required placeholder="Ex: 5.33">
                                                </td>
                                                <td>
                                                    <input type="number" name="item_price[]" class="item-price" value="<?php echo $item['price']; ?>" min="0" step="0.01" required>
                                                </td>
                                                <td>
                                                    <input type="number" name="item_subtotal[]" class="item-subtotal" value="<?php echo $item['subtotal']; ?>" readonly>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger remove-item">Remover</button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td>
                                                <select name="inventory_id[]" class="inventory-select">
                                                    <option value="">Selecione um produto</option>
                                                    <?php foreach ($inventory as $inv): ?>
                                                        <option value="<?php echo $inv['id']; ?>" data-price="<?php echo $inv['price']; ?>">
                                                            <?php echo htmlspecialchars($inv['name'] . ' (' . $inv['sku'] . ')'); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="text" name="item_description[]" required>
                                            </td>
                                            <td>
                                                <input type="number" name="item_quantity[]" class="item-quantity" value="1" min="0.001" step="0.001" required placeholder="Ex: 5.33">
                                            </td>
                                            <td>
                                                <input type="number" name="item_price[]" class="item-price" value="0" min="0" step="0.01" required>
                                            </td>
                                            <td>
                                                <input type="number" name="item_subtotal[]" class="item-subtotal" value="0" readonly>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger remove-item">Remover</button>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="6">
                                            <button type="button" id="add-item" class="btn btn-success">Adicionar Item</button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>

                            <!-- Quote Totals -->
                            <div class="quote-details">
                                <div class="form-group">
                                    <label for="subtotal">Subtotal:</label>
                                    <input type="number" name="subtotal" id="subtotal" value="<?php echo $quote['subtotal']; ?>" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="tax_rate">Taxa de Imposto (%):</label>
                                    <input type="number" name="tax_rate" id="tax_rate" value="<?php echo $quote['tax_rate']; ?>" min="0" step="0.01">
                                </div>

                                <div class="form-group">
                                    <label for="tax_amount">Valor do Imposto:</label>
                                    <input type="number" name="tax_amount" id="tax_amount" value="<?php echo $quote['tax_amount']; ?>" readonly>
                                </div>

                                <div class="form-group">
                                    <label for="discount_amount">Desconto:</label>
                                    <input type="number" name="discount_amount" id="discount_amount" value="<?php echo $quote['discount_amount']; ?>" min="0" step="0.01">
                                </div>

                                <div class="form-group">
                                    <label for="total">Total:</label>
                                    <input type="number" name="total" id="total" value="<?php echo $quote['total']; ?>" readonly>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="form-group">
                                <label for="notes">Observações:</label>
                                <textarea name="notes" id="notes" rows="4"><?php echo htmlspecialchars($quote['notes']); ?></textarea>
                            </div>

                            <div class="form-actions">
                                <div>
                                    <a href="quotes.php" class="btn btn-secondary">Voltar para Orçamentos</a>
                                </div>
                                <div>
                                    <a href="quote_print.php?id=<?php echo $id; ?>" target="_blank" class="btn btn-secondary">Imprimir</a>
                                    <button type="submit" class="btn">Atualizar Orçamento</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">Orçamento não encontrado.</div>
                <p><a href="quotes.php" class="btn btn-secondary">Voltar para Orçamentos</a></p>
            <?php endif; ?>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tony's AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script>
        // Script para manipular os cálculos e funcionalidades dinâmicas
        document.addEventListener('DOMContentLoaded', function() {
            // Função para calcular o subtotal de um item
            function calculateItemTotal(input) {
                const row = input.closest('tr');
                const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.item-price').value) || 0;
                const subtotal = quantity * price;
                row.querySelector('.item-subtotal').value = subtotal.toFixed(2);

                calculateTotals();
            }

            // Função para calcular os totais do orçamento
            function calculateTotals() {
                const subtotalInputs = document.querySelectorAll('.item-subtotal');
                let subtotal = 0;

                subtotalInputs.forEach(input => {
                    subtotal += parseFloat(input.value) || 0;
                });

                const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
                const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;

                const taxAmount = subtotal * (taxRate / 100);
                const total = subtotal + taxAmount - discountAmount;

                document.getElementById('subtotal').value = subtotal.toFixed(2);
                document.getElementById('tax_amount').value = taxAmount.toFixed(2);
                document.getElementById('total').value = total.toFixed(2);
            }

            // Função para adicionar uma nova linha de item
            function addItemRow() {
                const tbody = document.querySelector('#quote-items tbody');
                const inventoryOptions = Array.from(document.querySelectorAll('.inventory-select option'))
                    .map(option => {
                        return `<option value="${option.value}" data-price="${option.dataset.price}">${option.textContent}</option>`;
                    })
                    .join('');

                const newRow = document.createElement('tr');

                newRow.innerHTML = `
                    <td>
                        <select name="inventory_id[]" class="inventory-select">
                            ${inventoryOptions}
                        </select>
                    </td>
                    <td>
                        <input type="text" name="item_description[]" required>
                    </td>
                    <td>
                        <input type="number" name="item_quantity[]" class="item-quantity" value="1" min="0.01" step="0.01" required>
                    </td>
                    <td>
                        <input type="number" name="item_price[]" class="item-price" value="0" min="0" step="0.01" required>
                    </td>
                    <td>
                        <input type="number" name="item_subtotal[]" class="item-subtotal" value="0" readonly>
                    </td>
                    <td>
                        <button type="button" class="btn btn-danger remove-item">Remover</button>
                    </td>
                `;

                tbody.appendChild(newRow);

                // Adicionar event listeners à nova linha
                setupRowEventListeners(newRow);
                calculateTotals();
            }

            // Função para remover uma linha de item
            function removeItemRow(button) {
                const tbody = document.querySelector('#quote-items tbody');
                const row = button.closest('tr');

                // Não remover se for a única linha
                if (tbody.children.length > 1) {
                    row.remove();
                    calculateTotals();
                } else {
                    alert('Você não pode remover todos os itens. Pelo menos um item é necessário.');
                }
            }

            // Função para configurar event listeners em uma linha
            function setupRowEventListeners(row) {
                // Event listener para seleção de produto
                const inventorySelect = row.querySelector('.inventory-select');
                if (inventorySelect) {
                    inventorySelect.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        if (selectedOption && selectedOption.dataset.price) {
                            const priceInput = row.querySelector('.item-price');
                            priceInput.value = selectedOption.dataset.price;
                            calculateItemTotal(priceInput);
                        }
                    });
                }

                // Event listeners para quantidade e preço
                const quantityInput = row.querySelector('.item-quantity');
                const priceInput = row.querySelector('.item-price');
                const removeButton = row.querySelector('.remove-item');

                if (quantityInput) {
                    quantityInput.addEventListener('input', function() {
                        calculateItemTotal(this);
                    });
                }

                if (priceInput) {
                    priceInput.addEventListener('input', function() {
                        calculateItemTotal(this);
                    });
                }

                if (removeButton) {
                    removeButton.addEventListener('click', function() {
                        removeItemRow(this);
                    });
                }
            }

            // Configurar event listeners para todas as linhas existentes
            document.querySelectorAll('#quote-items tbody tr').forEach(row => {
                setupRowEventListeners(row);
            });

            // Event listener para o botão de adicionar item
            document.getElementById('add-item').addEventListener('click', addItemRow);

            // Event listeners para taxa de imposto e desconto
            document.getElementById('tax_rate').addEventListener('input', calculateTotals);
            document.getElementById('discount_amount').addEventListener('input', calculateTotals);

            // Calcular totais ao carregar a página
            calculateTotals();
        });
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
