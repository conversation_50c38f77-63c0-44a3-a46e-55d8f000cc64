<?php
// Configurar log de erros em vez de exibi-los (mais seguro para produção)
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'error_log');
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$error = '';
$service_call = null;

// Função para formatar a data
function format_date($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// Função para traduzir o status
function translate_status($status) {
    $translations = [
        'pending' => 'Pendente',
        'in_progress' => 'Em Andamento',
        'completed' => 'Concluído',
        'cancelled' => 'Cancelado'
    ];

    return isset($translations[$status]) ? $translations[$status] : $status;
}

// Get service call data
if ($id > 0) {
    $result = db_query("
        SELECT sc.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email, 
               c.address as customer_address, c.city as customer_city, c.state as customer_state, c.zip as customer_zip
        FROM service_calls sc
        JOIN customers c ON sc.customer_id = c.id
        WHERE sc.id = $id
    ");

    if ($result && db_num_rows($result) > 0) {
        $service_call = db_fetch_assoc($result);
    } else {
        $error = 'Chamado não encontrado.';
    }
} else {
    $error = 'ID inválido.';
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <link rel="stylesheet" href="css/hamburger-fix.css">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chamado #<?php echo $id; ?> - Impressão</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #333;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .title {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #ddd;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 8px;
        }
        
        .detail-label {
            font-weight: bold;
            width: 150px;
            flex-shrink: 0;
        }
        
        .detail-value {
            flex: 1;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
            text-align: center;
            min-width: 100px;
        }
        
        .status-pending {
            background-color: #ffeeba;
            color: #856404;
        }
        
        .status-in-progress {
            background-color: #b8daff;
            color: #004085;
        }
        
        .status-completed {
            background-color: #c3e6cb;
            color: #155724;
        }
        
        .status-cancelled {
            background-color: #f5c6cb;
            color: #721c24;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .no-print {
            margin-top: 30px;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                padding: 0;
                margin: 0;
            }
            
            .section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <?php if ($error): ?>
        <div class="error">
            <p><?php echo $error; ?></p>
            <p><a href="service_calls.php">Voltar para a lista de chamados</a></p>
        </div>
    <?php elseif ($service_call): ?>
        <div class="header">
            <div class="logo">Tony's AC Repair</div>
            <div class="title">Chamado de Atendimento #<?php echo $service_call['id']; ?></div>
            <div>Data de Impressão: <?php echo date('d/m/Y H:i'); ?></div>
        </div>
        
        <div class="section">
            <div class="section-title">Informações do Cliente</div>
            <div class="detail-row">
                <div class="detail-label">Nome:</div>
                <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_name']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Telefone:</div>
                <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_phone']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Email:</div>
                <div class="detail-value"><?php echo htmlspecialchars($service_call['customer_email']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Endereço:</div>
                <div class="detail-value">
                    <?php 
                    $address_parts = [];
                    if (!empty($service_call['customer_address'])) $address_parts[] = $service_call['customer_address'];
                    if (!empty($service_call['customer_city'])) $address_parts[] = $service_call['customer_city'];
                    if (!empty($service_call['customer_state'])) $address_parts[] = $service_call['customer_state'];
                    if (!empty($service_call['customer_zip'])) $address_parts[] = $service_call['customer_zip'];
                    echo htmlspecialchars(implode(', ', $address_parts));
                    ?>
                </div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">Informações do Chamado</div>
            <div class="detail-row">
                <div class="detail-label">Título:</div>
                <div class="detail-value"><?php echo htmlspecialchars($service_call['title']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Data Agendada:</div>
                <div class="detail-value"><?php echo format_date($service_call['scheduled_date']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Status:</div>
                <div class="detail-value">
                    <span class="status-badge status-<?php echo $service_call['status']; ?>">
                        <?php echo translate_status($service_call['status']); ?>
                    </span>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Criado em:</div>
                <div class="detail-value"><?php echo format_date($service_call['created_at']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Atualizado em:</div>
                <div class="detail-value"><?php echo format_date($service_call['updated_at']); ?></div>
            </div>
        </div>
        
        <div class="section">
            <div class="section-title">Descrição</div>
            <div class="detail-value"><?php echo nl2br(htmlspecialchars($service_call['description'])); ?></div>
        </div>
        
        <?php if (!empty($service_call['service_history'])): ?>
        <div class="section">
            <div class="section-title">Histórico de Atendimento</div>
            <div class="detail-value"><?php echo nl2br(htmlspecialchars($service_call['service_history'])); ?></div>
        </div>
        <?php endif; ?>
        
        <div class="footer">
            <p>Tony's AC Repair LLC - <?php echo date('Y'); ?></p>
            <p>Este documento foi gerado automaticamente pelo sistema.</p>
        </div>
        
        <div class="no-print" style="text-align: center;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Imprimir
            </button>
            <button onclick="window.close()" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
                Fechar
            </button>
        </div>
    <?php endif; ?>
    
    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically open the print dialog
            // window.print();
        };
    </script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
