<?php
// This file returns only the table content for AJAX requests
// Variables $invoices, $sort_field, $sort_order are already available from invoices_list.php
?>

<div class="invoices-table">
    <table class="table-mobile" id="invoicesTable">
        <thead>
            <tr>
                <th onclick="sortTable('invoice_number')" style="cursor: pointer;">
                    ID <i class="fas fa-sort<?php
                        if ($sort_field === 'invoice_number') {
                            echo $sort_order === 'ASC' ? '-up' : '-down';
                        }
                    ?> sort-icon <?php echo $sort_field === 'invoice_number' ? 'active' : ''; ?>"></i>
                </th>
                <th onclick="sortTable('invoice_date')" style="cursor: pointer;">
                    Date <i class="fas fa-sort<?php
                        if ($sort_field === 'invoice_date') {
                            echo $sort_order === 'ASC' ? '-up' : '-down';
                        }
                    ?> sort-icon <?php echo $sort_field === 'invoice_date' ? 'active' : ''; ?>"></i>
                </th>
                <th onclick="sortTable('customer_name')" style="cursor: pointer;">
                    Name <i class="fas fa-sort<?php
                        if ($sort_field === 'customer_name') {
                            echo $sort_order === 'ASC' ? '-up' : '-down';
                        }
                    ?> sort-icon <?php echo $sort_field === 'customer_name' ? 'active' : ''; ?>"></i>
                </th>
                <th onclick="sortTable('service_address')" style="cursor: pointer;">
                    Address <i class="fas fa-sort<?php
                        if ($sort_field === 'service_address') {
                            echo $sort_order === 'ASC' ? '-up' : '-down';
                        }
                    ?> sort-icon <?php echo $sort_field === 'service_address' ? 'active' : ''; ?>"></i>
                </th>
                <th onclick="sortTable('total')" style="cursor: pointer;">
                    Total <i class="fas fa-sort<?php
                        if ($sort_field === 'total') {
                            echo $sort_order === 'ASC' ? '-up' : '-down';
                        }
                    ?> sort-icon <?php echo $sort_field === 'total' ? 'active' : ''; ?>"></i>
                </th>
                <th onclick="filterByStatus()" style="cursor: pointer;" id="statusHeader" class="status-header">
                    <div class="status-header-content">
                        <div class="status-text">
                            <?php
                            $status_labels = ['All', 'Paid', 'Unpaid', 'Partial', 'Cancelled'];
                            $status_values = ['all', 'paid', 'unpaid', 'partial', 'cancelled'];
                            $current_index = array_search($filter_status, $status_values);
                            if ($current_index === false) $current_index = 0;
                            echo $status_labels[$current_index];
                            ?>
                        </div>
                        <i class="fas fa-filter filter-icon <?php echo $filter_status !== 'all' ? 'active' : ''; ?>"></i>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($invoices as $index => $invoice): ?>
                <tr onclick="selectRow(<?php echo $index; ?>)" data-invoice-id="<?php echo $invoice['id']; ?>" data-invoice-number="<?php echo htmlspecialchars($invoice['invoice_number']); ?>" data-customer-name="<?php echo htmlspecialchars($invoice['customer_name']); ?>">
                    <td><?php echo htmlspecialchars($invoice['invoice_number']); ?></td>
                    <td><?php echo date('m/d', strtotime($invoice['invoice_date'])); ?></td>
                    <td title="<?php echo htmlspecialchars($invoice['customer_name']); ?>"><?php echo htmlspecialchars($invoice['customer_name']); ?></td>
                    <td title="<?php echo htmlspecialchars($invoice['service_address'] ?? ''); ?>"><?php echo htmlspecialchars($invoice['service_address'] ?? ''); ?></td>
                    <td>$<?php echo number_format($invoice['total'], 2, '.', ','); ?></td>
                    <td>
                        <?php
                        $status = '';
                        switch ($invoice['status']) {
                            case 'paid':
                                $status = '<span class="status-paid" title="Paid">P</span>';
                                break;
                            case 'unpaid':
                                $status = '<span class="status-unpaid" title="Unpaid">U</span>';
                                break;
                            case 'partial':
                                $status = '<span class="status-partial" title="Partial">Pa</span>';
                                break;
                            case 'cancelled':
                                $status = '<span class="status-cancelled" title="Cancelled">C</span>';
                                break;
                            default:
                                $status = '<span title="' . htmlspecialchars($invoice['status']) . '">' . htmlspecialchars(substr($invoice['status'], 0, 2)) . '</span>';
                        }
                        echo $status;
                        ?>
                    </td>
                </tr>
                <tr class="action-row" id="actionRow<?php echo $index; ?>">
                    <td colspan="6">
                        <div class="action-buttons">
                            <a href="generate_invoice_pdf.php?id=<?php echo $invoice['id']; ?>" class="btn btn-view" title="View" target="_blank">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="invoice_edit.php?id=<?php echo $invoice['id']; ?>" class="btn btn-edit" title="Edit">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            <a href="invoice_payment.php?id=<?php echo $invoice['id']; ?>" class="btn btn-payment" title="Payment">
                                <i class="fas fa-dollar-sign"></i> Payment
                            </a>
                            <button onclick="shareInvoice(<?php echo $invoice['id']; ?>, '<?php echo htmlspecialchars($invoice['invoice_number']); ?>', '<?php echo htmlspecialchars($invoice['customer_name']); ?>')" class="btn btn-share" title="Share">
                                <i class="fas fa-share-alt"></i> Share
                            </button>
                            <button onclick="confirmDelete(<?php echo $invoice['id']; ?>, '<?php echo htmlspecialchars($invoice['invoice_number']); ?>', '<?php echo htmlspecialchars($invoice['customer_name']); ?>')" class="btn btn-delete" title="Delete">
                                <i class="fas fa-trash-alt"></i> Delete
                            </button>
                        </div>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
        <?php if (empty($invoices)): ?>
            <tbody>
                <tr>
                    <td colspan="6" style="padding: 2rem; text-align: center; color: #6c757d; font-style: italic;">
                        Nenhuma fatura encontrada.
                        <br><br>
                        <button onclick="clearAllFilters()" style="background: #007bff; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; font-size: 0.9rem;">
                            <i class="fas fa-times"></i> Limpar Filtros
                        </button>
                    </td>
                </tr>
            </tbody>
        <?php endif; ?>
    </table>
</div>
