<?php
// Include simple session fix
require_once 'simple_session_fix.php';

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$id = 0;
$name = $email = $phone = $address = $city = $state = $zip = $notes = '';
$error = $success = '';

// Check if ID is provided
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    // Get customer data
    $result = db_query("SELECT * FROM customers WHERE id = $id");

    if ($result && db_num_rows($result) > 0) {
        $customer = db_fetch_assoc($result);

        $name = $customer['name'];
        $email = $customer['email'];
        $phone = $customer['phone'];
        $address = $customer['address'];
        $city = $customer['city'];
        $state = $customer['state'];
        $zip = $customer['zip'];
        $notes = $customer['notes'];
    } else {
        $error = 'Cliente não encontrado.';
    }
} else {
    $error = 'ID do cliente não fornecido.';
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $id = intval($_POST['id']);
    $name = isset($_POST['name']) ? $_POST['name'] : '';
    $email = isset($_POST['email']) ? $_POST['email'] : '';
    $phone = isset($_POST['phone']) ? $_POST['phone'] : '';
    $address = isset($_POST['address']) ? $_POST['address'] : '';
    $city = isset($_POST['city']) ? $_POST['city'] : '';
    $state = isset($_POST['state']) ? $_POST['state'] : '';
    $zip = isset($_POST['zip']) ? $_POST['zip'] : '';
    $notes = isset($_POST['notes']) ? $_POST['notes'] : '';

    // Validate form data
    if (empty($name)) {
        $error = 'O nome do cliente é obrigatório.';
    } else {
        // Escape all input values to prevent SQL injection
        $name = db_escape($name);
        $email = db_escape($email);
        $phone = db_escape($phone);
        $address = db_escape($address);
        $city = db_escape($city);
        $state = db_escape($state);
        $zip = db_escape($zip);
        $notes = db_escape($notes);

        // Create and execute query
        $query = "UPDATE customers SET
                  name = '$name',
                  email = '$email',
                  phone = '$phone',
                  address = '$address',
                  city = '$city',
                  state = '$state',
                  zip = '$zip',
                  notes = '$notes'
                  WHERE id = $id";

        if (db_query($query)) {
            $success = 'Cliente atualizado com sucesso!';
        } else {
            $error = 'Erro ao atualizar cliente: ' . db_error();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Cliente - Tonys AC Repair</title>
    <link rel="stylesheet" href="css/style.css"><link rel="stylesheet" href="css/hamburger-fix.css">
</head>
<body>
    <?php include 'includes/header_responsive.php'; ?>

    <main>
        <div class="container">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">Editar Cliente</h1>
                    <a href="customers.php" class="btn">Voltar para Lista</a>
                </div>
                <div class="card-body">
                    <?php if ($id > 0 && empty($error)): ?>
                        <form method="POST" action="customer_edit.php" class="needs-validation">
                            <input type="hidden" name="id" value="<?php echo $id; ?>">

                            <div class="form-group">
                                <label for="name">Nome *</label>
                                <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>">
                            </div>

                            <div class="form-group">
                                <label for="phone">Telefone</label>
                                <input type="tel" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>">
                            </div>

                            <div class="form-group">
                                <label for="address">Endereço</label>
                                <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($address); ?>">
                            </div>

                            <div class="form-group">
                                <label for="city">Cidade</label>
                                <input type="text" id="city" name="city" value="<?php echo htmlspecialchars($city); ?>">
                            </div>

                            <div class="form-group">
                                <label for="state">Estado</label>
                                <input type="text" id="state" name="state" value="<?php echo htmlspecialchars($state); ?>">
                            </div>

                            <div class="form-group">
                                <label for="zip">CEP</label>
                                <input type="text" id="zip" name="zip" value="<?php echo htmlspecialchars($zip); ?>">
                            </div>

                            <div class="form-group">
                                <label for="notes">Observações</label>
                                <textarea id="notes" name="notes"><?php echo htmlspecialchars($notes); ?></textarea>
                            </div>

                            <button type="submit" class="btn">Atualizar Cliente</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> Tonys AC Repair. Todos os direitos reservados.</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
<?php
// Close database connection
db_close();
?>
